package com.sinitek.cloud.base.support;

import com.sinitek.cloud.common.properties.SinicubeCloudProperties;
import com.sinitek.sirm.application.support.ApplicationSupport;
import com.sinitek.sirm.common.application.constant.ApplicationConstant;
import com.sinitek.spirit.um.server.shiro.IgnoreUrlUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class BaseConfig implements WebMvcConfigurer {

    /**
     * 默认转发的HeaderList
     */
    private static final List<String> DEFAULT_FORWARD_HEADER_LIST = Arrays.asList("accesstoken",
            "api-jwt",
            "client-type",
            ApplicationConstant.APPLICATION_TOKEN_NAME,
            ApplicationConstant.APPLICATION_JWT_NAME
    );

    @Bean
    ClientTokenInterceptor getClientTokenInterceptor(SinicubeCloudProperties sinicubeCloudProperties, ApplicationSupport applicationSupport){
        List<String> forwardHeaderList = sinicubeCloudProperties.getForwardHeaderList();
        if (forwardHeaderList == null || forwardHeaderList.isEmpty()) {
            forwardHeaderList = new ArrayList<>();
        }
        forwardHeaderList.addAll(DEFAULT_FORWARD_HEADER_LIST);
        return new ClientTokenInterceptor(forwardHeaderList, applicationSupport);
    }

    @Bean
    ActionTrackInterceptor getActionTrackInterceptor(SinicubeCloudProperties sinicubeCloudProperties){
        List<String> forwardHeaderList = sinicubeCloudProperties.getForwardHeaderList();
        if (forwardHeaderList == null || forwardHeaderList.isEmpty()) {
            forwardHeaderList = new ArrayList<>();
        }
        forwardHeaderList.addAll(DEFAULT_FORWARD_HEADER_LIST);
        return new ActionTrackInterceptor(forwardHeaderList);
    }

//    @Bean
//    FeignHystrixConcurrencyStrategy getFeignHystrixConcurrencyStrategy(){
//        return new FeignHystrixConcurrencyStrategy();
//    }

    @Resource
    private TokenCheckInterceptor tokenCheckInterceptor;

    @Autowired
    private IgnoreUrlUtil ignoreUrlUtil;

    @Value("${sirm.security.path:/**}")
    private String path;
    @Value("${sirm.security.excludepath:}")
    private String excludepath;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 自定义拦截器，添加拦截路径和排除拦截路径
        List<String> ignoreUrlList = ignoreUrlUtil.getIgnoreUrlList();

        InterceptorRegistration registration = registry.addInterceptor(tokenCheckInterceptor);
        registration.addPathPatterns(StringUtils.split(path, ","));
        registration.excludePathPatterns(ignoreUrlList);

        if(StringUtils.isNotBlank(excludepath)){
            registration.excludePathPatterns(StringUtils.split(excludepath, ","));
        }
    }

}
