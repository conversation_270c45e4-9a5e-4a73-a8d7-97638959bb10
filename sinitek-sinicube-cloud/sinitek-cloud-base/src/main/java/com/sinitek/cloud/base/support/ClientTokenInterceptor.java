package com.sinitek.cloud.base.support;

import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.cloud.common.utils.JWTUtils;
import com.sinitek.sirm.application.support.ApplicationSupport;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Map;

/**
 * 通过feign方式调用其他微服务，设置feign请求的header
 */
@Slf4j
public class ClientTokenInterceptor implements RequestInterceptor {

    @Setter
    @Getter
    private List<String> forwardHeaderList;

    private ApplicationSupport applicationSupport;

    public ClientTokenInterceptor(List<String> forwardHeaderList, ApplicationSupport applicationSupport) {
        this.forwardHeaderList = forwardHeaderList;
        this.applicationSupport = applicationSupport;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {

        try {
            this.supportApplicationOpenApi(requestTemplate);

            String token = null;

            // 获取当前线程的request，传递header，以便后续调用的微服务接口能够获取原始请求中的headers
            Map<String, String> headers = SpringMvcUtil.getRequestHeaders();
            if(MapUtils.isNotEmpty(headers)) {
                headers.forEach((key, value) -> {
                    if (forwardHeaderList.contains(key)) {
                        requestTemplate.header(key, value);
                    }
                });
                token = MapUtils.getString(headers, "api-jwt", "");
            }

            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                String accessToken = HttpUtils.getCookie(attributes.getRequest(), "accesstoken");
                if (StringUtils.isNotBlank(accessToken)) {
                    requestTemplate.header("accesstoken", accessToken);
                }
            }
            // 没有当前request，可能是定时任务或者后端直接发起的请求

            if (StringUtils.isBlank(token)) {
                // 没有api-jwt， 可能不是从网关过来的，是微服务去调其他微服务，需要生成一个

                UserDTO user = new UserDTO();
                user.setOrgid("0");
                user.setOrgname("系统");
                user.setUserid("");
                user.setUsename("");
                token = JWTUtils.createJwt(user);

                log.debug("设置新的的api-jwt[{}]", token);
            }

            requestTemplate.header("api-jwt", token + "");
        } catch (Exception e) {
            log.error("设置feign请求的header失败,{}","无其他核心参数", e);
        }
    }

    /**
     * 支持应用OpenApi接口增加特殊的标识
     * @param requestTemplate
     */
    public void supportApplicationOpenApi(RequestTemplate requestTemplate) {
        boolean isOpenApi = applicationSupport.isOpenApiByCurrentRequest();
        if (!isOpenApi) {
            return;
        }
        Map<String, String> applicationFlagMap = applicationSupport.getApplicationFlagMap();
        for (String key : applicationFlagMap.keySet()) {
            String value = applicationFlagMap.get(key);
            requestTemplate.header(key, value);
        }
    }
}
