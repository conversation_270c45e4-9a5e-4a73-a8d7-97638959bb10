package com.sinitek.cloud.base.support;

import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.cloud.common.utils.JWTUtils;
import com.sinitek.sirm.application.support.ApplicationSupport;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * 拦截请求，验证请求中的api-jwt，确保请求来自微服务中
 */
@Slf4j
@Component
public class TokenCheckInterceptor implements HandlerInterceptor {

    @Value("${sirm.cloud.enable:true}")
    private String inCloud;

    @Autowired
    private ApplicationSupport applicationSupport;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(StringUtils.equalsIgnoreCase(inCloud, Boolean.FALSE.toString())){
            return true;
        }

        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            boolean isOpenApi = applicationSupport.isOpenApiAndInternalIdentification(handlerMethod);
            if (isOpenApi) {
                return true;
            }
        }

        String token = request.getHeader("api-jwt");
        log.debug("微服务调用验证，api-jwt = {}", token);
        if(StringUtils.isNotBlank(token)){
            try {
                UserDTO user = JWTUtils.parseUserByJwt(token);
                if (user != null) {
                    // 维护当前用户信息
                    log.debug("current user = {}", user.toString());
                    CurrentUser.begin();
                    CurrentUser.setRequest(request);
                    CurrentUser.init(user);
                    return true;
                }
            } catch (Exception e){
                log.error("接口: {},api-jwt: {}, 解码失败",SpringMvcUtil.getRequestUri(), token, e);
            }
        } else {
            log.info("接口: {} 中的api-jwt为空", SpringMvcUtil.getRequestUri());
        }

        log.debug("微服务调用验证失败，无效的token");
        HttpUtils.buildErrorResponse(response, HttpStatus.FORBIDDEN, "微服务调用验证失败，身份无效");

        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        log.debug("run postHandle ");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentUser.end();
    }
}
