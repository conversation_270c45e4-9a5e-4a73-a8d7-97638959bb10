package com.sinitek.cloud.base.interceptor;

import com.sinitek.cloud.common.utils.JWTUtils;
import com.sinitek.sirm.application.support.ApplicationSupport;
import com.sinitek.sirm.common.application.constant.ApplicationConstant;
import com.sinitek.sirm.common.application.dto.ApplicationDTO;
import com.sinitek.sirm.common.application.support.CurrentApplicationContext;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 子服务应用身份验证拦截器
 *
 * <AUTHOR>
 * @date 2023/02/28
 */
@Slf4j
public class CloudApplicationInterceptor implements HandlerInterceptor  {

    @Autowired
    private ApplicationSupport applicationSupport;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod)handler;
            boolean isOpenApi = applicationSupport.isOpenApi(handlerMethod);
            if (!isOpenApi) {
                return true;
            }
            String accessTokenJwt = request.getHeader(ApplicationConstant.APPLICATION_JWT_NAME);
            if (StringUtils.isBlank(accessTokenJwt)) {
                log.info("接口: {} 中的{}为空，可能请求未经过网关", SpringMvcUtil.getRequestUri(), ApplicationConstant.APPLICATION_JWT_NAME);
                return fail(response);
            }
            try {
                ApplicationDTO applicationDTO = JWTUtils.parseApplicationByJwt(accessTokenJwt);
                CurrentApplicationContext.begin();
                CurrentApplicationContext.init(applicationDTO);
                return true;
            } catch (Exception e) {
                log.error("接口: {},{}: {}, 解码失败",SpringMvcUtil.getRequestUri(), ApplicationConstant.APPLICATION_JWT_NAME, accessTokenJwt, e);
            }
            return fail(response);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentApplicationContext.end();
    }

    @SneakyThrows
    private boolean fail (HttpServletResponse response) {
        HttpUtils.buildErrorResponse(response, HttpStatus.UNAUTHORIZED, CommonMessageCode.APPLICATION_JWT_UNABLE_TO_PARSE);
        return false;
    }
}
