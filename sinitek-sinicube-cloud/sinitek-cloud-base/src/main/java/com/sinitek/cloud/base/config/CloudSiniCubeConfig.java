package com.sinitek.cloud.base.config;

import com.sinitek.cloud.base.interceptor.CloudApplicationInterceptor;
import com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl;
import com.sinitek.sirm.common.user.bridging.ICurrentUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 注册子服务对 ICurrentUserInfo的实现到Spring
 *
 * sinicube.cloud.type=MICROSERVICE时配置生效,没有配置的情况下默认生效
 *
 * <AUTHOR>
 * @date 2020-1-12
 */
@Slf4j
@ConditionalOnProperty(prefix = "sinicube.cloud", name = "type", havingValue = "MICROSERVICE", matchIfMissing = true)
@Configuration
public class CloudSiniCubeConfig implements WebMvcConfigurer {

    /**
     * 创建子服务中对ICurrentUserInfo的实现到Spring中,如果已经存在则不创建(让项目上可以自行替换)
     *
     * @return
     */
    @Bean
    @ConditionalOnMissingBean
    public ICurrentUserInfo currentUserInfo() {
        CloudServerCurrentUserInfoImpl cloudServerCurrentUserInfo = new CloudServerCurrentUserInfoImpl();
        log.info("当前{}的实现类为: {}", ICurrentUserInfo.class.getName(), cloudServerCurrentUserInfo.getClass().getName());
        return cloudServerCurrentUserInfo;
    }

    @Bean
    @ConditionalOnMissingBean
    public CloudApplicationInterceptor cloudApplicationInterceptor() {
        return new CloudApplicationInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration registration = registry.addInterceptor(cloudApplicationInterceptor());
        registration.addPathPatterns("/**");
    }
}
