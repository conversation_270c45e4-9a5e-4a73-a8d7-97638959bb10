package com.sinitek.cloud.base.user;

import com.sinitek.cloud.base.support.CurrentUser;
import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.user.CurrentUserInfo;
import com.sinitek.sirm.common.user.bridging.ICurrentUserInfo;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.setting.service.ISettingExtService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.Collections;
import java.util.List;

/**
 * 子服务对ICurrentUserInfo的实现
 *
 * <AUTHOR>
 * @date 2020-1-12
 */
@Slf4j
public class CloudServerCurrentUserInfoImpl implements ICurrentUserInfo {

    @Lazy
    @Autowired
    ISettingExtService settingExtService;

    @Override
    public CurrentUserInfo getCurrentUserInfo() {
        UserDTO currentUser = CurrentUser.getCurrentUser();
        if (currentUser == null) {
            return null;
        }
        CurrentUserInfo currentUserInfo = new CurrentUserInfo();
        currentUserInfo.setUserId(currentUser.getUserid());
        currentUserInfo.setName(currentUser.getUsename());
        currentUserInfo.setOrgId(currentUser.getOrgid());
        currentUserInfo.setDisplayName(currentUser.getOrgname());
        currentUserInfo.setTenantId(currentUser.getTenantid());
        currentUserInfo.setLocale(currentUser.getLocale());
        if (StringUtils.isNotBlank(currentUser.getOrgid())) {
            currentUserInfo.setIsAdmin(SpringFactory.getBean(IOrgService.class).isAdmin(currentUser.getOrgid()));
        }

        return currentUserInfo;
    }

    @Override
    public HttpServletRequest getRequest() {
        return CurrentUser.getRequest();
    }

    @Override
    public List<String> getAuthTenantIdList(String resource, String operation) {
        log.error("调用子服务的 getAuthTenantIdList方法,当前为空实现。resource: {}, operation: {}", resource, operation);
        return Collections.emptyList();
    }

    @Override
    public void initCurrentUserInfo(CurrentUserInfo userInfo) {
        if (userInfo != null) {
            CurrentUser.begin();

            UserDTO user = new UserDTO();
            user.setUserid(userInfo.getUserId());
            user.setUsename(userInfo.getName());
            user.setOrgid(userInfo.getOrgId());
            user.setOrgname(userInfo.getDisplayName());
            user.setLocale(userInfo.getLocale());
            user.setTenantid(userInfo.getTenantId());
            CurrentUser.init(user);
        }
    }

    @Override
    public void cleanCurrentUserInfo() {
        CurrentUser.end();
    }
}
