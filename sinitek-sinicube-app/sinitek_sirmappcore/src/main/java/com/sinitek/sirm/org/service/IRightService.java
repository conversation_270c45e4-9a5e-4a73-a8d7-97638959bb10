package com.sinitek.sirm.org.service;

import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.dto.OrgObjectInfoDTO;
import com.sinitek.sirm.org.entity.OrgAuthSnapDetail;
import com.sinitek.spirit.right.server.dto.AuthorityDTO;
import com.sinitek.spirit.right.server.entity.RightAuth;

import java.util.List;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: indicator
 * Author:      潘虹
 * History:     11-5-13 created by 潘虹
 */
public interface IRightService {


    /**
     * 检查员工是否在指定的组织结构内
     *
     * @param userid 员工编号
     * @param orgstr 字符串
     * @return true：在组织内，false：不在
     * @throws com.sinitek.spirit.um.NoSuchUserException 该用户不存在
     */
    public boolean checkRight(int userid, String orgstr);

    /**
     * 添加授权权限--------------------------
     *
     * @param orgId         组织结构对象
     * @param rightDefineKey 权限模块定义(SPRT_RIGHTDEF)
     * @param rejectFlag     是否禁止
     * @param rightTypes     权限类型
     */
    public void addRightAuth(String orgId, String rightDefineKey, boolean rejectFlag, String[] rightTypes);

    /**
     * 添加授权权限
     *
     * @param orgId          组织结构对象编号
     * @param rightObjId     授权对象
     * @param rightDefineKey 权限模块定义(SPRT_RIGHTDEF)
     * @param rejectFlag     是否禁止
     * @param rightTypes     权限
     */
    public void addRightAuth(String orgId, String rightObjId, String rightDefineKey, boolean rejectFlag, String[] rightTypes);

    /**
     * 删除授权对象的授权数据（给别人授权）
     *
     * @param orgObj         授权组织结构对象
     * @param rightDefineKey 授权定义key值:权限模块定义(SPRT_RIGHTDEF)
     * @param rightTypes     授权类型------------？
     */
    public void deleteRightAuth(OrgObjectDTO orgObj, String rightDefineKey, String[] rightTypes);

    /**
     * 删除授权对象授权数据
     *
     * @param orgObj         组织结构对象
     * @param objectKey      授权对象关键值-------------？
     * @param rightDefineKey 权限模块定义(SPRT_RIGHTDEF)
     * @param rightTypes     授权类型 ----------？
     */
    public void deleteRightAuth(OrgObjectDTO orgObj, String objectKey, String rightDefineKey, String[] rightTypes);

    /**
     * 删除授权对象授权数据列表
     *
     * @param orgObj         组织结构对象
     * @param objectKeyList  授权对象关键值-------------？
     * @param rightDefineKey 权限模块定义(SPRT_RIGHTDEF)
     * @param rightTypes     授权类型 ----------？
     */
    public void deleteRightAuthList(OrgObjectDTO orgObj, List<String> objectKeyList, String rightDefineKey, String[] rightTypes);

    /**
     * 删除授权对象授权数据
     *
     * @param objectKey      授权对象关键值
     * @param rightDefineKey 权限模块定义(SPRT_RIGHTDEF)
     * @param rightTypes     授权类型
     */
    public void deleteRightAuth(String objectKey, String rightDefineKey, String[] rightTypes);

    /**
     * 删除授权对象授权数据
     *
     * @param rightDefineKey 权限模块定义(SPRT_RIGHTDEF)
     * @param rightTypes     授权类型
     */
    public void deleteRightAuth(String rightDefineKey, String[] rightTypes);

    /**
     * 检查组织结构对象是否具有权限
     *
     * @param orgid          组织结构对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    public boolean checkRight(String orgid, String rightDefineKey, String rightType);

    /**S
     * 验证该ORG对象自己是否有这个权限
     *
     * @param orgid
     * @param rightObjId
     * @param rightDefineKey
     * @param rightType
     * @return
     */
    public boolean checkOrgRight(String orgid, String rightObjId, String rightDefineKey, String rightType);

    /**
     * 检查组织结构对象是否具有权限
     *
     * @param orgid          组织结构对象id
     * @param rightObjId     权限对象Id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    public boolean checkRight(String orgid, String rightObjId, String rightDefineKey, String rightType);

    /**
     * 获取授权对象的组织结构对象集合
     *
     * @param rightObjId     对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    @Deprecated
    public List<OrgObjectDTO> findAuthOrgObjects(String rightObjId, String rightDefineKey, String rightType);

    /**
     * 获取授权对象的组织结构对象集合
     *
     * @param rightObjId
     * @param rightDefineKey
     * @param rightType
     * @return
     */
    public List<String> findAuthOrgIdList(String rightObjId, String rightDefineKey, String rightType);


    /**
     * 查询授权对应组织机构对象集合
     *
     * @param rightObjId     权限对象id
     * @param rightDefineKey 权限对象Entityname
     * @param rightType      权限访问类型
     * @return
     */
    public List<OrgObjectDTO> findEnableAuthOrgObjects(String rightObjId, String rightDefineKey, String rightType);

    /**
     * 读取缓存中权限列表
     *
     * @param orgObj         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    public List<String> findAuthedObjects(String orgObj, String rightDefineKey, String rightType);

    /**
     * 读取缓存中权限列表
     *
     * @param orgObj         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    public List<String> findAuthedObjects(String orgObj, String rightDefineKey, String[] rightType);

    /**
     * 读取缓存中权限列表
     *
     * @param orgId         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    public List<RightAuth> findEnableAuthedObjects(String orgId, String rightDefineKey, String rightType, List<RightAuth> outlist);

    /**
     * 读取权限快照中的权限列表
     *
     * @param orgAuthSnapDetails
     * @param snapId
     * @param orgId
     * @param rightDefineKey
     * @param rightType
     * @return
     **/
    List<OrgAuthSnapDetail> findEnableAuthedSnapObjects(List<OrgAuthSnapDetail> orgAuthSnapDetails, Long snapId, String orgId, String rightDefineKey, String rightType, List<OrgAuthSnapDetail> outlist);

    /**
     * 读取缓存中权限列表
     *
     * @param orgId         对象id
     * @param objectKey     权限对象值
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightTypes      权限
     * @return
     */
    public List<RightAuth> findAuthedObjects(String orgId, String objectKey, String rightDefineKey, String[] rightTypes, List<RightAuth> outlist, boolean excludereject);


    /**
     * 读取权限列表
     *
     * @param orgObj         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @param outlist
     * @param excludereject  是否排除拒绝权限
     * @return
     */
    public List<RightAuth> findAuthedObjects(String orgObj, String rightDefineKey, String rightType, List<RightAuth> outlist, boolean excludereject);

    /**
     * 只获取组织结构自身的权限列表
     *
     * @param orgId
     * @param rightDefineKey
     * @param rightType
     * @return
     */
    public List<RightAuth> findSelfAuthList(String orgId, String rightDefineKey, String rightType);

    /**
     * 合并所有权限
     * @param orgId
     * @param rightType
     * @param rightDefineKey
     */
    public void recoverOrgAuth1(String orgId, String rightType, String rightDefineKey);

    /**
     * 查询所有的权限对象
     */
    public List<AuthorityDTO> findRightAuth(String rightDefineKey, String menuId);

    /**
     * 查询所有的权限对象
     */
    public List<AuthorityDTO> findRightAuth(String rightDefineKey, String menuId, String orgId);

    /**
     * 查询所有的权限对象
     */
    public List<AuthorityDTO> findRightAuth(String rightDefineKey, String menuId, String orgId, String rightType);

    /**
     * 根据授权对象获取所有组织结构对象
     *
     * @param rightObjId
     * @param rightDefineKey
     * @param rightType
     * @return
     */
    public List<OrgObjectInfoDTO> findAllAuthOrgObjects(String rightObjId, String rightDefineKey, String rightType);

    /**
     * 根据授权对象和授权菜单获取权限信息
     *
     * @param rightDefineKey
     * @param orgId
     * @param objectKey
     * @param rightType
     * @return
     */
    public RightAuth getRightAuth(String rightDefineKey, String orgId, String objectKey, String rightType);

    /**
     * 批量新增授权信息
     *
     * @param rightAuthList
     * @return
     */
    public void saveRightAuthList(List<RightAuth> rightAuthList);

    /**
     * 批量删除授权信息(根据id)
     *
     * @param rightAuthList
     * @return
     */
    public void deleteRightAuthList(List<RightAuth> rightAuthList);

    /**
     * 批量删除授权信息(任意条件)
     *
     * @param rightAuthList
     * @return
     */
    void deleteRightAuthListByCondition(List<RightAuth> rightAuthList);

    /**
     * 根据orgid得到权限信息
     *
     * @param orgIds
     * @return
     */
    public List<AuthorityDTO> findAuthOrg(String orgIds, String rightDefineKey, String rightType);

    /**
     * 根据orgid得到权限信息
     *
     * @param orgIds
     * @return
     */
    public List<AuthorityDTO> findAuthOrg(String[] orgIds, String rightDefineKey, String rightType);

    /**
     * 新增/修改 RightAuth
     * @param rightAuth
     */
    public void saveOrUpdateRightAuth(RightAuth rightAuth);

    /**
     * 根据objId查询RightAuth
     * @param objIds
     */
    public List<RightAuth> findRightAuthList(List<Long> objIds);

    /**
     * 获取组织结构自身的多种分类权限列表
     * @param orgId
     * @param rightDefineKeyList
     * @return
     */
    public List<RightAuth> findSelfAuthListByRightDefineKeyList(String orgId, List<String> rightDefineKeyList);

    /**
     * 加载机构默认角色 拥有单功能点权限的 角色列表
     * @param rightDefineKey
     * @param objectKey
     * @return
     */
    List<RightAuth> findDefaultRoleRightAuthList(String rightDefineKey, String objectKey);

    /**
     * 根据条件删除 权限
     * @param authOrgId
     * @param recoverObjId
     * @param rightDefineKey
     * @param rightTypes
     */
    void deleteRightAuthList(String authOrgId,String recoverObjId,String rightDefineKey, List<String> rightTypes);

    /**
     * 获取自身、上级节点继承，的权限类型
     * @param orgId 组织结构id
     * @param rightDefineKey 权限种类
     * @param rightType 权限控制类型
     * @param type<br/>      1=仅自身
     *                       2=仅上级节点
     *                       3=自身+上级节点
     * @return
     */
    public List<RightAuth> findAllAuthList(String orgId, String rightDefineKey, String[] rightType, int type, boolean excludereject);

    /**
     *
     * 查询系统所有权限授权信息
     *
     * @return
     **/
    public List<RightAuth> findAllOrgAuthList();

}
