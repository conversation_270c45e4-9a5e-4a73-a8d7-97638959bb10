package com.sinitek.sirm.org.entity;

import com.sinitek.sirm.common.utils.StringUtil;
import com.sinitek.sirm.org.enumerate.EmployeeChangeEventFlagEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;

import java.beans.PropertyDescriptor;
import java.util.*;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: indicator
 * Author:      潘虹
 * History:     11-4-29 created by 潘虹
 */
@ToString
@Slf4j
public class Employee implements java.io.Serializable {
    @Schema(description = "id")
    private String id = null;
    /**
     * 登录名
     */
    @Schema(description = "登录名")
    private String userName;
    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    private String password;
    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;
    /**
     * 员工名称
     */
    @Schema(description = "员工名称")
    private String empName;
    /**
     * 在职状态
     */
    @Schema(description = "在职状态")
    private int inservice = 0;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 性别
     */
    @Schema(description = "性别")
    private int sex;

    /**
     * 办公电话
     */
    @Schema(description = "办公电话")
    private String tel;

    /**
     * 职位
     */
    @Schema(description = "职位")
    private String job;

    /**
     * 手机
     */
    @Schema(description = "手机")
    private String mobilePhone;

    /**
     * 岗位编号
     */
    @Schema(description = "岗位编号")
    private String postid;

    /**
     * 从业资格编号
     */
    @Schema(description = "从业资格编号")
    private String qualifyno;

    /**
     * 从业资格类型
     */
    @Schema(description = "从业资格类型")
    private int qualifytype;
    /**
     * 办公电话2
     */
    @Schema(description = "办公电话2")
    private String tel2;
    /**
     * 家庭电话1
     */
    @Schema(description = "家庭电话1")
    private String familytelephone;
    /**
     * 手机电话2
     */
    @Schema(description = "手机电话2")
    private String mobilephone2;
    /**
     * 家庭电话2
     */
    @Schema(description = "家庭电话2")
    private String familytelephone2;
    /**
     * 其他电话1
     */
    @Schema(description = "其他电话1")
    private String otherphone;
    /**
     * 其他电话2
     */
    @Schema(description = "其他电话2")
    private String otherphone2;
    /**
     * BP机
     */
    @Schema(description = "BP机")
    private String bp;
    /**
     * 办公室
     */
    @Schema(description = "办公室")
    private String office;
    /**
     * 公司传真1
     */
    @Schema(description = "公司传真1")
    private String fax;
    /**
     * 公司传真2
     */
    @Schema(description = "公司传真2")
    private String fax2;
    /**
     * 家庭传真1
     */
    @Schema(description = "家庭传真1")
    private String familyfax;
    /**
     * 家庭传真2
     */
    @Schema(description = "家庭传真2")
    private String familyfax2;
    /**
     * 公司地址
     */
    @Schema(description = "公司地址")
    private String companyaddress;
    /**
     * 家庭地址
     */
    @Schema(description = "家庭地址")
    private String familyaddress;
    /**
     * 公司邮编
     */
    @Schema(description = "公司邮编")
    private String companyzip;
    /**
     * 家庭邮编
     */
    @Schema(description = "家庭邮编")
    private String familyzip;
    /**
     * 其他地址
     */
    @Schema(description = "其他地址")
    private String otheraddress;
    /**
     * 其他邮编
     */
    @Schema(description = "其他邮编")
    private String otherzip;
    /**
     * 其他Email 1
     */
    @Schema(description = "其他Email 1")
    private String email1;
    /**
     * 其他Email 2
     */
    @Schema(description = "其他Email 2")
    private String email2;
    /**
     * 主页
     */
    @Schema(description = "主页")
    private String homepage;
    /**
     * QQ
     */
    @Schema(description = "QQ")
    private String qq;
    /**
     * MSN
     */
    @Schema(description = "MSN")
    private String msn;
    /**
     * 工作地
     */
    @Schema(description = "工作地")
    private String where;
    /**
     * 通讯录
     */
    @Schema(description = "通讯录")
    private String addressbook;
    /**
     * 个人简介
     */
    @Schema(description = "个人简介")
    private String introduction;

    /**
     * 密码修改时间
     */
    @Schema(description = "密码修改时间")
    private String passwordUpdateTime;

    /**
     * 用户锁定时间
     */
    @Schema(description = "用户锁定时间")
    private String userLockTime;

    /**
     * 用户属性
     */
    @Schema(description = "用户属性")
    private transient Map<String, String> otherProperties = null;

    /**
     * 员工姓名拼音
     */
    @Schema(description = "员工姓名拼音")
    private String namePy;

    /**
     * 员工姓名简拼
     */
    @Schema(description = "员工姓名简拼，首字母拼音")
    private String simpleNamePy;

    /**
     * 入职日期
     */
    @Schema(description = "入职日期")
    private String rzrq;
    /**
     * 离职日期
     */
    @Schema(description = "离职日期")
    private String lzrq;

    /**
     * 员工英文名称
     */
    @Schema(description = "员工英文名称")
    private String englishName;

    /**
     * 个人英文简介
     */
    @Schema(description = "个人英文简介")
    private String englishIntroduction;

    /**
     * 远程系统中的用户的唯一标识
     */
    @Schema(description = "远程系统中的用户的唯一标识")
    private String origId;

    @Schema(description = "数据源")
    private String dataSrc;

    /**
     * 数据所属机构
     */
    @Schema(description = "数据所属机构")
    private String tenantId;

    /**
     * 到期时间
     */
    @Schema(description = "到期日期")
    private Date expireTime;

    /**
     * 用户锁定标记
     */
    @Schema(description = "用户锁定标记")
    private String lockFlag;

    /**
     * 用户信息更新时间
     */
    @Schema(description = "用户信息更新时间")
    private Date updateTimeStamp;

    /**
     * 用户信息创建时间
     */
    private Date createTimeStamp;

    /**
     * 用户密码的盐
     */
    private String salt;

    /**
     * 用户修改事件标记，区分新增和更新
     */
    private EmployeeChangeEventFlagEnum eventFlag;

    private static final String ERROR_MSG = "message";
    private static final String ORGID = "orgid";
    private static final String DISPLAYNAME = "displayname";
    private static final String INSERVICE_STR = "inservice";

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getEnglishIntroduction() {
        return englishIntroduction;
    }

    public void setEnglishIntroduction(String englishIntroduction) {
        this.englishIntroduction = englishIntroduction;
    }

    public String getOtherProperty(String propertyname) {
        return this.loadOtherProperties().get(propertyname);
    }

    public String getOtherProperty(String propertyname, String dv) {
        return StringUtil.safeToString(this.loadOtherProperties().get(propertyname), dv);
    }

    public void setOtherProperty(String propertyname, String propertyvalue) {
        this.loadOtherProperties().put(propertyname, propertyvalue);
    }

    public void setOtherProperties(Map<String, String> otherProperties) {
        this.otherProperties = otherProperties;
    }

    /**
     * 加载用户的其他属性
     *
     * @return 用户的其他属性otherProperties
     */
    @Deprecated
    public Map<String, String> loadOtherProperties() {
        if (this.otherProperties == null) {
            Map<String, String> properties = new HashMap<>();
            if (StringUtils.isNotBlank(this.getUserId())) {
                try {
                    Map<String, PropertyDescriptor> mapping = getPropertiesMapping();
                    for (String name : mapping.keySet()) {
                        properties.remove(name);
                    }
                } catch (Exception e) {
                    log.error("加载用户的其他属性错误，getUserId:{}",this.getUserId(), e);
                }
            }
            this.otherProperties = properties;
        }
        return this.otherProperties;
    }

    /**
     * 用于保存和修改时使用
     *
     * @return
     */
    public Map<String, String> getAllUserProperties() {
        Map<String, String> param = new HashMap<>();
        param.putAll(this.loadOtherProperties());
        Map<String, PropertyDescriptor> mapping = getPropertiesMapping();
        Set<Map.Entry<String, PropertyDescriptor>> entrySet = mapping.entrySet();
        Iterator<Map.Entry<String, PropertyDescriptor>> iterator = entrySet.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, PropertyDescriptor> item = iterator.next();
            PropertyDescriptor pd = item.getValue();
            String value = "";
            if (pd != null) {
                try {
                    value = StringUtil.safeToString(pd.getReadMethod().invoke(this), "");
                } catch (Exception e) {
                    log.error("获得用于读取属性值的方法错误: PropertyDescriptor : {}",pd, e);
                }
                if (value == null || "null".equals(value)) {
                    value = "";
                }
            }
            param.put(item.getKey(), value);
        }

        return param;
    }

    /**
     * 从Map中实例化数据
     *
     * @param data
     * @return
     */
    public static Employee instanceFromMap(Map<String, Object> data) {
        Employee result = new Employee();
        if (data == null) {
            return result;
        }
        Map<String, PropertyDescriptor> pdMapping = getPropertiesMapping();
        for (Iterator<String> it = data.keySet().iterator(); it.hasNext(); ) {
            String name = it.next();
            Object value = data.get(name);
            PropertyDescriptor pd = pdMapping.get(name);
            if (pd != null) {
                try {
                    BeanUtils.setProperty(result, pd.getName(), value == null ? "" : value);
                } catch (Exception ex) {
                    log.error("设置Employee属性 {} 错误: 值 : {}",pd.getName(),value, ex);
                }
            } else if (value != null) {
                result.setOtherProperty(name, String.valueOf(value));
            }
        }

        return result;
    }

    /**
     * 获得Employee的属性描述器
     *
     * @return
     */
    private static Map<String, PropertyDescriptor> getEmployeePropertyDescriptors() {
        Map<String, PropertyDescriptor> pdMapping = new CaseInsensitiveMap();
        PropertyDescriptor[] pds = PropertyUtils.getPropertyDescriptors(Employee.class);
        if (pds != null) {
            for (PropertyDescriptor pd : pds) {
                pdMapping.put(pd.getName(), pd);
            }
        }
        return pdMapping;
    }

    /**
     * 添加保存到um_userproperty表中的字段
     *
     * @param
     */
    public void addUserOtherProperties() {
        this.setOtherProperty(ORGID, StringUtil.safeToString(this.id, ""));
        this.setOtherProperty(DISPLAYNAME, StringUtil.safeToString(this.empName, ""));
        this.setOtherProperty(INSERVICE_STR, StringUtil.safeToString(this.inservice, ""));
    }

    /**
     * 将Employee属性封装到map中
     *
     * @return
     */
    private static Map<String, PropertyDescriptor> getPropertiesMapping() {
        Map<String, PropertyDescriptor> pdMapping = new CaseInsensitiveMap(getEmployeePropertyDescriptors());

        Map<String, PropertyDescriptor> param = new CaseInsensitiveMap();
        param.put(ORGID, pdMapping.get("id"));
        param.put("objid", pdMapping.get("id"));
        param.put(DISPLAYNAME, pdMapping.get("empName"));
        param.put("sex", pdMapping.get("sex"));
        param.put("mobilePhone", pdMapping.get("mobilePhone"));
        param.put("job", pdMapping.get("job"));
        param.put("email", pdMapping.get("email"));
        param.put("tel", pdMapping.get("tel"));
        param.put("qualifyno", pdMapping.get("qualifyno"));
        param.put("qualifytype", pdMapping.get("qualifytype"));
        param.put(INSERVICE_STR, pdMapping.get(INSERVICE_STR));
        param.put("userId", pdMapping.get("userId"));
        param.put("userName", pdMapping.get("userName"));
        param.put("password", pdMapping.get("password"));
        //2012-01-17增加
        param.put("tel2", pdMapping.get("tel2"));
        param.put("familytelephone", pdMapping.get("familytelephone"));
        param.put("mobilephone2", pdMapping.get("mobilephone2"));
        param.put("familytelephone2", pdMapping.get("familytelephone2"));
        param.put("otherphone", pdMapping.get("otherphone"));
        param.put("otherphone2", pdMapping.get("otherphone2"));
        param.put("bp", pdMapping.get("bp"));
        param.put("office", pdMapping.get("office"));
        param.put("fax", pdMapping.get("fax"));
        param.put("fax2", pdMapping.get("fax2"));
        param.put("familyfax", pdMapping.get("familyfax"));
        param.put("familyfax2", pdMapping.get("familyfax2"));
        param.put("companyaddress", pdMapping.get("companyaddress"));
        param.put("familyaddress", pdMapping.get("familyaddress"));
        param.put("companyzip", pdMapping.get("companyzip"));
        param.put("familyzip", pdMapping.get("familyzip"));
        param.put("otheraddress", pdMapping.get("otheraddress"));
        param.put("otherzip", pdMapping.get("otherzip"));
        param.put("email1", pdMapping.get("email1"));
        param.put("email2", pdMapping.get("email2"));
        param.put("homepage", pdMapping.get("homepage"));
        param.put("qq", pdMapping.get("qq"));
        param.put("msn", pdMapping.get("msn"));
        param.put("where1", pdMapping.get("where"));
        param.put("addressbook", pdMapping.get("addressbook"));
        param.put("introduction", pdMapping.get("introduction"));
        param.put("passwordUpdateTime", pdMapping.get("passwordUpdateTime"));
        param.put("userLockTime", pdMapping.get("userLockTime"));

        //2015-01-22新增
        param.put("namepy", pdMapping.get("namePy"));
        param.put("rzrq", pdMapping.get("rzrq"));
        param.put("lzrq", pdMapping.get("lzrq"));

        //2018-05-07新增
        param.put("englishname", pdMapping.get("englishName"));
        param.put("englishintroduction", pdMapping.get("englishIntroduction"));
        // 2022/8/4新增
        param.put("lockflag", pdMapping.get("lockflag"));

        // 2022/10/09新增
        param.put("updateTimeStamp", pdMapping.get("updateTimeStamp"));
        // 2023/5/26新增
        param.put("eventFlag", pdMapping.get("eventFlag"));
        return param;
    }


    public String getTel2() {
        return tel2;
    }

    public void setTel2(String tel2) {
        this.tel2 = tel2;
    }

    public String getFamilytelephone() {
        return familytelephone;
    }

    public void setFamilytelephone(String familytelephone) {
        this.familytelephone = familytelephone;
    }

    public String getMobilephone2() {
        return mobilephone2;
    }

    public void setMobilephone2(String mobilephone2) {
        this.mobilephone2 = mobilephone2;
    }

    public String getFamilytelephone2() {
        return familytelephone2;
    }

    public void setFamilytelephone2(String familytelephone2) {
        this.familytelephone2 = familytelephone2;
    }

    public String getOtherphone() {
        return otherphone;
    }

    public void setOtherphone(String otherphone) {
        this.otherphone = otherphone;
    }

    public String getOtherphone2() {

        return otherphone2;
    }

    public void setOtherphone2(String otherphone2) {
        this.otherphone2 = otherphone2;
    }

    public String getBp() {
        return bp;
    }

    public void setBp(String bp) {
        this.bp = bp;
    }

    public String getNamePy() {
        return namePy;
    }

    public void setNamePy(String namePy) {
        this.namePy = namePy;
    }

    public String getOffice() {
        return office;
    }

    public void setOffice(String office) {
        this.office = office;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getFax2() {
        return fax2;
    }

    public void setFax2(String fax2) {
        this.fax2 = fax2;
    }

    public String getFamilyfax() {
        return familyfax;
    }

    public void setFamilyfax(String familyfax) {
        this.familyfax = familyfax;
    }

    public String getFamilyfax2() {
        return familyfax2;
    }

    public void setFamilyfax2(String familyfax2) {
        this.familyfax2 = familyfax2;
    }

    public String getCompanyaddress() {
        return companyaddress;
    }

    public void setCompanyaddress(String companyaddress) {
        this.companyaddress = companyaddress;
    }

    public String getFamilyaddress() {
        return familyaddress;
    }

    public void setFamilyaddress(String familyaddress) {
        this.familyaddress = familyaddress;
    }

    public String getCompanyzip() {
        return companyzip;
    }

    public void setCompanyzip(String companyzip) {
        this.companyzip = companyzip;
    }

    public String getFamilyzip() {
        return familyzip;
    }

    public void setFamilyzip(String familyzip) {
        this.familyzip = familyzip;
    }

    public String getOtheraddress() {
        return otheraddress;
    }

    public void setOtheraddress(String otheraddress) {
        this.otheraddress = otheraddress;
    }

    public String getOtherzip() {
        return otherzip;
    }

    public void setOtherzip(String otherzip) {
        this.otherzip = otherzip;
    }

    public String getEmail1() {
        return email1;
    }

    public void setEmail1(String email1) {
        this.email1 = email1;
    }

    public String getEmail2() {
        return email2;
    }

    public void setEmail2(String email2) {
        this.email2 = email2;
    }

    public String getHomepage() {
        return homepage;
    }

    public void setHomepage(String homepage) {
        this.homepage = homepage;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getMsn() {
        return msn;
    }

    public void setMsn(String msn) {
        this.msn = msn;
    }

    public String getWhere() {
        return where;
    }

    public void setWhere(String where) {
        this.where = where;
    }

    public String getAddressbook() {
        return addressbook;
    }

    public void setAddressbook(String addressbook) {
        this.addressbook = addressbook;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public int getInservice() {
        return inservice;
    }

    public void setInservice(int inservice) {
        this.inservice = inservice;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getPostid() {
        return postid;
    }

    public void setPostid(String postid) {
        this.postid = postid;
    }

    public String getQualifyno() {
        return qualifyno;
    }

    public void setQualifyno(String qualifyno) {
        this.qualifyno = qualifyno;
    }

    public int getQualifytype() {
        return qualifytype;
    }

    public void setQualifytype(int qualifytype) {
        this.qualifytype = qualifytype;
    }

    public String getPasswordUpdateTime() {
        return passwordUpdateTime;
    }

    public void setPasswordUpdateTime(String passwordUpdateTime) {
        this.passwordUpdateTime = passwordUpdateTime;
    }

    public String getUserLockTime() {
        return userLockTime;
    }

    public String getRzrq() {
        return rzrq;
    }

    public void setRzrq(String rzrq) {
        this.rzrq = rzrq;
    }

    public String getLzrq() {
        return lzrq;
    }

    public void setLzrq(String lzrq) {
        this.lzrq = lzrq;
    }

    public void setUserLockTime(String userLockTime) {
        this.userLockTime = userLockTime;
    }

    public String getOrigId() {
        return origId;
    }

    public void setOrigId(String origId) {
        this.origId = origId;
    }

    public String getDataSrc() {
        return dataSrc;
    }

    public void setDataSrc(String dataSrc) {
        this.dataSrc = dataSrc;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getLockFlag() {
        return lockFlag;
    }

    public void setLockFlag(String lockFlag) {
        this.lockFlag = lockFlag;
    }

    public Date getUpdateTimeStamp() {
        return updateTimeStamp;
    }

    public void setUpdateTimeStamp(Date updateTimeStamp) {
        this.updateTimeStamp = updateTimeStamp;
    }

    public EmployeeChangeEventFlagEnum getEventFlag() {
        return eventFlag;
    }

    public void setEventFlag(EmployeeChangeEventFlagEnum eventFlag) {
        this.eventFlag = eventFlag;
    }

    public String getSimpleNamePy() {
        return simpleNamePy;
    }

    public void setSimpleNamePy(String simpleNamePy) {
        this.simpleNamePy = simpleNamePy;
    }

    public Date getCreateTimeStamp() {
        return createTimeStamp;
    }

    public void setCreateTimeStamp(Date createTimeStamp) {
        this.createTimeStamp = createTimeStamp;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }
}
