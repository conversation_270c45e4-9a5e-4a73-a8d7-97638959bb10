package com.sinitek.sirm.common.message.template.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/6/16 11:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageReceiverTemplateDTO {
    /**
     * 员工id
     */
    private String empId;

    /**
     * 员工登录名
     */
    private String userName;

    /**
     * 员工登录名
     */
    private String empName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 收件人类型, 'to':邮件收件人，'cc':邮件抄送人
     */
    private String receiveType;

}
