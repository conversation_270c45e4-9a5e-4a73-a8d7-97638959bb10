package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 候选人选中查询 - DTO
 *
 * <AUTHOR>
 * date 2024-03-01
 */
@Data
@Schema(description = "候选人选中查询DTO")
@EqualsAndHashCode(callSuper = true)
public class CandidateTransDTO extends UserSelectionHistoryRecordDTO {

    @Schema(description = "组织结构ID列表")
    private List<OrgIdTypeDTO> orgIdTypeDTOList;
}
