package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表格视图字段-保存DTO
 *
 * <AUTHOR>
 * date 2024-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图字段-保存DTO")
public class TableViewColumnSaveDTO extends TableViewColumnBaseDTO {

    @Schema(description = "主键")
    private Long id;
}
