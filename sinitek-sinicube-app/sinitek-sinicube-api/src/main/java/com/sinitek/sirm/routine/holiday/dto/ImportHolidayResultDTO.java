package com.sinitek.sirm.routine.holiday.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/26
 */
@Data
@NoArgsConstructor
@Schema(description = "返回给前端的结果类")
public class ImportHolidayResultDTO {

    @Schema(description= "节假日方案Code")
    private String schemeCode;

    @Schema(description = "消息提示")
    private Map<String,String> message;
}
