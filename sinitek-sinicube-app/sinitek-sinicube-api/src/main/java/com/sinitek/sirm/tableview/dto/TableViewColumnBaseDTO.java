package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.tableview.message.TableViewColumnMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * 表格视图字段 基础DTO
 *
 * <AUTHOR>
 * date 2024-01-03
 */
@Data
@Schema(description = "表格视图字段-基础DTO")
public class TableViewColumnBaseDTO {

    @Schema(description = "所属视图Id")
    private Long viewId;

    @Schema(description = "字段名", required = true)
    @NotBlank(message = "{"+ TableViewColumnMessage.NAME_CAN_NOT_NULL +"}")
    @Length(max = 60,message = "{"+ TableViewColumnMessage.NAME_CAN_NOT_EXCEED +"}")
    private String name;

    @Schema(description = "排序字段，由前端排序")
    private String sort;

    @Schema(description = "是否展示，0为不展示、1为展示")
    private Integer showFlag;

    @Schema(description = "子表格列")
    private List<TableViewColumnBaseDTO> children;
}
