package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.tableview.message.TableViewMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 表格视图-移动DTO
 *
 * <AUTHOR>
 * date 2023-07-14
 */
@Data
@Schema(description = "表格视图-移动DTO")
public class TableViewMoveDTO {

    @Schema(description = "表格唯一标识")
    @NotBlank(message = "{"+ TableViewMessage.TABLE_ID_CAN_NOT_NULL +"}")
    private String code;

    @Schema(description = "展示的Id列表,id的顺序即排序")
    private List<Long> showIdList;

    @Schema(description = "隐藏的Id列表,id的顺序即排序")
    private List<Long> hideIdList;

}
