package com.sinitek.sirm.calendar.dto;

import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/3/14
 */
@Data
@Schema(description = "日程同步资源dto")
public class CalendarEventSyncSourceDTO {

    @Schema(description = "日程id")
    private Long id;

    @Schema(description = "软件保存日程id")
    private String scheduleId;

    @Schema(description = "所属员工id")
    private String empId;

    @Schema(description = "日程标题")
    private String title;

    @Schema(description = "日程开始时间")
    private Date startTime;

    @Schema(description = "日程结束时间")
    private Date endTime;

    @Schema(description = "日程是否提醒")
    private Integer remindFlag;

    @Schema(description = "日程提醒时间")
    private Integer[] remindTime;

    @Schema(description = "日程内容")
    private String content;

    @Schema(description = "是否重复")
    private Integer repeatFlag;

    @Schema(description = "重复实体dto")
    private RepeatTimeDTO repeatTimeDTO;
}
