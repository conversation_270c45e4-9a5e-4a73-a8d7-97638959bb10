package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 接近到期用户信息
 *
 * <AUTHOR>
 * @date 2021-07-27
 */
@Data
@Schema(description = "接近到期用户信息")
public class ExpireUserDTO {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "登陆名")
    private String userName;

    @Schema(description = "用户组织结构Id")
    private String orgId;

    @Schema(description = "员工姓名")
    private String orgName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String mobilePhone;

    @Schema(description = "所属机构Id")
    private String tenantId;

    @Schema(description = "机构名称")
    private String tenantName;

    @Schema(description = "到期时间")
    private Date expireTime;
}
