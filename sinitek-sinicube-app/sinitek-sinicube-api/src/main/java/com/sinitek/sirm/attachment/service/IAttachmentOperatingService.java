package com.sinitek.sirm.attachment.service;

import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;

import java.io.FileNotFoundException;

/**
 * 封装附件操作的 Service
 *
 * <AUTHOR>
 * @date 2021-02-02
 */
public interface IAttachmentOperatingService {

    /**
     * 根据UploadFile获取到主服务中对应文件的流
     *
     * @param uploadFile 文件上传
     * @param response   响应
     * @throws FileNotFoundException    文件找不到异常
     * @return
     * 获取到主服务中对应文件的流
     */
    ResponseEntity<byte[]> getInputStreamByUploadFile(UploadFileDTO uploadFile, HttpServletResponse response);

}
