package com.sinitek.sirm.tableview.service;

import com.sinitek.sirm.tableview.dto.TableViewColumnRespDTO;
import com.sinitek.sirm.tableview.dto.TableViewColumnSaveDTO;

import java.util.List;

/**
 * 表格视图字段 Service 接口
 *
 * <AUTHOR>
 * date 2024-01-03
 */
public interface ITableViewColumnService {

    /**
     * 根据viewId批量删除
     * @param viewId
     */
    void deleteTableViewColumnListByViewId(Long viewId);

    /**
     * 批量保存字段列表
     * @param columnList
     * @param viewId
     */
    void saveBatchList(List<TableViewColumnSaveDTO> columnList, Long viewId);

    /**
     * 根据viewId获取List<TableViewColumnRespDTO>
     * @param viewId
     * @return
     */
    List<TableViewColumnRespDTO> findTableViewColumnList(Long viewId);

    /**
     * 只查询展示的列
     * @param viewId
     * @return
     */
    List<TableViewColumnRespDTO> findShowTableViewColumnList(Long viewId);
}
