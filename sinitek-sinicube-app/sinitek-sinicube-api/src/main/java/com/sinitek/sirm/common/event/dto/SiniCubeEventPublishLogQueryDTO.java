package com.sinitek.sirm.common.event.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date  2022/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "事件触发历史查询参数dto")
public class SiniCubeEventPublishLogQueryDTO extends PageDataParam {

    @Schema(description = "事件对象")
    private String name;

    @Schema(description = "触发结果")
    private Integer result;

    @Schema(description = "触发时间范围")
    private List<Date> time;

    @Schema(description = "触发开始时间")
    private Date startTime;

    @Schema(description = "触发结束时间")
    private Date endTime;
}
