package com.sinitek.sirm.common.message.template.enumerate;

import com.sinitek.base.enumsupport.AbstractEnumItem;

/**
 * <AUTHOR>
 * @Date 2021/9/8
 */
public class SendModeTypeEnum extends AbstractEnumItem {

    public static final SendModeTypeEnum SENDMODE_EMAIL = new SendModeTypeEnum("1", 1, "邮件", null);
    public static final SendModeTypeEnum SENDMODE_SMS = new SendModeTypeEnum("2", 2, "短信", null);
    public static final SendModeTypeEnum SENDMODE_SYSREMINDER = new SendModeTypeEnum("3", 4, "系统消息", null);
    public static final SendModeTypeEnum SENDMODE_WXWORK = new SendModeTypeEnum("4", 8, "企业微信", null);
    public static final SendModeTypeEnum SENDMODE_MOBILE = new SendModeTypeEnum("5", 16, "手机推送", null);
    /**
     * 只允许子类使用的构造函数
     *
     * @param enumItemName         枚举项名称
     * @param enumItemValue        枚举项值
     * @param enumItemInfo         枚举项说明信息
     * @param enumItemDisplayValue 枚举项显示值
     */
    protected SendModeTypeEnum(String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }
}

