
package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * User: wyzhang
 * Date: 2018/5/31.
 */
@Data
@Schema(description = "组织结构树")
public class CandidateDTO implements Serializable {
    @Schema(description = "ID")
    private String id;
    @Schema(description = "节点名称")
    private String label;
    @Schema(description = "图标")
    private String icon;
    @Schema(description = "主键")
    private String key;
    @Schema(description = "类型")
    private String type;
    @Schema(description = "类型")
    private int orgtype;
    @Schema(description = "多种类型")
    private int multitype;
    @Schema(description = "父节点id")
    private String parentId;
    @Schema(description = "子节点")
    private transient Object children;
    @Schema(description = "禁用状态")
    private boolean disabled = false;

}
