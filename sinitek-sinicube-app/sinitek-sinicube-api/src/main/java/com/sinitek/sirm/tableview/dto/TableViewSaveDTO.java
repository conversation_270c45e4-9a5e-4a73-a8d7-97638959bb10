package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 表格视图-保存DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图-保存DTO")
public class TableViewSaveDTO extends TableViewBaseDTO {

    @Schema(description = "主键")
    private Long id;

    @Valid
    @Schema(description = "条件组列表")
    private List<TableViewConditionGroupSaveDTO> conditionGroupList;

    @Valid
    @Schema(description = "字段列表")
    private List<TableViewColumnSaveDTO> columnList;
}
