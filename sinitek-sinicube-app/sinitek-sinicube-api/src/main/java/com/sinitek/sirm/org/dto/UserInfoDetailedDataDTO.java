package com.sinitek.sirm.org.dto;

import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 员工较详细信息列表, 选人控件弹出框，角色管理-角色成员，组织结构管理-部门员工、岗位员工、
 *
 * <AUTHOR>
 * @date 2021-02-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "员工较详细信息")
public class UserInfoDetailedDataDTO extends TenantIdParam {

    @Schema(description = "用户orgId")
    private String orgid;

    @Schema(description = "用户Id")
    private String userId;

    @Schema(description = "登录名")
    private String username;

    @Schema(description = "员工姓名")
    private String empname;

    /**
     * com.sinitek.sirm.user.enumerate.SexEnum
     */
    @Schema(description = "性别")
    private int sex;

    private String displayname;

    private String email;

    private String tel;

    private String key;

    private String positionname;

    private String rolename;

    private String teamname;

    private String superior;

    private String city;

    private Boolean inservice;

    private List<EmpParentPositionDTO> positionList;
}
