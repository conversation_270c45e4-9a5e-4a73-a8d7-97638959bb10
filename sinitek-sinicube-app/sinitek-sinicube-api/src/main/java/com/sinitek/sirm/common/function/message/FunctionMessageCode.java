package com.sinitek.sirm.common.function.message;

/**
 * 功能配置系列的Message Code 常量
 *
 * <AUTHOR>
 * @date 2020-08-19
 */
public class FunctionMessageCode {

    /**
     * 代码不能为空
     */
    public final static String CODE_CAN_NOT_NULL = "0601001";

    /**
     * 名称不能为空
     */
    public final static String NAME_CAN_NOT_NULL = "0601002";

    /**
     * 排序不能为空
     */
    public final static String SORT_CAN_NOT_NULL = "0601003";

    /**
     * 分组ID不能为空
     */
    public final static String GROUP_ID_CAN_NOT_NULL = "0601004";

    /**
     * 类型不能为空
     */
    public final static String TYPE_CAN_NOT_NULL = "0601005";

    /**
     * url不能为空
     */
    public final static String URL_CAN_NOT_NULL = "0601006";

    /**
     * action不能为空
     */
    public final static String ACTION_CAN_NOT_NULL = "0601007";

    /**
     * method不能为空
     */
    public final static String METHOD_CAN_NOT_NULL = "0601008";

    /**
     * 功能配置类型有误
     */
    public final static String FUNCTION_TYPE_ERROR = "0601009";

    /**
     * 功能配置保存错误
     */
    public final static String FUNCTION_SAVE_ERROR = "0601010";

    /**
     * 名称不能超过100个字符
     */
    final public static String NAME_LENGTH_TOO_BIG = "0601011";

    /**
     * url不能超过1000个字符
     */
    final public static String URL_LENGTH_TOO_BIG = "0601012";

    /**
     * 功能类型错误,1为CSS选择器,2为功能代码,3为埋点，其他值不支持
     */
    final public static String MENU_FUNCTION_TYPE_ERROR = "0601013";

    /**
     * 说明不能超过100个字符
     */
    final public static String BRIEF_LENGTH_TOO_BIG = "0601014";

    /**
     * Css选择器不能超过100个字符
     */
    final public static String SELECTOR_LENGTH_TOO_BIG = "0601015";

    /**
     * 功能配置所属菜单不能为空
     */
    final public static String FUNCTION_MENU_ID_CAN_NOT_NULL = "0601016";

    /**
     * menuId有误,没有对应的菜单信息
     */
    final public static String MENU_ID_ERROR_NOT_DATA = "0601017";

    /**
     * 功能代码不能超过100个字符
     */
    final public static String CODE_LENGTH_TOO_BIG = "0601018";

    /**
     * CSS选择器已存在
     */
    final public static String SELECTOR_EXIST_REPEAT = "0601019";

    /**
     * 功能名称已存在
     */
    final public static String NAME_EXIST_REPEAT = "0601020";

    /**
     * 分类名不能为空
     */
    final public static String GROUP_NAME_CAN_NOT_NULL = "0601021";

    /**
     * 分组名不能超过100个字符
     */
    final public static String GROUP_NAME_LENGTH_TOO_BIG = "0601022";

    /**
     * 分类说明不能超过100个字符
     */
    final public static String GROUP_BRIEF_LENGTH_TOO_BIG = "0601023";

    /**
     * 功能分类不能重复
     */
    final public static String FUNCTION_GROUP_CAN_NOT_REPEAT = "0601024";

    /**
     * 功能id集合不能为空
     */
    public final static String ID_LIST_CAN_NOT_NULL = "0601025";

    /**
     * 功能信息不能为空
     */
    public final static String FUNCTION_INFO_NOT_EXIST = "0601026";

    /**
     * 功能编码已存在
     */
    public final static String FUNCTION_CONTENT_ALREADY_EXISTS = "0601027";

    /**
     * 功能名不能为空
     */
    public final static String FUNCTION_NAME_CAN_NOT_NULL = "com.sinitek.sirm.common.function.message.function_name_can_not_null";

    /**
     * 功能类型不能为空
     */
    public final static String FUNCTION_TYPE_CAN_NOT_NULL = "com.sinitek.sirm.common.function.message.function_type_can_not_null";

    /**
     * 功能编码不能为空
     */
    public final static String FUNCTION_VALUE_CAN_NOT_NULL = "com.sinitek.sirm.common.function.message.function_value_can_not_null";

    /**
     * 功能归属不能为空
     */
    public final static String FUNCTION_BELONG_CAN_NOT_NULL = "com.sinitek.sirm.common.function.message.function_belong_can_not_null";

    /**
     * 关联菜单不存在，请检查菜单管理
     */
    public final static String FUNCTION_LINK_MENU_NOT_EXIST = "com.sinitek.sirm.common.function.message.function_link_menu_not_exist";

    /**
     * 功能条件查询结果异常
     */
    public final static String FUNCTION_SEARCH_RESULT_ERROR = "com.sinitek.sirm.common.function.message.function_search_result_error";

    /**
     * 功能编码长度不能超过100个字符
     */
    public final static String FUNCTION_VALUE_IS_TOO_BIG = "com.sinitek.sirm.common.function.message.function_value_is_too_big";

    /**
     * 功能所属页面路由不能为空
     */
    public final static String FUNCTION_BELONG_ROUTE_CAN_NOT_NULL = "com.sinitek.sirm.common.function.message.function_belong_route_can_not_null";

    /**
     * 当前用户【{0}】没有功能【{1} 】的访问权限,相关url【{2}】
     */
    public final static String NO_FUNCTION_URL_AUTH = "com.sinitek.sirm.common.function.message.no_function_url_auth";

    /**
     * 当前用户【{0}】没有相关功能的访问权限
     */
    public final static String NO_AUTH = "com.sinitek.sirm.common.function.message.no_auth";

    /**
     * 当前用户【{0}】没有功能【{1}】的访问权限，可以在【功能权限设置】中进行设置
     */
    public final static String NO_FUNCTION_AUTH = "com.sinitek.sirm.common.function.message.no_function_auth";

    /**
     * URL由数字、英文字母、英文字符组成
     */
    public final static String URL_VALIDATE = "com.sinitek.sirm.common.function.message.url_validate";

    /**
     * 功能页面路由由数字、英文字母、英文字符组成
     */
    public final static String PAGE_URL_VALIDATE  = "com.sinitek.sirm.common.function.message.page_url_validate";

    /**
     * css选择器由数字、英文字母、英文字符组成
     */
    public final static String CSS_VALIDATE  = "com.sinitek.sirm.common.function.message.css_validate";

    /**
     * 功能代码由数字、英文字母、英文字符组成
     */
    public final static String CODE_VALIDATE  = "com.sinitek.sirm.common.function.message.code_validate";

    /**
     * 排序失败
     */
    public final static String GROUP_SORT_FAILED  = "com.sinitek.sirm.common.function.message.sort_failed";

    /**
     * 功能分组名称不能重复
     */
    public final static String GROUP_NAME_REPEAT  = "com.sinitek.sirm.common.function.message.group_name_repeat";

    /**
     * 功能分组所属路由不能重复
     */
    public final static String GROUP_ROUTE_PATH_REPEAT  = "com.sinitek.sirm.common.function.message.group_route_path_repeat";

    /**
     * 功能分组名称不能为空
     */
    public final static String GROUP_NAME_CANT_NULL  = "com.sinitek.sirm.common.function.message.group_name_cant_null";

    /**
     * 功能分组【{0}】不存在
     */
    public final static String GROUP_NOT_EXIST  = "com.sinitek.sirm.common.function.message.group_not_exist";

    /**
     * 功能页面长度不能超过100个字符
     */
    public final static String FUNCTION_PAGE_CANT_BEYOND_100  = "com.sinitek.sirm.common.function.message.function_page_cant_beyond_100";
}
