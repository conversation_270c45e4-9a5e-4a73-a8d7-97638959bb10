package com.sinitek.sirm.common.attachment.utils;

import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;

import java.util.List;

/**
 * 附件操作工具类，支持local和cloud两种场景
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
public class AttachmentExtUtils {

    private AttachmentExtUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获得 IAttachmentExtService
     * @return
     */
    public static IAttachmentExtService getAttachmentService () {
        return SpringFactory.getBean(IAttachmentExtService.class);
    }

    /**
     * 根据附件的明文id克隆Attachment
     * @param id
     * @return
     */
    public static AttachmentDTO copyAttachment(Long id) {
        if (IdUtil.isNotDataId(id)) {
            return null;
        }
        AttachmentDTO attachment = getAttachmentService().getAttachmentById(id);
        if (attachment == null) {
            return null;
        }
        return attachment.cloneNewAttachment();
    }

    /**
     * 保存文件列表
     * @param uploadDto 文件接收dto
     * @param sourceId  来源id
     * @param sourceEntity 来源实体
     */
    public static List<AttachmentDTO> saveAttachmentList(UploadDTO uploadDto, Long sourceId, String sourceEntity) {
        return getAttachmentService().saveAttachmentList(uploadDto, sourceId, sourceEntity);
    }
}
