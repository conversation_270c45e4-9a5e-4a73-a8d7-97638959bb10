package com.sinitek.sirm.org.enumerate;

import com.sinitek.base.enumsupport.AbstractEnumItem;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: com.sinitek.sirm.org.busin.enumerate
 * Author:      潘虹
 * History:     11-8-3 created by 潘虹
 */
public class QualifyType extends AbstractEnumItem {
    /**
     * 一般证券业务
     */
    public static final QualifyType ONE = new QualifyType("1", 1, "分析师业务", null);
    /**
     * 投资咨询业务        小于等于2 可以写报告
     */
    public static final QualifyType TWO = new QualifyType("2", 2, "一般证券业务", null);

    /**
     * 投资顾问业务         小于等于3可以写晨会
     * **/
    public static final QualifyType THREE = new QualifyType("3", 3, "投资顾问业务", null);

    /**
     * 香港分析师业务
     * **/
    public static final QualifyType FOUR = new QualifyType("4", 4, "香港分析师业务", null);

    /**
     * 无资格
     */
    public static final QualifyType ZERO = new QualifyType("0", 0, "无资格", null);

    protected QualifyType(String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }
}
