package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.message.template.support.IMessageTextHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 触发动作的参数对象
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Schema(description = "触发动作的参数对象")
@Data
public class TriggerActionDTO<T> {

    @Schema(description = "绑定对象id")
    private String sourceId;

    @Schema(description = "绑定对象名")
    private String sourceName;

    @Schema(description = "动作初始参数")
    private T data;

    @Schema(description = "事件发布记录id")
    private Long eventPublishLogId;

    @Schema(description = "动作初始参数class名称")
    private String dataClassName;

    @Schema(description = "动作code，未绑定关系时通过该code直接执行动作")
    private String code;

    @Schema(description = "动作表单数据")
    private String componentData;

    @Schema(description = "自定义占位符替换handler")
    private IMessageTextHandler messageTextHandler;

    @Schema(description = "动作初始参数class")
    private Class<?> dataClass;
}
