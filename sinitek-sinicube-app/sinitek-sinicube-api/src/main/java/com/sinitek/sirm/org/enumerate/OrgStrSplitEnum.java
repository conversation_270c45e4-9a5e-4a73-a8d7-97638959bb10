package com.sinitek.sirm.org.enumerate;

/**
 * <AUTHOR>
 * @date 2022/11/18
 * 选人字符串分割后的代表
 */
public enum OrgStrSplitEnum {

    /**
     * 选人字符串截取第一位：orgid
     */
    ORGID(0),

    /**
     * 选人字符串截取第二位：orgname
     */
    ORGNAME(1),

    /**
     * 选人字符串截取第三位：orgType
     */
    ORGTYPE(2);

    private int code;

    OrgStrSplitEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
