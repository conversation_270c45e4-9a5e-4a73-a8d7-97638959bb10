package com.sinitek.sirm.user.service;

import com.sinitek.sirm.framework.frontend.support.RequestResult;

/**
 * 用户标识令牌-通用方法顶层接口
 *
 * <AUTHOR>
 * @date 2021/06/16
 */
public interface IUserFlagTokenService {

    /**
     * 根据userName生成用户标识令牌
     * @param userName   用户名称
     * @return
     * 根据userName生成用户标识令牌
     */
    String generateUserFlagToken(String userName);

    /**
     * 根据userFlagToken解密出userName
     * @param userFlagToken   用户标识令牌
     * @return
     * 根据userFlagToken解密出userName
     */
    String getUserNameByUserFlagToken(String userFlagToken);

    /**
     * 根据 userName登陆并返回 accessToken
     * @param userName   用户名称
     * @return
     *根据 userName登陆并返回 accessToken
     */
    String loginByUserName(String userName);

    /**
     * 链接登录-逻辑
     * @param userFlagTokenStr
     * @return
     */
    RequestResult<String> linkLogin(String userFlagTokenStr);
}
