package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表格视图条件-保存DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图条件-保存DTO")
public class TableViewConditionSaveDTO extends TableViewConditionBaseDTO {

    @Schema(description = "主键")
    private Long id;
}
