package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/8
 */
@Schema(description = "组织结构路径信息")
@Data
public class OrgRelationPathDTO {
    @Schema(description = "关系的起始节点，比如岗位是起始节点，岗位下的员工是结束节点")
    private String fromobjectid;

    @Schema(description = "关系的起始节点层级，从下往上数，以0开始")
    private int lev;

    @Schema(description = "组织结构的树枝编号")
    private int branch;
}
