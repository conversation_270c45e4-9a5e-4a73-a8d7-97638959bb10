package com.sinitek.sirm.common.message.template.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Schema(description= "对外系统消息DTO")
@Data
public class ExtSirmSendMessageDTO {

    @Schema(description= "发送状态")
    private Integer status;

    @Schema(description= "消息标题")
    private String title;

    @Schema(description= "消息类型")
    private Integer type;

    @Schema(description= "定时发送标识")
    private Integer isTime;

    @Schema(description= "发送时间")
    private Date sendTime;

    @Schema(description= "发送人Id")
    private String senderId;

    @Schema(description= "发送人名称")
    @TableField (value = "sender_name")
    private String senderName;

    @Schema(description= "消息重要度")
    private Integer importantLevel;

    @Schema(description= "来源记录id")
    private Long sourceId;

    @Schema(description= "来源记录名称")
    private String sourceEntity;

    @Schema(description= "发送邮件地址")
    private String mailFrom;

    @Schema(description= "多租户id")
    private String tenantId;

    @Schema(description= "消息详情集合")
    private List<ExtSirmSendMessageDetailDTO> extSirmSendMessageDetailDTOS;
}
