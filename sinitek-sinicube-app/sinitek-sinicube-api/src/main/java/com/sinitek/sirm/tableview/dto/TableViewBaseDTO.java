package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.tableview.message.TableViewMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 表格视图 基础DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@Schema(description = "表格视图-基础DTO")
public class TableViewBaseDTO {

    @Schema(description = "视图名称", required = true)
    @NotBlank(message = "{"+ TableViewMessage.NAME_CAN_NOT_NULL +"}")
    @Length(max = 10,message = "{"+ TableViewMessage.NAME_CAN_NOT_EXCEED +"}")
    private String name;

    @Schema(description = "视图颜色", required = true)
    @Length(max = 30,message = "{"+ TableViewMessage.COLOR_CAN_NOT_EXCEED +"}")
    private String color;

    @Schema(description = "表格的唯一标识", required = true)
    @NotBlank(message = "{"+ TableViewMessage.TABLE_ID_CAN_NOT_NULL +"}")
    @Length(max = 100,message = "{"+ TableViewMessage.TABLEID_CAN_NOT_EXCEED +"}")
    private String code;

    @Schema(description = "是否展示, 0=否、1=是")
    private Integer showFlag;

    @Schema(description = "用户Id", required = true)
    @Length(max = 20,message = "{"+ TableViewMessage.USERID_CAN_NOT_EXCEED +"}")
    private String userOrgId;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "是否默认")
    private Integer defaultFlag = 0;
}
