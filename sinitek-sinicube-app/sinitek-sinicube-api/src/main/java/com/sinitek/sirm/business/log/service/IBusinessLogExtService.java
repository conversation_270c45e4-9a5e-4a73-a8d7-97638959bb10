package com.sinitek.sirm.business.log.service;

import com.sinitek.sirm.business.log.dto.BusinessLogDTO;

/**
 * 统一 业务日志 接口
 *
 * <AUTHOR>
 * @date 2021-08-20
 */
public interface IBusinessLogExtService {

    /**
     * 添加一个业务日志
     * @param businessLogDTO
     */
    void saveBusinessLog(BusinessLogDTO businessLogDTO);

    /**
     * 简化添加业务日志步骤
     * @param moduleName
     * @param operateType
     * @param desc
     */
    default void simpleSaveBusinessLog(String moduleName, int operateType, String desc) {
        BusinessLogDTO defaultBusinessLog = this.createDefaultBusinessLog();
        defaultBusinessLog.setModuleName(moduleName);
        defaultBusinessLog.setOperateType(operateType);
        defaultBusinessLog.setDescription(desc);
        saveBusinessLog(defaultBusinessLog);
    }

    /**
     *
     * @param moduleName 操作类型名称
     * @param operateType 操作类型
     * @param desc 操作描述
     * @param bigDesc 详细描述
     */
    default void simpleSaveBusinessLog(String moduleName, int operateType, String desc, String bigDesc) {
        BusinessLogDTO defaultBusinessLog = this.createDefaultBusinessLog();
        defaultBusinessLog.setModuleName(moduleName);
        defaultBusinessLog.setOperateType(operateType);
        defaultBusinessLog.setDescription(desc);
        defaultBusinessLog.setBigDescription(bigDesc);
        saveBusinessLog(defaultBusinessLog);
    }

    /**
     * 简化添加业务日志步骤 (额外设置userId 和 tenantId)
     * @param userId
     * @param tenantId
     * @param moduleName
     * @param operateType
     * @param desc
     */
    default void simpleSaveBusinessLog(String userId, String tenantId, String moduleName, int operateType, String desc) {
        BusinessLogDTO defaultBusinessLog = createDefaultBusinessLog();
        defaultBusinessLog.setUserId(userId);
        defaultBusinessLog.setTenantId(tenantId);
        defaultBusinessLog.setModuleName(moduleName);
        defaultBusinessLog.setOperateType(operateType);
        defaultBusinessLog.setDescription(desc);

        saveBusinessLog(defaultBusinessLog);
    }

    /**
     * 创建默认的 BusinessLogDTO
     * @return
     */
    BusinessLogDTO createDefaultBusinessLog();
}
