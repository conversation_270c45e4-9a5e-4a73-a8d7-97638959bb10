package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表格视图 分页查询DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图-分页查询DTO")
public class TableViewSearchDTO extends PageDataParam {

    @Schema(description = "视图名称")
    private String name;

    @Schema(description = "视图颜色")
    private String color;

    @Schema(description = "表格的唯一标识")
    private String tableId;

    @Schema(description = "是否开启默认排序, 0=否、1=是")
    private Integer defaultSortFlag;

    @Schema(description = "是否展示, 0=否、1=是")
    private Integer showFlag;

    @Schema(description = "用户Id")
    private String userId;


}
