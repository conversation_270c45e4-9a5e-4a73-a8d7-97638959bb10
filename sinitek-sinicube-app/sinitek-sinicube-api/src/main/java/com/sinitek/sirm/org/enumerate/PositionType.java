package com.sinitek.sirm.org.enumerate;

import com.sinitek.base.enumsupport.AbstractEnumItem;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: com.sinitek.sirm.org.busin.enumerate
 * Author:      潘虹
 * History:     11-10-21 created by 潘虹
 */
public class PositionType extends AbstractEnumItem {
    /**
     * 行政
     */
    public static final PositionType EXECUTE = new PositionType("SUPEXECUTE", 1, "行政上级", null);
    /**
     *业务
     */
    public static final PositionType BUSINESS = new PositionType("SUPBUSIN", 2, "业务上级", null);

    protected PositionType(String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }
}
