package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表格视图字段 分页查询DTO
 *
 * <AUTHOR>
 * date 2024-01-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图字段-分页查询DTO")
public class TableViewColumnSearchDTO extends PageDataParam {

    @Schema(description = "所属视图Id")
    private Long viewId;

    @Schema(description = "字段名")
    private String name;

    @Schema(description = "排序字段，从小到大")
    private Integer sort;


}
