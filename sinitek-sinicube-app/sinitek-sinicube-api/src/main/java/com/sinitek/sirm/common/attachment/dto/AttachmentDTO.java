package com.sinitek.sirm.common.attachment.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sinitek.cloud.sirmapp.base.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 * 附件信息数据对象
 *
 * <AUTHOR>
 * @date 2021/5/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AttachmentDTO extends BaseDTO {

    @Schema(description = "加密主键")
    private String id;

    @Schema(description = "附件类型")
    private Integer type;

    @Schema(description = "附件名称")
    private String name;

    @Schema(description = "附件大小")
    private Long contentSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "存储方式")
    @TableField(value = "storetype")
    private Integer storeType;

    @Schema(description = "文档存储路径")
    @TableField(value = "storepath")
    private String storePath;

    @Schema(description = "所属实体名称")
    private String sourceEntity;

    @Schema(description = "所属业务实例ID")
    private Long sourceId;

    @Schema(description = "页数")
    private Long pageCount;

    @Schema(description = "转换标识")
    private Integer convertStatus;

    @Schema(description = "附件摘要")
    private String digest;

    @Schema(description = "转换结果")
    private Integer convertResult;

    @Schema(description = "转换标识")
    private Integer sendFlag;

    @Schema(description = "转换标识")
    private Integer convertFlag;

    @Schema(description = "转换附件Id")
    private Integer convertId;

    @Schema(description = "附件上传人id")
    private String ownerId;

    @Schema(description = "备注")
    private String brief;

    @Schema(description = "storeKey")
    private String storeKey;

    @Schema(description = "是否被删除")
    private Integer deletedFlag;

    @Schema(description = "加密秘钥")
    private String encKey;

    @Schema(description = "加密所用的算法")
    private String encAlgorithm;

    @Schema(description = "附件内容id")
    private Long contentId;

    /**
     * 基于当前对象,构建出一个新的Attachment
     * @return
     */
    public AttachmentDTO cloneNewAttachment() {
        AttachmentDTO attachment = new AttachmentDTO();
        BeanUtils.copyProperties(this, attachment, "objId");
        return attachment;
    }


}
