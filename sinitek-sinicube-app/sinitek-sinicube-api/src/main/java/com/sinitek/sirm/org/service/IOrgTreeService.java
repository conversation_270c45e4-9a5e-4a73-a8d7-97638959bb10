package com.sinitek.sirm.org.service;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import com.sinitek.sirm.org.dto.*;
import com.sinitek.sirm.org.message.OrgTreeMessageCode;
import com.sinitek.sirm.tenant.support.TenantIdParam;
import com.sinitek.spirit.org.core.dto.OrgSpiritObjectDTO;

import java.util.List;
import java.util.Map;

/**
 * Org 树形列表展示Service
 *
 * <AUTHOR>
 * @date 2020-7-20
 */
public interface IOrgTreeService {

    /**
     * 选人控件 - 左侧组织结构树
     *
     * @param param   组织结构查询参数
     * @return
     * 返回左侧组织结构树
     */
    List<OrgTreeDTO> candidateTree(CandidateParamDTO param);

    /**
     * 根据名称查询所有人员、部门、岗位、角色、小组
     *
     * @param jointName   查询参数
     * @return
     * 返回符合条件的人员、部门、岗位、角色、小组
     */
    List<AllOrgInfoDTO> candidateSelectAll(String jointName);

    /**
     * 组织结构树整个加载的逻辑
     *
     * @param orgTreeParamDTO   组织结构树查询条件
     * @return
     * 返回左侧组织结构树
     */
    List<OrgTreeDTO> loadAllOrgTree(OrgTreeParamDTO orgTreeParamDTO);

    /**
     * 加载组织结构树-- 公司
     * @param hasEmp     上级组织结构
     * @param parent     是否加载员工
     * @param operation  是否允许管理
     * @return
     * 返回左侧组织结构树
     */
    default OrgTreeDTO loadOrgTree(OrgSpiritObjectDTO parent, boolean hasEmp, boolean operation){
        return this.loadOrgTree(parent, true, hasEmp, operation);
    }

    /**
     * 加载组织结构树-- 公司
     * @param parent 上级组织结构
     * @param hasPosition 是否加载岗位
     * @param hasEmp 是否加载员工
     * @param operation 是否允许管理
     * @return
     * 返回组织结构树
     */
    OrgTreeDTO loadOrgTree(OrgSpiritObjectDTO parent, boolean hasPosition, boolean hasEmp, boolean operation);


    /**
     *
     * 加载组织结构树,过滤选人方案
     * @param parent 上级组织结构
     * @param hasPosition 是否加载岗位
     * @param hasEmp 是否加载员工
     * @param operation 是否允许管理
     * @return
     * 返回组织结构树
     */
    OrgTreeDTO loadOrgTreeWithSchemeList(OrgSpiritObjectDTO parent,
                                         String mode,
                                         boolean hasPosition,
                                         boolean hasEmp,
                                         boolean operation,
                                         Map<String, String> schemeMap);

    /**
     *
     * 加载组织结构树,过滤选人方案
     * @param parent 上级组织结构
     * @param hasPosition 是否加载岗位
     * @param hasEmp 是否加载员工
     * @param operation 是否允许管理
     * @param selectOrgDTO 选中组织结构DTO
     * @return
     * 返回组织结构树
     */
    OrgTreeDTO loadOrgTreeWithSchemeList(OrgSpiritObjectDTO parent,
                                         String mode,
                                         boolean hasPosition,
                                         boolean hasEmp,
                                         boolean operation,
                                         Map<String, String> schemeMap,
                                         CandidateSelectOrgDTO selectOrgDTO);

    /**
     * 加载组织结构单选树
     *
     * @param tenantIdParam 所属租户对象
     * @param hideTeam 是否隐藏小组
     * @param hideRole 是否隐藏角色
     * @return
     * 返回行政上级树
     */
    List<OrgCascaderDTO> loadOrgCascaderTree(TenantIdParam tenantIdParam, boolean hideTeam, boolean hideRole);

    /**
     * 加载组织结构树-- 小组
     * @param rootId        组织结构rela树状查询
     * @param hasEmp         是否加载员工
     * @param operation      是否允许管理
     * @return
     * 返回组织结构树
     */
    default OrgTreeDTO loadTeamTree(String rootId,boolean hasEmp,boolean operation) {
        throw new BussinessException(OrgTreeMessageCode.NOT_SUPPORT_TEAM_TREE);
    }

    /**
     * 加载组织结构树-- 未分配岗位
     * @param hasEmp      是否加载员工
     * @param operation   是否允许管理
     * @return
     *返回组织结构树
     */
    default OrgTreeDTO loadNoOrgEmpTree(boolean hasEmp,boolean operation) {
        throw new BussinessException(OrgTreeMessageCode.NOT_SUPPORT_JOBLESS_STAFF_TREE);
    }

    /**
     * 加载组织结构树-- 角色
     * @param rootId      组织结构rela树状查询
     * @param hasEmp      是否加载员工
     * @param operation   是否允许管理
     * @return
     * 返回组织结构树
     */
    default OrgTreeDTO loadRoleTree(String rootId,boolean hasEmp,boolean operation) {
        throw new RuntimeException(OrgTreeMessageCode.NOT_SUPPORT_ROLE_TREE);
    }

    /**
     * 检查树模型是否正确
     * @param param  组织结构是否需要初始化参数
     */
    default void checkOrgTree(InitCheckParamDTO param) {
        throw new RuntimeException(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT);
    }

    /**
     * 加载组织结构树-- 用户(拥有自身权限的用户)
     * @return
     */
    OrgTreeDTO loadUserTree(OrgTreeParamDTO paramDTO);
}
