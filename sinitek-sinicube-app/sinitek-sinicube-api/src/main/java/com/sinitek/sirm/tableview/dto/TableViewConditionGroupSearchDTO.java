package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表格视图条件组 分页查询DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图条件组-分页查询DTO")
public class TableViewConditionGroupSearchDTO extends PageDataParam {

    @Schema(description = "所属视图Id")
    private Long viewId;

    @Schema(description = "组内关系, 1=全部、2=任何")
    private Integer groupRelation;


}
