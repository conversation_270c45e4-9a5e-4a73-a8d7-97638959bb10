package com.sinitek.sirm.org.enumerate;

import com.sinitek.base.enumsupport.AbstractEnumItem;
import com.sinitek.spirit.org.core.constant.OrgConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: org
 * Author:      潘虹
 * History:     11-5-11 created by 潘虹
 */
public class OrgType extends AbstractEnumItem {

    public static final String UNIT_NAME = "UNIT";
    public static final String POSITION_NAME = "POSITION";
    public static final String EMPLOYEE_NAME = "EMPLOYEE";
    public static final String TEAM_NAME = "TEAM";
    public static final String ROLE_NAME = "ROLE";

    /**
     * 部门
     */
    public static final OrgType DEPT = new OrgType("1", 1, UNIT_NAME, "部门");
    /**
     * 岗位
     */
    public static final OrgType POSITION = new OrgType("2", 2, POSITION_NAME, "岗位");
    /**
     * 人员
     */
    public static final OrgType EMPLOYEE = new OrgType("8", 8, <PERSON><PERSON>LOYEE_NAME, "人员");
    /**
     * 小组
     */
    public static final OrgType TEAM = new OrgType("4", 4, TEAM_NAME, "小组");
    /**
     * 角色
     */
    public static final OrgType ROLE = new OrgType("16", 16, ROLE_NAME, "角色");


    protected OrgType(String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }

    /**
     * 通过value获得对应的组织类型
     * @param value ：允许的值1,2,4,8,16
     * @return value!=1,2,4,8,16则返回null
     */
    public static OrgType getOrgTypeByValue(int value) {
        OrgType r = null;
        switch (value) {
            case OrgConstant.ORG_TYPE_DEPT:
                r = DEPT;
                break;
            case OrgConstant.ORG_TYPE_POST:
                r = POSITION;
                break;
            case OrgConstant.ORG_TYPE_TEAM:
                r = TEAM;
                break;
            case OrgConstant.ORG_TYPE_EMP:
                r = EMPLOYEE;
                break;
            case OrgConstant.ORG_TYPE_ROLE:
                r = ROLE;
                break;
            default:
        }
        return r;
    }

    /**
     * 通过组织机构名称获得组织结构类型
     * @param enumItemInfo 可以是UNIT，POSITION，EMPLOYEE，TEAM，ROLE或者小写的
     * @return 对应的组织机构类型，enumItemInfo不等于UNIT，POSITION，EMPLOYEE，TEAM，ROLE或者小写的，则默认返回OrgType.DEPT;
     */
    public static OrgType getOrgTypeName(String enumItemInfo) {
        OrgType orgType = OrgType.DEPT;
        if (UNIT_NAME.equals(enumItemInfo) || UNIT_NAME.toLowerCase().equals(enumItemInfo)) {
            orgType = OrgType.DEPT;
        }
        if (POSITION_NAME.equals(enumItemInfo) || POSITION_NAME.toLowerCase().equals(enumItemInfo)) {
            orgType = OrgType.POSITION;
        }
        if (EMPLOYEE_NAME.equals(enumItemInfo) || EMPLOYEE_NAME.toLowerCase().equals(enumItemInfo)) {
            orgType = OrgType.EMPLOYEE;
        }
        if (TEAM_NAME.equals(enumItemInfo) || TEAM_NAME.toLowerCase().equals(enumItemInfo)) {
            orgType = OrgType.TEAM;
        }
        if (ROLE_NAME.equals(enumItemInfo) || ROLE_NAME.toLowerCase().equals(enumItemInfo)) {
            orgType = OrgType.ROLE;
        }
        return orgType;
    }

    /**
     *
     * @param orgType
     * @param enumItemInfo
     * @return
     */
    public static boolean equalsOrgType(OrgType orgType, String enumItemInfo) {
        return orgType != null && enumItemInfo != null
                && (StringUtils.equals(orgType.getEnumItemInfo(), enumItemInfo.toUpperCase())
                || StringUtils.equals(orgType.getEnumItemInfo(), enumItemInfo.toLowerCase())
        );
    }

}
