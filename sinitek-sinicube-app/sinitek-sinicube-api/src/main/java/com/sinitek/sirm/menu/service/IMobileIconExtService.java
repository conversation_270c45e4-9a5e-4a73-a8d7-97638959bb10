package com.sinitek.sirm.menu.service;

import com.sinitek.sirm.menu.dto.MenuMobileIconDTO;

import java.util.List;

/**
 * 菜单图标统一Api
 *
 * <AUTHOR>
 * @date 2022/12/1
 */
public interface IMobileIconExtService {

    /**
     * 根据objid查询图标信息
     * @param objId
     * @return
     */
    MenuMobileIconDTO getIconByObjId(Long objId);

    /**
     * 根据类型获取排序
     * @param type
     * @return
     */
    MenuMobileIconDTO getSortByType(String type);

    /**
     * 根据MenuMobileIconDTO更新或保存图标信息
     * @param menuMobileIconDTO
     * @return
     */
    Long saveOrUpdate(MenuMobileIconDTO menuMobileIconDTO);
    /**
     * 根据Ids删除图标信息
     * @param ids
     * @return
     */
    void removeByIds(List<Long> ids);

}
