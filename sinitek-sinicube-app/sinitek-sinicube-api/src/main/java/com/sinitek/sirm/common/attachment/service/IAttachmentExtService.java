
package com.sinitek.sirm.common.attachment.service;

import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.dto.DownloadFileResultDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 统一附件相关接口
 *
 * <AUTHOR>
 * @date 2021/5/26
 */
public interface IAttachmentExtService {

    /**
     * 保存文件列表
     * @param uploadDto
     * @param sourceId
     * @param sourceEntity
     */
    List<AttachmentDTO> saveAttachmentList(UploadDTO uploadDto, Long sourceId, String sourceEntity);

    /**
     * 根据临时文件对象获取对应的 文件流
     * @param uploadFileDTO
     * @return
     */
    InputStream getInputStreamByUpdateFile(UploadFileDTO uploadFileDTO);

    /**
     * 基于attachmentIdList, 复制一份新的附件。并保存到对应的 newSourceId 和 newSourceEntity
     *
     * @param attachmentIdList
     * @param newSourceId
     * @param newSourceEntity
     * @return
     */
    List<AttachmentDTO> copyAttachmentList(List<Long> attachmentIdList, Long newSourceId, String newSourceEntity);

    /**
     * 根据id获取附件内容
     * @param id
     * @return
     * 若存在，返回附件内容
     * 若不存在，返回null
     * @see IAttachmentExtService#getAttachmentAsInputStream(Long)
     */
    @Deprecated
    byte[] getAttachmentAsInputStreamById(Long id);

    /**
     * 根据id获取附件流
     * @param id
     * @return
     */
    InputStream getAttachmentAsInputStream(Long id);

    /**
     * 保存附件列表,并返回保存后的信息
     *
     * @param sourceId
     * @param sourceEntity
     * @param attachmentInfoDtoList
     * @return
     */
    List<AttachmentDTO> saveAttachmentList(Long sourceId, String sourceEntity, List<AttachmentDTO> attachmentInfoDtoList);

    /**
     * 保存单个附件,并返回保存后的信息
     *
     * @param attachmentDTO
     * @return
     */
    AttachmentDTO saveAttachment(AttachmentDTO attachmentDTO);

    /**
     * 根据id获取附件信息
     * @param id
     * @return
     * 若存在，返回附件信息
     * 若不存在，返回null
     */
    AttachmentDTO getAttachmentById(Long id);


    /**
     * 根据加密id获取附件信息
     * @param encryptId
     * @return
     * 若存在，返回附件信息
     * 若不存在，返回null
     */
    AttachmentDTO getAttachmentByEncryptId(String encryptId);



    /**
     * 查询附件
     * @param sourceEntity 实体名称
     * @param sourceId     关联业务编号
     * @return
     * 返回附件列表
     */
    List<AttachmentDTO> findAttachmentList(String sourceEntity, Long sourceId);

    /**
     * 查询附件
     *
     * @param sourceEntity 实体名称
     * @param sourceId  关联业务编号
     * @param type 附件类型
     * @deprecated 该方法只能取到一个对象，而不是一个list，请替换为{@link #getAttachment(String sourceEntity, Long sourceId, int type)}
     * @return
     * 返回附件列表
     *
     */
    List<AttachmentDTO> findAttachmentList(String sourceEntity, Long sourceId, int type);

    /**
     * 查询附件
     *
     * @param sourceEntity 实体名称
     * @param sourceId  关联业务编号
     * @param type 附件类型
     * @return
     * 返回附件列表
     *
     */
    AttachmentDTO getAttachment(String sourceEntity, Long sourceId, int type);

    /**
     * 查询附件
     * @param attachmentIdList
     * @return
     */
    List<AttachmentDTO> findAttachmentList(List<Long> attachmentIdList);

    /**
     * 根据附件id删除附件
     * @param id
     */
    void removeAttachment(Long id);

    /**
     * 根据sourceEntity和sourceId删除附件列表
     * @param sourceEntity
     * @param sourceId
     */
    void removeAttachment(String sourceEntity, Long sourceId);

    /**
     * 删除附件信息
     * @param attachmentDTO
     */
    void removeAttachment(AttachmentDTO attachmentDTO);
    /**
     * 批量移除附件
     * @param ids
     */

    void batchRemoveAttachment(String ... ids);

    /**
     * 批量移除附件
     * @param ids
     */

    void batchRemoveAttachment(List<Long> ids);

    /**
     * 批量删除附件
     * @param ids
     * @param sourceEntity
     * @param sourceId
     * @return 返回剩下附件个数
     */
    int removeAttachmentList(String ids, String sourceEntity, Long sourceId);

    /**
     * 根据实体名称、实体Id、实体type删除附件信息
     * @param sourceEntity
     * @param sourceId
     * @param type
     */
    void removeAttachmentList(String sourceEntity, Long sourceId, Integer type);

    /**
     *
     * @param sourceEntity  实体名称
     * @param type          类型 ，-1：数据库为空
     * @param sourceIdList  实体id集合
     * @return              附件
     */
    List<AttachmentDTO> findAttachments(String sourceEntity, Integer type, List<Long> sourceIdList);

    /**
     * 根据输入流，保存附件正文
     * @param dto   附件属性
     * @param file 附件正文
     * @return 保存后的参数
     */
    AttachmentDTO saveAttachment(AttachmentDTO dto, MultipartFile file);

    /**
     * 下载附件
     * @param request
     * @param response
     */
    String downloadAttachment(HttpServletRequest request, HttpServletResponse response);

    /**
     * 更小覆盖下载附件
     * @param id
     * @return
     */
    DownloadFileResultDTO downloadAttachment(Long id);
}
