package com.sinitek.sirm.tableview.message;

/**
 * 表格视图条件 MessageCode
 *
 * <AUTHOR>
 * date 2023-07-11
 */
public class TableViewConditionMessage {

    private TableViewConditionMessage() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 字段名称不能为空
     */
    public static final String FIELDNAME_CAN_NOT_NULL = "com.sinitek.sirm.tableview.fieldname_can_not_null";
    /**
     * 字段名称不能超过30个字符
     */
    public static final String FIELDNAME_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.fieldname_can_not_exceed";
    /**
     * 条件值不能为空
     */
    public static final String CONDITIONVALUE_CAN_NOT_NULL = "com.sinitek.sirm.tableview.conditionvalue_can_not_null";
    /**
     * 条件值不能超过30个字符
     */
    public static final String CONDITIONVALUE_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.conditionvalue_can_not_exceed";
    /**
     * 字段值不能为空
     */
    public static final String FIELDVALUE_CAN_NOT_NULL = "com.sinitek.sirm.tableview.fieldvalue_can_not_null";
    /**
     * 字段值不能超过300个字符
     */
    public static final String FIELDVALUE_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.fieldvalue_can_not_exceed";
    /**
     * 字段类型不能超过30个字符
     */
    public static final String FIELDTYPE_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.fieldtype_can_not_exceed";

    /**
     * 绑定的组件信息不能超过300个字符
     */
    public static final String COMPONENT_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.component_can_not_exceed";

    /**
     * 不支持[{0}]的判断条件: {0}
     */
    public static final String CONDITION_NOT_SUPPORT = "com.sinitek.sirm.tableview.condition_not_support";
}
