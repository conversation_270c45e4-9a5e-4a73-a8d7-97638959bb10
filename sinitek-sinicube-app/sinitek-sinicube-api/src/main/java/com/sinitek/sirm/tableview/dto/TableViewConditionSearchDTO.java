package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表格视图条件 分页查询DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图条件-分页查询DTO")
public class TableViewConditionSearchDTO extends PageDataParam {

    @Schema(description = "所属组Id")
    private Long groupId;

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "条件值")
    private String conditionValue;

    @Schema(description = "字段值")
    private String fieldValue;

    @Schema(description = "字段类型")
    private String fieldType;

}
