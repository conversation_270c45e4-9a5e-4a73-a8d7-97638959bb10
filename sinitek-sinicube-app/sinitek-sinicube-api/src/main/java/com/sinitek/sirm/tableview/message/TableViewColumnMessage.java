package com.sinitek.sirm.tableview.message;

/**
 * 表格视图字段 MessageCode
 *
 * <AUTHOR>
 * date 2024-01-03
 */
public class TableViewColumnMessage {

    private TableViewColumnMessage() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 字段名不能为空
     */
    public static final String NAME_CAN_NOT_NULL = "com.sinitek.sirm.tableviewcolumn.name_can_not_null";
    /**
     * 字段名不能超过60个字符
     */
    public static final String NAME_CAN_NOT_EXCEED = "com.sinitek.sirm.tableviewcolumn.name_can_not_exceed";
}
