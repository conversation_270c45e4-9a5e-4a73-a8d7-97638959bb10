package com.sinitek.sirm.calendar.dto;

import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/3/2
 */
@Data
@Schema(description = "日程表单DTO")
public class CalendarEventDataDTO{

    @Schema(description = "日程类型")
    private String type;

    @Schema(description = "日程表单数据")
    private Object calendarData;

    @Schema(description = "重复规则数据")
    private RepeatTimeDTO repeatTime;

    @Schema(description = "编辑类型：0-只编辑此日程，1-编辑此日程及后续日程")
    private Integer editType;

    @Schema(description = "编辑日期")
    private Date editDate;
}
