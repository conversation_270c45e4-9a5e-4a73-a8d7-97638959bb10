package com.sinitek.sirm.framework.frontend.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.common.encryption.factory.EncryptionAlgorithmFactory;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.setting.service.ISettingExtService;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "附件基本信息对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadFileDTO {

    @Schema(description = "存储后的文件id")
    private String id;

    @Schema(description = "文件md5")
    private String md5;

    @Schema(description = "文件名")
    private String name;

    @Schema(description = "上传状态")
    private String status;

    @Schema(description = "随机序列id")
    private String uid;

    @Schema(description = "是否删除")
    private Integer removeFlag;

    @Schema(description = "响应对象")
    private Map<String,Object> response;

    @Schema(description = "文件大小")
    private Integer size;

    @Schema(description = "文件url")
    private String url;

    @Schema(description = "头像url")
    private String avatarUrl;

    @Schema(description = "文件存储类型")
    private Integer type;

    /**
     * 获得文件的路径
     * @return
     */
    public String getPath() {
        if (response == null || response.isEmpty()) {
            return "";
        }

        String[] items = {SpringFactory.getBean(ISettingExtService.class).getTempDir(), "uploader", "uploaddir", this.getResponseId()};

        return StringUtils.join(items, File.separator);
    }

    /**
     * 获取response对象中的id
     * @return
     */
    public String getResponseId () {
        return MapUtils.getString(response,"id", "");
    }

    /**
     * 设置response对象中的id
     * @param uniqueName
     */
    public void setResponseId (String uniqueName) {
        if (null == response) {
            response = new HashMap<>();
        }
        response.put("id", uniqueName);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getRemoveFlag() {
        return removeFlag;
    }

    public void setRemoveFlag(Integer removeFlag) {
        this.removeFlag = removeFlag;
    }

    public Map<String,Object> getResponse() {
        return response;
    }

    public void setResponse(Map<String,Object> response) {
        this.response = response;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAvatarUrl() {
        if (StringUtils.isNotBlank(avatarUrl)) {
            return EncryptionAlgorithmFactory.getSymmetryEncryption()
                    .decrypt(avatarUrl);
        }
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    @Override
    public String toString() {
        return "UploadFile{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status='" + status + '\'' +
                ", uid='" + uid + '\'' +
                ", removeFlag=" + removeFlag +
                ", response=" + response +
                ", size=" + size +
                ", url='" + url + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", type=" + type +
                '}';
    }
}
