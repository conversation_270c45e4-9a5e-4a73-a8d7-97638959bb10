package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 表格管理视图 - 返回DTO
 *
 * <AUTHOR>
 * date 2023-07-13
 */
@Data
@Schema(description = "表格管理视图 - 返回DTO")
public class TableViewManageRespDTO {

    @Schema(description = "展示的视图")
    private List<TableViewRespDTO> showList;

    @Schema(description = "隐藏的视图")
    private List<TableViewRespDTO> hideList;
}
