package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/3/10
 */
@Data
@Schema(description = "日程详情dto")
public class CalendarEventDetailDTO {

    @Schema(description = "日程标题")
    private String title;

    @Schema(description = "日历日期")
    private Date calendarDate;

    @Schema(description = "日历类型")
    private String[] typeList;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "类型")
    private String selectType;

}
