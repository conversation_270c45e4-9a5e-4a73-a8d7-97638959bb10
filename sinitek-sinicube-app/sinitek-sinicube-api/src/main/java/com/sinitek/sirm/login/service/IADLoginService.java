package com.sinitek.sirm.login.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * AD域登录-接口层
 *
 * <AUTHOR>
 * date 2025-04-10
 */
public interface IADLoginService {

    /**
     * 请求经过身份验证拦截器并且身份验证未通过时，执行如下方法尝试域免登录
     * @param request
     * @param response
     */
    boolean adAutoLogin(HttpServletRequest request, HttpServletResponse response);
}
