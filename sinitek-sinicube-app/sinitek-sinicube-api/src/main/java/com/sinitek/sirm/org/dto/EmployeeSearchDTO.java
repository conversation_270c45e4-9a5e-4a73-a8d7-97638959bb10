package com.sinitek.sirm.org.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

/**
 * 员工查询
 * User: 王志华
 * Date: 2018/3/18
 * Time: 下午10:30
 * To change this template use File | Settings | File Templates.
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "员工查询条件")
@Data
public class EmployeeSearchDTO extends PageDataParam {
    @Schema(description = "用户名，员工姓名，员工姓名拼音简称")
    private String jointName;

    @Schema(description = "工作地")
    private String city;

    @Schema(description = "模糊登录名")
    private String userName;

    @Schema(description = "精确登录名")
    private String jquserName;

    @Schema(description = "模糊员工姓名，拼音简称")
    private String empName;

    @Schema(description = "精确员工姓名")
    private String jqEmpName;

    @Schema(description = "是否在职")
    private String inservice;

    @Schema(description = "手机号")
    private String mobilePhone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "未分配岗位人员")
    private boolean unUser = false;

    @Schema(description = "人员orgid列表")
    private String empIds;

    @Schema(description = "用户名称集合：多个用户名称，逗号分隔")
    private String userNames;

    @Schema(description = "用户Id集合:多个用户Id，逗号分隔")
    private String userIds;

    @Schema(description = "所属组织结构id列表")
    private List<String> unitIds;

    @Schema(description = "选人方案代码")
    private String schemeCode;

    @Schema(description = "选人方案范围")
    private Boolean schemeRange;

    @Schema(description = "选人方案角色id列表")
    private Set<String> schemeRoleIds;

    @Schema(description = "选人方案小组id列表")
    private Set<String> schemeTeamIds;

    @Schema(description = "选人方案岗位id列表")
    private Set<String> schemePostIds;

    @Schema(description = "选人方案部门id列表")
    private Set<String> schemeDepartmentIds;

    @Schema(description = "选人方案人员orgid列表")
    private Set<String> schemeEmpIds;

    @Schema(description = "选人方案条件总量")
    private Integer schemeOrgCount;

    @Schema(description = "选人控件上不展示的人员，存放orgid")
    private List<String> excludeEmpIds = new ArrayList<>();

    @Schema(description = "所属组织结构的具体类型")
    private Integer orgType;

    @Schema(description = "过期开始时间")
    private Date expireTimeStart;

    @Schema(description = "过期结束时间")
    private Date expireTimeEnd;

    @Schema(description = "是否查询机构已删除用户")
    private Boolean isSearchDelTenantUser = false;

    @Schema(description = "提供给外部扩展参数")
    private Map<String, Object> extendMap;

    @Schema(description = "用户来源")
    private String dataSrc;

    @Schema(description = "是否是默认的用户来源(系统中用户来源为空的数据,也归属于默认来源)")
    private Boolean defaultDataSrcFlag = false;

    @Schema(description = "选中部门Id列表(多个逗号分割)")
    private String selectDeptIds;

    @Schema(description = "选中角色Id列表(多个逗号分割)")
    private String selectRoleIds;

    @Schema(description = "选中岗位Id列表(多个逗号分割)")
    private String selectPostIds;

    @Schema(description = "选中小组Id列表(多个逗号分割)")
    private String selectTeamIds;

    @Schema(description = "选中员工Id列表(多个逗号分割)")
    private String selectEmpIds;

    @Schema(description = "选中组织结构Id列表(多个逗号分割)")
    private String selectOrgIds;

    @Schema(description = "是否开启用户选择历史记录")
    private boolean enableUserSelectionHistory;

    @Schema(description = "记录场景")
    private String scene;

    @Schema(description = "当前用户的OrgId")
    private String currentUserOrgId;

    @Schema(description = "小组,角色作为查询条件")
    private boolean groupOrRoleAsCondition;

    @Schema(description = "选中部门Id列表")
    private List<String> selectDeptIdList;

    @Schema(description = "选中角色Id列表")
    private List<String> selectRoleIdList;

    @Schema(description = "选中岗位Id列表")
    private List<String> selectPostIdList;

    @Schema(description = "选中小组Id列表")
    private List<String> selectTeamIdList;

    @Schema(description = "选中员工Id列表")
    private List<String> selectEmpIdList;

    @Schema(description = "选中组织结构Id列表")
    private List<String> selectOrgIdList;
}
