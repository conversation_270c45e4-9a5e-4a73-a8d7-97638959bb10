package com.sinitek.sirm.common.sirmenum.support;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.support.data.BaseSingleFieldTableResultFormat;
import com.sinitek.sirm.sirmenum.service.IEnumService;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * User: 王志华
 *
 * Date: 2020/2/7
 *
 * Time: 11:40 下午
 */
public class EnumTableResultFormat<T> extends BaseSingleFieldTableResultFormat<T> {

    /**
     * 枚举分类
     */
    private String enumCatalog = null;
    /**
     * 枚举类型
     */
    private String enumType = null;

    public EnumTableResultFormat(String formattingFieldName,
        String formatedFieldName, String enumCatalog, String enumType) {
        super(formattingFieldName, formatedFieldName);

        this.enumCatalog = enumCatalog;
        this.enumType = enumType;
    }

    @Override
    protected Map getDataMappingByFormattingFieldValues(List formattingFieldValues) {
        return SpringFactory.getBean(IEnumService.class).getSirmEnumByCataLogAndType(this.enumCatalog, this.enumType);
    }
}
