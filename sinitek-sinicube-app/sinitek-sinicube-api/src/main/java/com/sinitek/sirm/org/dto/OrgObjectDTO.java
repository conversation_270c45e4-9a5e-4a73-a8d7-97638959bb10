package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * File Desc:
 * Product Name:
 * Module Name:
 * Author:      王志华
 * History:     11-6-25 上午9:32
 */
@Schema(description = "组织结构信息描述模型")
public class OrgObjectDTO implements java.io.Serializable {

    @Schema(description = "组织结构对象id")
    private String orgId = null;

    @Schema(description = "orgName")
    private String orgName = null;

    @Schema(description = "组织结构类型")
    private int orgType = 0;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "所属机构Id")
    private String tenantId;

    @Schema(description = "Org为员工下有效,标识用户是否在职")
    private Boolean inservice;

    @Schema(description = "用户登录名称,um_userinfo.username字段")
    private String userName;

    @Schema(description = "用户Id")
    private String userId;

    public OrgObjectDTO(){}

    public OrgObjectDTO(String orgId, int orgType) {
        this.orgId = orgId;
        this.orgType = orgType;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public int getOrgType() {
        return orgType;
    }

    public void setOrgType(int orgType) {
        this.orgType = orgType;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Boolean getInservice() {
        return inservice;
    }

    public void setInservice(Boolean inservice) {
        this.inservice = inservice;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
