package com.sinitek.sirm.user.dto;

import com.sinitek.sirm.common.validator.annotation.Email;
import com.sinitek.sirm.common.validator.annotation.EmpName;
import com.sinitek.sirm.common.validator.annotation.Username;
import com.sinitek.sirm.common.validator.annotation.UsernameStart;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.tenant.support.TenantIdParam;
import com.sinitek.sirm.user.message.UserMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.Map;

/**
 * User: wyzhang
 * Date: 2018/3/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户信息描述模型")
public class UserInfoDTO extends TenantIdParam {

    @Schema(description = "类型")
    private String type = null;

    @Schema(description = "用户ID")
    private String orgid = null;

    @UsernameStart(message = "{"+ UserMessageCode.USERNAME_CAN_NOT_STAR_WITH +"}")
    @Username(message = "{"+ UserMessageCode.USERNAME_CHECK +"}")
    @NotBlank(message = "{"+ UserMessageCode.LOGIN_NAME_CAN_NOT_NULL +"}")
    @Schema(description = "登陆名")
    @Length(max = 50, message = "{"+ UserMessageCode.LOGIN_NAME_LENGTH_TOO_BIG +"}")
    private String username = null;

    @NotBlank(message = "{"+ UserMessageCode.EMP_NAME_CAN_NOT_NULL +"}")
    @Schema(description = "员工名称")
    @EmpName(message = "{"+ UserMessageCode.EMP_NAME_CHECK +"}")
    @Length(max = 30, message = "{"+ UserMessageCode.EMP_NAME_LENGTH_TOO_BIG +"}")
    private String empname = null;

    @Schema(description = "员工姓名拼音")
    @Length(max = 200, message = "{"+ UserMessageCode.EMP_NAME_PY_LENGTH_TOO_BIG +"}")
    private String empnamepy = null;

    @Schema(description = "性别")
    private Integer sex = null;

    @Email(message = "{"+ UserMessageCode.USER_EMAIL_CHEK +"}")
    @Schema(description = "邮箱")
    @Length(max = 100, message = "{"+ UserMessageCode.EMAIL_LENGTH_TOO_BIG +"}")
    private String email = null;

    @Schema(description = "办公电话")
    @Length(max = 30, message = "{"+ UserMessageCode.OFFICE_PHONE_LENGTH_TOO_BIG +"}")
    private String tel = null;

    @Schema(description = "手机")
    @Length(max = 30, message = "{"+ UserMessageCode.PHONE_LENGTH_TOO_BIG +"}")
    private String mobile = null;

    @Schema(description = "职位")
    @Length(max = 30, message = "{"+ UserMessageCode.POSITION_LENGTH_TOO_BIG +"}")
    private String job = null;

    @Schema(description = "在职状态")
    private Integer inservice = null;

    @Schema(description = "入职日期")
    private Date entrydate = null;

    @Schema(description = "离职日期")
    private Date leavedate = null;

    @Schema(description = "工作地")
    private Integer workplace = null;

    @Schema(description = "个人简介")
    @Length(max = 100, message = "{"+ UserMessageCode.RESUME_NAME_LENGTH_TOO_BIG +"}")
    private String introduction = null;

    @Schema(description = "所属岗位")
    private String postids = null;

    @Schema(description = "所属角色")
    private String roleids = null;

    @Schema(description = "所属小组")
    private String teamids = null;

    @Schema(description = "行政上级")
    private String superior = null;

    @Schema(description = "头像")
    private UploadDTO photo;

    @Schema(description = "签名")
    private UploadDTO sign;

    @Schema(description = "导入文件")
    private UploadDTO importFile;

    @Schema(description = "导入方式")
    private String importType;

    @Schema(description = "到期日期")
    private Date expireTime;

    @Schema(description = "用户源")
    private String dataSrc;

    @Schema(description = "用户源objid")
    private String origId;

    @Schema(description = "自定义表单参数Json")
    private String extendMapStr;

    @Schema(description = "自定义表单参数Map")
    private Map<String, Object> extendMap;
}
