package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Schema(description = "系统消息重发DTO")
@Data
public class SirmSendMessageResendDTO {

    @Schema(description= "系统消息id")
    private Long sendMessageId;

    @Schema(description= "系统消息详情ids")
    private List<Long> sendMessageDetailIds;

    @Schema(description= "所有的收件人是否都重新发送；true：都发送；false：只发送状态为失败的")
    private Boolean allSendFlag = false;

    @Schema(description= "数据来源id")
    private Long sourceId;

    @Schema(description= "数据来源实体")
    private String sourceEntity;
}
