package com.sinitek.sirm.routine.holiday.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;


/**
 * 节假日数据对象
 *
 * <AUTHOR>
 * @date 2021/4/27
 */
@Schema(description = "节假日信息")
@Data
public class HolidayDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "节假日保存人")
    private String inputId;

    @Schema(description= "年份")
    private Integer year;

    @Schema(description= "节假日期")
    private Date holidayDate;

}
