package com.sinitek.sirm.routine.holiday.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/10/8
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询节假日方案列表数据模型")
public class HolidaySchemeQueryDTO extends PageDataParam {

    @Schema(description = "方案编码")
    private String code;

    @Schema(description = "方案名称")
    private String name;

    @Schema(description= "是否为默认值（0：否，1；是）")
    private Integer defaultFlag;
}
