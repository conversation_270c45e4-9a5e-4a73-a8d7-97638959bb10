package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 表格视图条件组-返回DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图条件组-返回DTO")
public class TableViewConditionGroupRespDTO extends TableViewConditionGroupBaseDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "条件列表")
    private List<TableViewConditionRespDTO> conditionList;
}
