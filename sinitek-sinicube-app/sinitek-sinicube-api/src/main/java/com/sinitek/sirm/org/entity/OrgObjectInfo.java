package com.sinitek.sirm.org.entity;

import com.sinitek.sirm.org.dto.EmpParentPositionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: com.sinitek.sirm.org.busin.entity
 * Author:      潘虹
 * History:     11-7-26 created by 潘虹
 */
@Data
public class OrgObjectInfo {

    @Schema(description = "父岗位列表")
    private List<EmpParentPositionDTO> positionList;

     /**
     * 部门
     */
    private String unitname;

    /**
     * 岗位名称
     */
    private String positionname;
    /**
     * 小组名称
     */
    private String teamname;

    /**
     * 角色名称
     */
    private String rolename;
}
