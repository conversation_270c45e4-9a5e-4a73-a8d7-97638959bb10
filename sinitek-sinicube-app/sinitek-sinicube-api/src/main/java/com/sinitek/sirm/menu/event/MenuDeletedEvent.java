package com.sinitek.sirm.menu.event;

import com.sinitek.sirm.common.event.annotation.EventDefinition;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.menu.dto.MenuDTO;

/**
 * <AUTHOR>
 * @date 2021/6/2
 * 删除菜单事件模型
 */
@EventDefinition(type = "SiniCube", module = "菜单管理", name = "菜单删除事件", brief = "当删除菜单的时候会触发")
public class MenuDeletedEvent extends SiniCubeEvent<MenuDTO> {

    public MenuDeletedEvent(MenuDTO source) {
        super(source);
    }
}
