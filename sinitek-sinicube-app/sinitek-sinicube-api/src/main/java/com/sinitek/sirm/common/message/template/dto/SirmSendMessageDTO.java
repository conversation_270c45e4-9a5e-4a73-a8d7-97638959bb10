package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/13
 */
@Data
@Schema(description = "系统消息DTO")
public class SirmSendMessageDTO {

    @Schema(description= "发送状态")
    private Integer status;

    @Schema(description=  "消息标题")
    private String title;

    @Schema(description=  "消息类型")
    private Integer type;

    @Schema(description=  "定时发送标识")
    private Integer isTime;

    @Schema(description=  "发送时间")
    private Date sendTime;

    @Schema(description=  "消息内容")
    private String content;

    @Schema(description=  "发送人Id")
    private String senderId;

    @Schema(description=  "发送人名称")
    private String senderName;

    @Schema(description=  "流程审批人")
    private Long exampleownerid;

    @Schema(description=  "模板类型")
    private Integer templatetypeid;

    @Schema(description=  "消息重要度")
    private Integer importantLevel;

    @Schema(description=  "来源记录id")
    private Long sourceId;

    @Schema(description=  "来源记录名称")
    private String sourceEntity;

    @Schema(description=  "发送邮件地址")
    private String mailFrom;

    @Schema(description=  "多租户id")
    private String tenantId;

    @Schema(description= "主键")
    private Long objId;

    @Schema(description= "实体类")
    private String entityName;

}
