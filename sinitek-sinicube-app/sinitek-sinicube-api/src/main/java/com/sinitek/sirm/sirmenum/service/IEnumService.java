package com.sinitek.sirm.sirmenum.service;

import com.sinitek.cloud.sirmapp.enumeration.dto.SirmEnumDTO;
import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;
import com.sinitek.sirm.sirmenum.dto.TypeCataLogDTO;

import java.util.List;
import java.util.Map;

/**
 * 统一枚举方法
 *
 * <AUTHOR>
 * @date 2021/6/8
 */
public interface IEnumService {

    /**
     * 根据 枚举分类 和 枚举类型 获取对应的枚举集合
     *
     * @param cataLog 枚举分类
     * @param type 枚举类型
     * @return key为 枚举名称 value为 枚举值
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<String, String> getSirmEnumByCataLogAndType(String cataLog, String type);

    /**
     * 根据 枚举分类 和 枚举类型 获取对应的枚举集合
     *
     * @param cataLog 枚举分类
     * @param type 枚举类型
     * @return key为 枚举名称 value为 枚举值
     */
    List<SirmEnumDTO> findSirmEnumByCataLogAndTypeObj(String cataLog, String type);

    /**
     * 根据条件获取单个枚举
     * @param cataLog 模块
     * @param type    数据类型
     * @param value   值
     * @return 单个枚举对象
     *
     */
    SirmEnumDTO getSirmEnumName(String cataLog, String type, Integer value);

    /**
     * 根据条件获取单个枚举
     * @param cataLog　模块
     * @param type　数据类型
     * @param strValue　值
     * @return 单个枚举对象
     *
     */
    SirmEnumDTO getSirmEnum(String cataLog, String type, String strValue);

    /**
     * 保存枚举
     * @param sirmEnumDTO   枚举
     */
    Long saveSirmEnum(SirmEnumDTO sirmEnumDTO);

    /**
     * 根据模块名和类型获取枚举列表
     * @param catalog
     * @param type
     * @return
     */
    public List<SirmEnumDTO> findSirmEnumList(String catalog, String type);

    /**
     * 批量查询枚举分类
     * @param sirmEnumDtos
     * @return
     */
    List<SirmEnumDTO> findSirmEnum(List<TypeCataLogDTO> sirmEnumDtos);

    /**
     * 删除枚举表信息
     * @param objid　
     */
    public void delSirmEnumById(Long objid);


    /**
     * 清除enumCache, cataLog、type,value 一组key
     * @param cataLog
     * @param type
     * @param value
     */
    public void evictEnumCacheWithCataLogAndTypeAndValue(String cataLog, String type, Integer value);

    /**
     * 清除enumCache, cataLog、type,value 一组key
     * @param cataLog
     * @param type
     */
    public void evictEnumCacheWithCataLogAndType(String cataLog, String type);

}
