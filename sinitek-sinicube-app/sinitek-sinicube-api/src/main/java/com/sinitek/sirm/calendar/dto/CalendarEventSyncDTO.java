package com.sinitek.sirm.calendar.dto;

import com.sinitek.sirm.calendar.support.ISyncCalendarEventHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Date  2022/3/12
 */
@Data
public class CalendarEventSyncDTO {

    @Schema(description = "软件类型")
    private int type;


    @Schema(description = "软件类型名称的国际化编码")
    private String i18nCode;


    @Schema(description = "排序")
    private int sort;


    @Schema(description = "数据处理类")
    private ISyncCalendarEventHandler handler;
}
