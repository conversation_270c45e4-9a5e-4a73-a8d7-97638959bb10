package com.sinitek.sirm.calendar.support;

import com.sinitek.sirm.calendar.dto.CalendarEventSyncDeleteDTO;
import com.sinitek.sirm.calendar.dto.CalendarEventSyncSourceDTO;

/**
 * <AUTHOR>
 * Date  2022/3/12
 */
public interface ISyncCalendarEventHandler {

    String save(CalendarEventSyncSourceDTO dto);

    void update(CalendarEventSyncSourceDTO dto);

    void delete(CalendarEventSyncDeleteDTO dto);
}
