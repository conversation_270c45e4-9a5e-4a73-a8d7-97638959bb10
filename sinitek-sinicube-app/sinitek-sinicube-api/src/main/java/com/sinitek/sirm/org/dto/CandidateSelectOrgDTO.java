package com.sinitek.sirm.org.dto;

import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 选人控件-选中组织结构-DTO层
 *
 * <AUTHOR>
 * date 2023-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "选人控件-选中组织结构-DTO层")
public class CandidateSelectOrgDTO extends TenantIdParam {

    @Schema(description = "选中部门Id列表")
    private List<String> selectDeptIds;

    @Schema(description = "选中角色Id列表")
    private List<String> selectRoleIds;

    @Schema(description = "选中岗位Id列表")
    private List<String> selectPostIds;

    @Schema(description = "选中小组Id列表")
    private List<String> selectTeamIds;

    @Schema(description = "选中员工Id列表")
    private List<String> selectEmpIds;

    @Schema(description = "选中组织结构Id列表")
    private List<String> selectOrgIds;

    @Schema(description = "是否为限制组织结构的模式")
    public boolean selectOrgFlag = false;

    @Schema(description = "提供给外部扩展参数")
    private Map<String, Object> extendMap;
}
