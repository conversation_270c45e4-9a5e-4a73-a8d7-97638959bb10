package com.sinitek.sirm.org.dto;

import com.sinitek.sirm.org.message.RoleMessageCode;
import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * User: wyzhang
 * Date: 2018/5/10.
 */
@Data
@Schema(description = "角色信息参数")
@EqualsAndHashCode(callSuper = true)
public class RoleDTO extends TenantIdParam {
    /**
     * 角色ID
     */
    @Schema(description = "角色ID")
    private String id = null;

    /**
     * 角色名称
     */
    @NotBlank(message = "{"+ RoleMessageCode.ROLE_NAME_IS_BLANK + "}")
    @Length(max = 30, message = "{"+ RoleMessageCode.ROLE_NAME_LENGTH_TOO_BIG + "}")
    @Schema(description = "角色名称")
    private String name = null;

    /**
     * 角色描述
     */
    @Length(max = 100, message = "{"+ RoleMessageCode.ROLE_DESC_LENGTH_TOO_BIG + "}")
    @Schema(description = "角色描述")
    private String description = null;

    @Schema(description = "角色成员id集合")
    private String empIds = null;

    @Schema(description = "角色成员名称集合")
    private String emps = null;
}
