package com.sinitek.sirm.framework.frontend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 附件列表查询DTO
 *
 * <AUTHOR>
 * date 2023-12-05
 */
@Data
@Schema(description = "附件列表查询DTO")
public class UploadSearchDTO {

    @Schema(description = "来源Id")
    private String sourceId;

    @Schema(description = "来源名称")
    private String sourceEntity;

    @Schema(description = "附件类型")
    private List<Integer> typeList;
}
