package com.sinitek.sirm.common.message.template.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/2
 * 接收消息展示DTO
 */
@Data
@Schema(description = "接收消息展示DTO")
public class ReceiveMessageDTO {

    @Schema(description = "消息id")
    private Long objId;

    @Schema(description = "发送人id")
    private Long sender;

    @Schema(description = "发送人姓名")
    private String senderName;

    @Schema(description = "消息标题")
    private String title;

    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date sendDate;

    @Schema(description = "状态key")
    private Integer status;

    @Schema(description = "总数")
    private Integer count = 0;

    @Schema(description = "接收者id")
    private Long receiveId;

    @Schema(description = "回复消息的id")
    private Long replyMessageId;

    @Schema(description = "重要程度")
    private Integer importantLevel;

    @Schema(description = "接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date receiveDate;

    @Schema(description = "状态值")
    private String statusValue;

    @Schema(description = "Org为员工下有效,标识用户是否在职")
    private String inservice;

    @Schema(description = "发送者")
    private String senderKey;


}
