package com.sinitek.sirm.org.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.sonar.IgnoreMapCheck;
import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.org.dto.*;
import com.sinitek.sirm.org.entity.*;
import com.sinitek.sirm.org.enumerate.DepartmentEmployeeQueryScopeEnum;
import com.sinitek.sirm.tenant.annotation.TenantFilterShutDown;
import com.sinitek.spirit.org.core.dto.EmployeeDTO;
import com.sinitek.spirit.org.core.dto.OrgSpiritObjectDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * File Desc: 组织结构接口 凡是出现用户编号[部门编号，岗位编号，小组编号 指的都是orgid字段]
 * Product Name: SIRM
 * Module Name: indicator
 * Author:      潘虹 李琦明
 * History:     11-4-29 created by 潘虹
 */
public interface IOrgService {

    /**
     * 根据 EmployeeSearchDTO返回员工列表,员工对象为 UserInfoDetailedDataDTO
     *
     * @param dto      员工查询条件
     * @return
     * 根据 EmployeeSearchDTO返回员工列表,员工对象为 UserInfoDetailedDataDTO
     */
    TableResult<UserInfoDetailedDataDTO> findEmployeeList(EmployeeSearchDTO dto);

    /**
     * 根据组织结构ID和类型查询组织结构对象
     *
     * @param candidateTransDTO    候选人选中查询DTO
     * @return
     * 根据组织结构ID和类型查询组织结构对象
     */
    List<CandidateResultDTO> candidateTrans(CandidateTransDTO candidateTransDTO);

    /**
     * 公共方法，选人控件上搜索查询组织结构列表
     * @param param 查询参数
     * @return
     */
    List<OrgAutoCompleteDTO> findCandidateList (CandidateParamDTO param);

    /**
     * 根据组织结构IdList获取对应的组织结构名称Map
     * Map key : orgId
     * Map value : orgName  Example: (管理员)
     *
     * @param orgIdList   用户id集合
     * @return
     * 根据组织结构IdList获取对应的组织结构名称Map
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<String,String> getOrgNameMapByOrgIdList(List<String> orgIdList);

    /**
     * 根据组织结构IdList获取对应的组织结构Map
     * Map key : orgId
     * Map value : OrgObject
     * @param orgIdList   用户id集合
     * @return
     * 根据组织结构IdList获取对应的组织结构Map
     */
    @IgnoreMapCheck
    Map<String, OrgObjectDTO> getOrgMapByOrgIdList(List<String> orgIdList);

    /**
     * 根据 userIdList获取对应的组织结构名称Map
     * Map key : userId
     * Map value : orgName
     *
     * @param userIdList   用户id集合
     * @return
     * 根据组织结构IdList获取对应的组织结构名称Map
     */
    @IgnoreMapCheck
    Map<String, String> getOrgNameMapByUserIdList(List<String> userIdList);


    /**
     * 根据 userId 获取对应的组织结构
     * @param userId     用户userid
     * @return
     * 根据 userId 获取对应的组织结构
     */
    OrgObjectDTO getEmpOrgObjectByUserId(String userId);


    /**
     * 根据用户IdList获取对应用户所在的角色,
     * Map key : orgId
     * Map value : 角色名称  Example: (管理员,测试人员)
     *
     * @param orgIdList     用户id集合
     * @return
     * 根据用户IdList获取对应用户所在的角色,
     */
    @IgnoreMapCheck
    Map<String,String> getRoleNameMapByOrgIdList(List<String> orgIdList);

    /**
     * 查询所有用户
     * @return
     * 返回用户列表，不会返回null
     */
    List<Employee> findAllEmployees();

    /**
     * 根据指定参数查询用户列表
     * @param params 键值对参数:可以为null,返回um_userinfo中username的首字母不为$的用户；其中params的key可以是jointname,city,username,jqusername,empname,jqEmpName,inservice,userid,unUser,strutureid,
     *               userids,orgids,usernames
     * @return
     *   返回用户列表
     */
    List<Employee> findAllEmployees(EmployeeSearchDTO params);

    /**
     * 根据tenantId获取机构下面的所有人员
     * @param tenantIds 机构列表
     * @return 返回用户列表:返回值不为null
     */
    List<Employee> findAllEmployeesByTenantIds(List<String> tenantIds);

    /**
     * 获取所有在职员工
     * @return
     *   返回用户列表
     */
    List<Employee> findAllEmployeesInSerivce();

    /**
     * 查询用户基本信息 ,分页显示
     * @param params   员工查询条件
     * @return
     * 返回查询用户基本信息 ,分页显示
     */
    IPage<Employee> searchEmployees(EmployeeSearchDTO params);

    /**
     * 根据不同的选人方案实现，获得对应的用户
     * @param dto
     * @param param   员工查询条件
     * @return
     * 返回查询用户基本信息 ,分页显示
     */
    List<Employee> findEmployees(EmployeeSearchDTO dto, Map<String,Object> param);

    /**
     * 查询角色下的用户基本信息 ,分页显示, 不进行机构拦截
     * @param params   员工查询条件
     * @return
     * 返回查询角色下的用户基本信息 ,分页显示, 不进行机构拦截
     */
    IPage<Employee> searchRoleEmployees(EmployeeSearchDTO params);


    /**
     * 通过用户编号查询用户
     * @param userid 用户userid
     * @return
     * 用户:当没有对应的用户时返回null
     */
    Employee getEmployeeByUserId(String userid);

    /**
     * 通过用户编号批量查询用户
     * @param userids 用户userid
     * @return  Employee 用户:当没有对应的用户时返回null
     */
    List<Employee> findEmployeesByUserIds(List<String> userids);

    /**
     * 通过用户登录名查询用户
     * @param username 用户名:对应的数据库列为：jqusername
     * @return
     * 用户:当没有对应的用户时返回null
     */
    @TenantFilterShutDown
    Employee getEmployeeByUserName(String username);

    /**
     * 根据用户id查询用户
     * @param orgId 用户编号
     * @return
     * 返回null没有对应的用户
     */
    Employee getEmployeeById(String orgId);

    /**
     * 通过用户姓名查询用户
     * @param empName 用户名称
     * @return
     * 返回用户列表:不会为null
     */
    List<Employee> findEmployeeByEmpName(String empName);

    /**
     * 通过用户姓名查询在职用户
     * @param empName 用户名称
     * @return
     * 返回用户列表:不会为null，size=0
     */
    List<Employee> findEmployeeInserviceByEmpName(String empName);

    /**
     * 根据部门编号查询用户列表
     * @param unitId 部门编号：对应的数据列strutureid:
     * @return
     * 返回用户列表：不返回null
     */
    List<Employee> findEmployeeByUnitId(String unitId);

    /**
     * 根据部门编号查询查询部门下员工，不包括下级部门员工
     * @param unitId 部门id
     * @return 用户信息
     */
    List<Employee> findDirectEmployeeByUnitId(String unitId);

    /**
     * 根据部门编号查询在职用户列表
     * @param unitId 部门编号：对应的数据列strutureid:
     * @return
     * 返回用户列表
     */
    List<Employee> findEmployeeInserviceByUnitId(String unitId);

    /**
     * 根据小组编号查询小组长(一个小组只能有一个小组长)
     * @param teamId 小组编号
     * @return EmployeeDTO
     *返回小组长;找不到就返回null
     */
    EmployeeDTO getTeamLeaderByTeamId(String teamId);

    /**
     * 根据小组编号查询在职小组长列表
     * @param teamId 小组编号
     * @return EmployeeDTO
     * 返回在职的小组长;找不到或者离职了就返回null
     */
    EmployeeDTO getTeamLeaderInserviceByTeamId(String teamId);

    /**
     * 根据岗位编号查询用户列表
     * @param postionId 岗位编号：对应的数据列strutureid:可以为null，返回strutureid=null的对象
     * @return
     * 返回用户列表：不返回null
     *
     */
    List<Employee> findEmployeeByPosId(String postionId);

    /**
     * 根据岗位编号查询在职用户列表
     * @param postId 岗位编号：对应的数据列strutureid:可以为null，返回strutureid=null的对象
     * @return
     * 返回用户列表：不返回null
     *
     */
    List<Employee> findEmployeeInserviceByPosId(String postId);

    /**
     * 查出所有未分配用户
     * @return
     * 返回未分配用户列表：不会为null
     */
    List<EmployeeDTO> findUnallocatedEmployee();

    /**
     * 查出所有未分配在职用户
     * @return  未分配用户列表：不会为null
     * 返回所有未分配在职用户
     */
    List<EmployeeDTO> findUnallocatedEmployeeInservice();

    /**
     * 根据组织结构类型和组织结构id查询用户列表
     * @param orgType 组织结构类型：如果orgtype=0则默认为系统
     * @param orgId   组织结构编号：岗位，部门，角色，小组对于的数据库列为strutureid
     * @return
     * 返回用户列表
     */
    List<Employee> findEmployeesByOrgTypeOrgId(int orgType, String orgId);

    /**
     * 根据组织结构类型和组织结构编号查询在职用户列表
     * @param orgType 组织结构类型：如果orgtype=0则默认为系统
     * @param orgId   组织结构编号：岗位，部门，角色，小组对于的数据库列为strutureid
     * @return
     * 返回用户列表
     */
    List<Employee> findEmployeesInserviceByOrgTypeOrgId(int orgType, String orgId);

    /**
     * 根据组织结构编号查询用户列表
     * @param orgId 组织结构编号：orgid不能为null或""
     * @return
     * 返回用户列表
     */
    List<Employee> findEmployeesByOrgId(String orgId);

    /**
     * 根据组织结构编号查询在职用户列表
     *
     * @param orgId 组织结构编号：orgid不能为null或""
     * @return 返回用户列表
     */
    List<Employee> findEmployeesInserviceByOrgId(String orgId);

    /**
     * 根据组织结构编号查询离职用户列表
     *
     * @param orgId 组织结构编号：orgid不能为null或""
     * @return List<Employee> 用户列表
     */
    List<Employee> findEmployeesResignedByOrgId(String orgId);

    /**
     * 根据小组查询用户列表
     *
     * @param teamId 小组编号
     * @return 返回用户列表:返回值不为null
     */
    List<Employee> findEmployeeByTeamId(String teamId);

    /**
     * 根据小组查询在职用户列表
     * @param teamId 小组编号
     * @return
     * 用户列表:返回值不为null
     */
    List<Employee> findEmployeeInserviceByTeamId(String teamId);

    /**
     * 分页查询在线用户对应信息
     * @param dto   当前在线人员列表
     * @return
     * 返回分页查询在线用户对应信息
     */
    IPage<OnLineUserDTO> searchOnLineUsers(EmployeeSearchDTO dto);

    /**
     * 根据角色id查询用户列表
     * @param roleId 角色id
     * @return
     * 返回用户列表
     */
    List<Employee> findEmployeeByRoleId(String roleId);

    /**
     * 根据角色查询在职用户列表
     * @param roleId 角色编号
     * @return
     * 返回用户列表
     */
    List<Employee> findEmployeeInserviceByRoleId(String roleId);

    /**
     * 通过用户编号查询业务上级或行政上级下人员列表
     * @param empId        用户编号：不能为null
     * @param positionType 岗位类型枚举（业务上级，行政上级）
     * @return
     * 返回用户列表
     */
    List<Employee> findAllPosParentByEmpId(String empId, String positionType);

    /**
     * 通过用户编号查询业务上级或行政上级下在职人员列表
     * @param empId        用户编号：不能为null
     * @param positionType 岗位类型枚举（业务上级，行政上级）
     * @return
     * 返回用户列表
     */
    List<Employee> findAllPosParentInserviceByEmpId(String empId, String positionType);

    /**
     * 查询某个机构下，过期时间晚于机构过期时间的员工
     * @return
     */
    List<Employee> findEmpIdsByTenantId(EmployeeSearchDTO originParam);

    /**
     * 通过用户编号查询业务下级或行政下级下人员列表
     *
     * @param empId        用户编号
     * @param positionType 岗位类型枚举（业务上级，行政上级）
     * @return
     * 返回用户列表
     */
    List<Employee> findAllPosChildrenByEmpId(String empId, String positionType);

    /**
     * 通过用户编号查询业务下级或行政下级下在职人员列表
     * @param empId        用户编号
     * @param positionType 岗位类型枚举（业务上级，行政上级）
     * @return
     * 返回用户列表
     */
    List<Employee> findAllPosChildrenByEmpIdInservice(String empId, String positionType);

    /**
     * 获取员工信息
     * @param empIds    用户编号
     * @return
     * 返回获取员工信息
     */
    List<OrgObjectDTO> findEmpOrgObjectsByEmpIds(List<String> empIds);

    /**
     * 获取员工信息
     * @param empIds    用户编号
     * @return
     * 返回获取员工信息
     */
    List<EmployeeDTO> findEmpByEmpIds(List<String> empIds);

    /**
     * 根据 username 获取 UserCoreInfo
     * @param username     用户名
     * @return
     * 根据 username 获取 UserCoreInfo
     */
    UserCoreInfo getUserCoreInfoByUserName(String username);

    /**
     * 根据 userId 获取 UserCoreInfo
     * @param userId   用户userid
     * @return
     * 根据 userId 获取 UserCoreInfo
     */
    UserCoreInfo getUserCoreInfoByUserId(String userId);

    /**
     * 解锁用户
     * @param userid 用户userid
     */
    void unlockUserByUserId(String userid);

    /**
     * 根据部门编号返回直属部门的用户编号集合
     * @param unitId 部门编号
     * @return
     * 返回直属部门的用户编号集合
     */
    List<String> findUserIdsByUnitId(String unitId);

    /**
     * 根据岗位编号查询岗位
     * @param orgId 岗位编号:岗位编号不能为null
     * @return Position 岗位
     */
    Position getPositionById(String orgId);

    /**
     * 根据组织结构id，查询组织结构路径
     * @param orgId         组织结构id
     * @return  组织结构路径
     */
    String getPathByOrgId(String orgId);

    /**
     * 根据组织结构路径，查询岗位
     *
     * @param path      组织结构路径，从根节点开始，使用 '/'分隔
     * @return  岗位，无法匹配路径、不是岗位时返回null
     */
    Position getPositionByPath(String path);

    /**
     * 根据组织结构路径，查询部门
     *
     * @param path 组织结构路径，从根节点开始，使用 '/'分隔
     * @return 部门，无法匹配路径、不是部门时返回null
     */
    Department getDepartmentByPath(String path);

    /**
     * 根据小组编号查询小组
     * @param orgId 用户编号：用户编号不能为null
     * @return Team 小组：返回null时没有对应的小组
     */
    Team getTeamById(String orgId);

    /**
     * 根据部门编号查询部门
     * @param orgId 部门编号：部门编号不能为null
     * @return Department 部门：返回null没有对应的部门
     * 根据部门编号查询部门
     */
    Department getDepartmentById(String orgId);


    /**
     * 根据部门编号查询直属岗位列表
     * @param  unitId 部门编号：不能为null
     * @return  岗位列表：不会为null
     * 获取岗位列表
     */
    List<Position> findPositionsByUnitId(String unitId);

    /**
     * 根据部门编号查询直属部门
     * @param unitId 部门编号：不能为null
     * @return  部门列表
     * 获取部门列表
     */
    List<Department> findUnitByParentId(String unitId);

    /**
     * 根据组织结构编号查询组织结构
     *
     * @param id 组织结构编号
     * @return  OrgObject 组织结构：返回null时没有对于组织
     */
    OrgObjectDTO getOrgObjectById(String id);

    /**
     * 根据组织结构编号集合 查询 组织结构列表
     *
     * @param idList 组织结构编号集合
     * @return   组织结构列表：不会返回null
     * 根据组织结构编号集合 查询 组织结构列表
     */
    List<OrgObjectDTO> findOrgObjectsByOrgIds(List<String> idList);

    /**
     * 根据用户编号查出所在部门和岗位，角色，小组信息
     * @param ids 用户编号：数据库对于列toobjectid
     * @return OrgInfo 岗位部门集合：返回null时没有对于信息
     */
    @IgnoreMapCheck
    Map<String, OrgObjectInfo> getOrgInfoByEmpIds(List<String> ids);

    /**
     * 根据用户编号，查出该用户所有岗位
     * @param id 用户编号:不能为null
     * @return  岗位列表
     * 根据用户编号，查出该用户所有岗位
     */
    List<Position> findPositionsByEmpId(String id);

    /**
     * 根据用户编号，查出该用户所在部门(直属部门)
     * @param id 用户编号
     * @return  部门列表:返回值不为null
     * 根据用户编号，查出该用户所在部门(直属部门)
     */
    List<Department> findUnitsByEmpId(String id);

    /**
     * 根据用户编号，查出该用户所在小组
     * @param id 用户编号
     * @return 小组集合:返回值不为null
     */
    List<Team> findTeamsByEmpId(String id);

    /**
     * 根据部门编号查询父类部门
     * @param id 用户编号
     * @return Department 部门：当部门编号为null或“”或没有找到父时返回null
     */
    Department getParentDepartmentById(String id);

    /**
     * 根据岗位编号查询父类部门
     * @param positionId 岗位编号
     * @return Department 部门：当岗位编号为null或“”或没有找到父时返回null
     */
    Department getDepartmentByPositionId(String positionId);

    /**
     * 查询角色列表
     * @return  角色列表
     * 获取角色列表
     */
    List<Role> findAllRole();

    /**
     * 根据机构的orgId 获取到角色列表
     * @param mechanismOrgId  获取当前选中机构下的角色
     * @return
     * 根据机构的orgId 获取到角色列表
     */
    List<Role> findRolesByMechanismOrgId(String mechanismOrgId);

    /**
     * 根据角色编号查询角色
     * @param orgId 角色编号
     * @return Role 角色：当角色编号为null或“”或没有找到对应角色时返回null
     */
    Role getRoleById(String orgId);

    /**
     * 根据角色名称获取角色信息
     * @param name 角色名称
     * @return
     * 获取角色信息
     */
    List<Role> findRoleByName(String name);

    /**
     * 根据用户编号查询角色列表
     * @param empId 用户编号：不能为null
     * @return   角色列表
     * 根据用户编号查询角色列表
     */
    List<Role> findRolesByEmpId(String empId);

    /**
     * 根据用户编号查询多个机构的角色列表
     * @param empId 用户编号：不能为null
     * @return   角色列表
     * 根据用户编号查询角色列表
     */
    List<Role> findMultiTenantRolesByEmpId(String empId);

    /**
     * 通过用户编号获取所有父类组织结构
     * @param empId 用户编号:不能为null或“”
     * @return  组织结构列表
     * 通过用户编号获取所有父类组织结构
     */
    List<OrgObjectDTO> findAllOrgByEmpId(String empId);

    /**
     * 通过用户编号查询业务上级或行政上级列表
     * @param empId        用户编号
     * @param positionType 岗位类型枚举（业务上级，行政上级）
     * @return  用户列表
     * 通过用户编号查询业务上级或行政上级列表
     */
    List<OrgObjectDTO> findPosParentByEmpId(String empId, String positionType);

    /**
     * 验证该用户的岗位是否有业务上级或行政上级
     * @param empId        用户编号：当empid=null时返回false，不能为null
     * @param positionType 岗位类型枚举（业务上级，行政上级）
     * @return boolean true 有业务上级或行政上级 false 无业务上级或行政上级
     */
    boolean checkPosParentByEmpId(String empId, String positionType);


    /**
     * 根据组织结构编号查询父类集合
     * @param id 组织结构编号
     * @return  组织结构列表
     * 根据组织结构编号查询父类集合
     */
    List<OrgObjectDTO> findParentOrgById(String id);

    /**
     * 根据用户userid查询密码
     * @param userId 用户userid:对应数据库列userid
     * @return String 密码字符串：当userid=""时返回""
     */
    String getPwdByEmpUserId(String userId);


    /**
     * 根据组织结构编号查询所有上级组织结构编号列表
     * @param orgId 组织结构编号
     * @return  组织结构编号列表
     * 根据组织结构编号查询所有上级组织结构编号列表
     */
    List<OrgRelationPathDTO> findAllOrgParentIdByOrgId(String orgId);

    /**
     * 根据上级小组id查询下级所有小组
     * @param parentTeamId 小组编号:不能为null或""
     * @return  小组列表
     * 根据上级小组id查询下级所有小组
     */
    List<Team> findTeamsByParent(String parentTeamId);

    /**
     * 根据用户的编号获取他所负责的小组
     * @param orgId 组织机构编码：
     * @return  小组列表
     * 根据用户的编号获取他所负责的小组
     */
    List<Team> findTeamsByTeamLeaderId(String orgId);

    /**
     * 根据部门编号查询所有子类部门
     * @param unitId 部门编号：不能为null或""
     * @return  部门列表
     */
    List<Department> findUnitsByUnitId(String unitId);

    /**
     * 根据部门id集合获取子部门
     * @param unitIds    所属组织结构id列表
     * @return
     * 根据部门id集合获取子部门
     */
    List<Department> findUnitsByUnitIds(List<String> unitIds);

    /**
     * 查询系统所有的岗位
     * @return  岗位列表
     * 获取系统所有的岗位
     */
    List<Position> findAllPositions();

    /**
     * 检查小组名是否唯一
     * @param name    角色名称
     * @param orgId （添加传null）组织结构编号
     * @return
     * 获取count == 0 判断小组名是否唯一
     */
    boolean checkTeamNameUnique(String name,String orgId);

    /**
     * 获取角色名称
     * @param name 角色名称
     * @return
     * 获取角色名称
     */
    String getRoleIdByRoleName(String name);

    /**
     * 获取角色名称
     *
     * @param name
     * @param tenantId
     * @return
     */
    String getRoleIdByRoleNameAndTenantId(String name, String tenantId);

    /**
     * 获取小组名称
     * @param teamName 小组名称
     * @return
     * 获取小组名称
     */
    String getTeamIdByTeamName(String teamName);

    /**
     * 获取小组名称
     * @param teamName 小组名称
     * @return
     */
    String getTeamIdByTeamNameAndTenantId(String teamName, String tenantId);

    /**
     * 获取部门名称
     * @param deptName 部门名称
     * @return
     * 获取部门名称
     */
    String getDeptIdByDeptName(String deptName);

    /**
     * 岗获取位名称
     * @param postName 岗位名称
     * @return
     * 岗获取位名称
     */
    String getPostIdByPostName(String postName);

    /**
     * 获取组织结构名称
     * @param orgName 组织结构名称
     * @return
     * 获取组织结构名称
     */
    String getOrgIdByOrgName(String orgName);

    /**
     * 查询所有组织结构
     *
     *
     * @return
     * 查询所有组织结构
     */
    List<OrgObjectDTO> findAllOrgObjects();

    /**
     * 根据组织结构编号查询父类集合
     * @param orgId 组织结构编号
     * @param orgType 组织结构类型
     * @return 组织结构列表
     * 根据组织结构编号查询父类集合
     */
    List<OrgObjectDTO> findParentOrgById(String orgId, int orgType);

    /**
     * 根据组织结构编号查询父类集合，根据resultOrgType过滤父类的集合
     *
     * @param orgId          组织结构编号
     * @param orgType        组织结构类型
     * @param resultOrgType  组织类型
     * @return
     * 根据组织结构编号查询父类集合，根据resultOrgType过滤父类的集合
     */
    List<OrgObjectDTO> findParentOrgById(String orgId, int orgType, String resultOrgType);

    /**
     * 根据用户orgId 查询所属所有角色 orgId
     * @param orgId     组织结构编号
     * @return
     * 获取查询所属所有角色 orgId
     */
    List<String> findRoleIdListByOrgId(String orgId);


    /**
     * 根据 userId获取到 OrgUserExtendInfo对象
     * @param userId     用户userid
     * @return
     * 获取到 OrgUserExtendInfo对象
     */
    OrgUserExtendInfoDTO getOrgUserExtendInfoByUserId(String userId);


    /**
     * 根据组织结构编号,查出组织结构所在的岗位
     * @param toObjectId         组织结构Org编号
     * @return
     * 根据组织结构编号,获取组织结构所在的岗位
     */
    List<OrgSpiritObjectDTO> findPositionsByToObjectId(String toObjectId);

    /**
     * 根据组织结构编号,查出组织结构下的全部岗位,包含条件 relationType in (relationTypeList)
     * @param fromObjectId        组织结构上级Org编号
     * @param relationTypeList    关系类型
     * @return
     * 根据组织结构编号,查出组织结构下的全部岗位,包含条件 relationType in (relationTypeList)
     */
    List<OrgSpiritObjectDTO> findPositionsByFromObjectIdAndInRelationTypeList(String fromObjectId, List<String> relationTypeList);

    /**
     * 根据组织结构编号,查出组织结构拥有的全部岗位,包含条件 relationType in (relationTypeList)
     * @param toObjectId            组织结构Org编号
     * @param relationTypeList      关系类型
     * @return
     * 根据组织结构编号,查出组织结构拥有的全部岗位,包含条件 relationType in (relationTypeList)
     */
    List<OrgSpiritObjectDTO> findPositionsByToObjectIdAndInRelationTypeList(String toObjectId, List<String> relationTypeList);




    /**
     * 根据组织结构编号,查出组织结构下的岗位 (递归查询出全部的岗位), 包含条件 relationType in ['SUPERVISION','UNDERLINE']
     * @param fromObjectId     组织结构上级Org编号
     * @return
     * 根据组织结构编号,查出组织结构下的岗位 (递归查询出全部的岗位), 包含条件 relationType in ['SUPERVISION','UNDERLINE']
     */
    List<OrgSpiritObjectDTO> findPositionsByFromObjectIdAndInSupervisionAndUnderlineAndRecursive(String fromObjectId);

    /**
     * 根据组织结构编号,查出组织结构下的岗位 和组织结构上的岗位，合并返回
     * @param orgId                   组织结构编号
     * @param relationTypeList        关系类型
     * @return
     * 根据组织结构编号,查出组织结构下的岗位 和组织结构上的岗位，合并返回
     */
    List<OrgSpiritObjectDTO> findPositionsByToObjectIdAndFromObjectIdAndInRelationTypeList(String orgId, List<String> relationTypeList);

    /**
     * 查出所有岗位对应的岗位路径，合并返回
     * @return map<岗位id, 岗位路径>
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<String, String> getPositionsNamePaths();

    /**
     * 根据组织结构编号,查出组织结构下的全部小组
     * @param fromObjectId    组织结构上级Org编号
     * @return
     * 根据组织结构编号,查出组织结构下的全部小组
     */
    List<OrgSpiritObjectDTO> findTeamsByFromObjectId(String fromObjectId);

    /**
     * 根据组织结构编号,查出组织结构下的匹配orgName的小组
     * @param fromObjectId    组织结构上级Org编号
     * @param orgName         组织结构名称
     * @return
     * 根据组织结构编号,查出组织结构下的匹配orgName的小组
     */
    List<OrgSpiritObjectDTO> findTeamsByFromObjectIdAndLikeName(String fromObjectId, String orgName);


    /**
     * 根据组织结构编号,查出组织结构下的全部小组, 包含条件 relationType in ['SUPERVISION','UNDERLINE']
     * @param fromObjectId     组织结构上级Org编号
     * @return
     * 根据组织结构编号,查出组织结构下的全部小组, 包含条件 relationType in ['SUPERVISION','UNDERLINE']
     */
    List<OrgSpiritObjectDTO> findTeamsByFromObjectIdAndInSupervisionAndUnderline(String fromObjectId);


    /**
     * 根据组织结构编号,查出组织结构所在UNIT (递归查询出全部的UNIT)
     * @param toObjectId        组织结构Org编号
     * @return
     * 根据组织结构编号,查出组织结构所在UNIT (递归查询出全部的UNIT)
     */
    List<OrgSpiritObjectDTO> findUnitsByToObjectIdAndRecursive(String toObjectId);


    /**
     * 根据组织结构编号,查出组织结构下的全部Unit
     * @param fromObjectId      组织结构上级Org编号
     * @return
     * 根据组织结构编号,查出组织结构下的全部Unit
     */
    List<OrgSpiritObjectDTO> findUnitsByFromObjectId(String fromObjectId);


    /**
     * 根据组织结构编号,查出组织结构下的全部Unit, 包含条件 relationType in ['SUPERVISION','UNDERLINE']
     * @param fromObjectId    组织结构上级Org编号
     * @return
     * 根据组织结构编号,查出组织结构下的全部Unit, 包含条件 relationType in ['SUPERVISION','UNDERLINE']
     */
    List<OrgSpiritObjectDTO> findUnitsByFromObjectIdAndInSupervisionAndUnderline(String fromObjectId);

    /**
     * 根据组织结构编号,查出组织结构上的全部Unit, 递归查询出全部(第一次查询跳过unittype的条件,结果也不加入返回值)
     * @param toObjectId     组织结构Org编号
     * @return
     * 根据组织结构编号,查出组织结构上的全部Unit, 递归查询出全部(第一次查询跳过unittype的条件,结果也不加入返回值)
     */
    List<OrgSpiritObjectDTO> findUnitsByToObjectIdAndUnitTypeIgnoreLevelOneAndRecursive(String toObjectId);

    /**
     * 根据组织结构编号,查出组织结构所拥有角色
     * @param toObjectId     组织结构Org编号
     * @return
     * 根据组织结构编号,查出组织结构所拥有角色
     */
    List<OrgSpiritObjectDTO> findRolesByToObjectId(String toObjectId);

    /**
     * 根据组织结构编号,查出组织结构下的全部角色
     * @param fromObjectId    组织结构上级Org编号
     * @return
     * 根据组织结构编号,查出组织结构下的全部角色
     */
    List<OrgSpiritObjectDTO> findRolesByFromObjectId(String fromObjectId);

    /**
     * 根据组织结构编号,查出组织结构下的全部unitType, 并匹配orgName条件
     * @param fromObjectId     组织结构上级Org编号
     * @param unitType         Unit类型
     * @param orgName          员工名称
     * @return
     * 根据组织结构编号,查出组织结构下的全部unitType, 并匹配orgName条件
     */
    List<OrgSpiritObjectDTO> findOrgsByFromObjectIdAndUnitTypeAndOrgName(String fromObjectId, String unitType, String orgName);

    /**
     *  根据组织结构编号, 查出组织结构拥有的全部Org, 包含条件 orgtype in (unitTypeList) And relationType in (relationTypeList)
     * @param toObjectId             组织结构Org编号
     * @param unitTypeList           unit类型
     * @param relationTypeList      关系类型
     * @return
     * 根据组织结构编号, 查出组织结构拥有的全部Org, 包含条件 orgtype in (unitTypeList) And relationType in (relationTypeList)
     */
    List<OrgSpiritObjectDTO> findOrgsByToObjectIdAndInUnitTypeListAndInRelationTypeList(String toObjectId, List<String> unitTypeList, List<String> relationTypeList);

    /**
     *  根据组织结构编号, 查出组织结构下的全部Org, 包含条件 orgtype in (unitTypeList) And relationType in (relationTypeList)
     * @param fromObjectId         组织结构上级Org编号
     * @param unitTypeList         unit类型
     * @param relationTypeList     关系类型
     * @return
     * 根据组织结构编号, 查出组织结构下的全部Org, 包含条件 orgtype in (unitTypeList) And relationType in (relationTypeList)
     */
    List<OrgSpiritObjectDTO> findOrgsByFromObjectIdAndInUnitTypeListAndInRelationTypeList(String fromObjectId, List<String> unitTypeList, List<String> relationTypeList);

    /**
     * 是否是系统管理员
     * @param empId    人员ID
     * @return
     * 获取是否是系统管理员
     */
    Boolean isAdmin(String empId);

    /**
     * 清除用户是否为admin的缓存
     * @param empId    人员ID
     */
    void evictEmpAdminCache(String empId);

    /**
     * 是否是机构管理员
     * @param empId     人员ID
     * @return
     * 是否是机构管理员
     */
    Boolean isMechanismAdmin(String empId);

    /**
     * 清除用户是否为机构管理员的缓存
     * @param empId    人员ID
     */
    void evictEmpMechanismAdminCache(String empId);

    /**
     * 判断员工是否有角色
     * @param empId     员工id
     * @param roleName 角色名称
     * @return
     *获取判断员工是否有角色
     */
    Boolean isEmpHasRole(String empId, String roleName);

    /**
     * 加载部门岗位的 Cascader列表
     *
     * @return
     * 返回行政上级树
     */
    List<OrgCascaderDTO> findDepartmentalPositionCascaderList();

    /**
     * 查询出接近到期的用户列表
     * @return
     */
    List<ExpireUserDTO> findNearExpireUserList(Date nowDate);

    /**
     * 批量重置用户密码
     * @param orgids
     * @param defaultPassword
     */
    void batchReset(String[] orgids, String defaultPassword);

    /**
     * 根据 userid 获取对应的 UserProperties
     * @param userId
     * @return
     */
    @IgnoreMapCheck
    Map<String, String> getUserProperties (String userId);

    /**
     * 根据orgIdList获取 用户头像集合
     * @param orgIdList
     * @return
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<String,String> getEmpPhotosByIds(List<String> orgIdList);

    /**
     * 根据orgId获取 用户头像
     * @param orgId
     * @return
     */
    String getEmpPhotoById (String orgId);


    /**
     * 根据orgId获取 组织结构类型
     * @param orgid
     * @return
     */
    String getUnitTypeStrByOrgId (String orgid);

    /**
     * 根据orgName模糊匹配获取 userIdList
     * @param orgName
     * @return
     */
    List<String> findUserIdListByOrgName(String orgName);


    /**
     * 检查 OrgUserExtendInfo 是否存在, 通过 phone
     * @param phone
     * @param ignoreUserId
     * @return
     */
    boolean checkOrgUserExtendInfoExistByPhone(String phone, String ignoreUserId);


    /**
     * 根据角色ids获取角色下存在人员的角色名称列表
     * @param roleIdList 角色ids
     * @return
     */
    List<String> findRoleNameListByRoleIdListAndEmployeeNotNull(List<String> roleIdList);

    /**
     * 用户列表自定义数据格式化
     * @return
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    default List<Map<String, Object>> formatUserListData(List<Map<String, Object>> dataList) {
        return dataList;
    }

    /**
     * 找当前组织对象所属根目录下的顶级部门
     * @param currentOrgId 当前组织对象
     * @return 根目录下的顶级部门
     */
    String getTopUnitOrgId(String currentOrgId);

    /**
     * 根据组织方案代码，找当前组织对象直属下级对象集合
     * @param currentOrgId 当前组织对象
     * @param schemeCode 组织方案代码
     * @return 当前组织对象的直属下级集合
     */
    List<String> findChildOrgIds(String currentOrgId,String schemeCode);

    /**
     * 查询所属人员-选人控件-根据组织结构id进行查询
     * @param unitIds
     * @return
     */
    RequestResult<List<UserInfoDetailedDataDTO>> findEmployeeListByOrgIds(List<String> unitIds);

    /**
     * 根据邮箱获取对应的用户名
     * @param email
     * @return
     */
    String getUsernameByEmail(String email);

    /**
     * 通过用户empId查询当前用户所属部门的员工
     * @param empId 用户组织结构id
     * @param queryScopeEnum 查询范围 ((1 所有、2 仅同级、3 仅下级 4 相邻部门 )
     *  @see  com.sinitek.sirm.org.enumerate.DepartmentEmployeeQueryScopeEnum
     * @return 用户集合
     */
    List<Employee> findDepartmentEmployeesByEmpId(String empId, DepartmentEmployeeQueryScopeEnum queryScopeEnum);
}
