package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */

@Schema(description = "发送消息的接收人查询DTO")
@Data
public class MessageSendReceiverQueryDTO {

    @Schema(description= "消息模板id")
    private Long templateId;

    @Schema(description= "初始化参数")
    private Map<String, Object> initData;
}
