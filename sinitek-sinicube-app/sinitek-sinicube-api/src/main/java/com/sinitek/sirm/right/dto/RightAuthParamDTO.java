package com.sinitek.sirm.right.dto;

import com.sinitek.cloud.sirmapp.right.dto.RightAuthDTO;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Schema(description = "RightAuth的Service层传参DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RightAuthParamDTO {

    @Schema(description = "对象")
    private OrgObjectDTO orgObj;

    @Schema(description= "权限分类")
    private String rightDefineKey;

    @Schema(description= "授权类型")
    private String[] rightType;

    @Schema(description= "自有权限")
    private List<RightAuthDTO> outlist;
}
