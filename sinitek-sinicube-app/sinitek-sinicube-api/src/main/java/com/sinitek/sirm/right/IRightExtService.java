package com.sinitek.sirm.right;

import com.sinitek.cloud.sirmapp.right.dto.RightAuthDTO;
import com.sinitek.sirm.org.dto.OrgObjectDTO;

import java.util.List;

/**
 * 统一权限扩展接口
 *
 * <AUTHOR>
 * @date 2021-03-04
 */
public interface IRightExtService {

    /**
     * 批量保存权限对象列表
     *
     * @param rightAuthList 要保存的对象
     */
    void saveRightAuthList(List<RightAuthDTO> rightAuthList);

    /**
     * 批量删除权限对象列表
     *
     * @param rightAuthList 要删除的对象
     */
    void deleteRightAuthList(List<RightAuthDTO> rightAuthList);

    /**
     * 读取有权限的组织结构IdList
     *
     * @param orgId         对象id
     * @param orgType       对象组织结构类型
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     * 获取有权限的组织结构IdList
     */
    List<String> findAuthedObjects(String orgId,int orgType, String rightDefineKey, String rightType);

    /**
     * 读取有权限的组织结构IdList
     *
     * @param orgId         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    List<String> findAuthedObjects(String orgId, String rightDefineKey, String rightType);

    /**
     * 读取权限列表
     *
     * @param orgObj         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @param outlist
     * @param excludereject  是否排除拒绝权限
     * @return
     */
    public List<RightAuthDTO> findAuthedObjects(String orgObj, String rightDefineKey, String rightType, List<RightAuthDTO> outlist, boolean excludereject);

    /**
     * 获取有权限的全部 RightAuthDTO
     *
     * @param orgId            对象id
     * @param orgType          对象组织结构类型
     * @param rightDefineKey   授权模块定义(SPRT_RIGHTDEF)
     * @param rightType        权限
     * @return
     * 获取有权限的全部 RightAuthDTO
     */
    List<RightAuthDTO> findRightAuthDTOList(String orgId,int orgType, String rightDefineKey, String rightType);

    /**
     * 根据授权对象和授权分类获取权限信息
     *
     * @param rightDefineKey  授权模块定义(SPRT_RIGHTDEF)
     * @param orgId           对象id
     * @param orgType         对象组织结构类型
     * @param objectKey       对应的权限模块具体权限点Id
     * @param rightType       权限
     * @return
     */
    RightAuthDTO getRightAuth(String rightDefineKey, String orgId, int orgType, String objectKey, String rightType);


    /**
     * 根据授权对象和授权分类获取权限信息
     *
     * @param rightDefineKey
     * @param orgId
     * @param objectKey
     * @param rightType
     * @return
     */
    RightAuthDTO getRightAuth(String rightDefineKey, String orgId, String objectKey, String rightType);

    /**
     * 检查组织结构对象是否具有权限
     *
     * @param orgId          组织结构对象id
     * @param orgType        对象组织结构类型
     * @param rightObjId     权限对象Id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     *获取权限
     */
    boolean checkRight(String orgId, int orgType, String rightObjId, String rightDefineKey, String rightType);

    /**
     * 检查组织结构对象是否具有权限
     *
     * @param orgId          组织结构对象id
     * @param rightObjId     权限对象Id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    boolean checkRight(String orgId, String rightObjId, String rightDefineKey, String rightType);

    /**
     * 查询授权对应组织机构对象集合
     *
     * @param rightObjId     权限对象id
     * @param rightDefineKey 权限对象Entityname
     * @param rightType      权限访问类型
     * @return
     */
    List<OrgObjectDTO> findEnableAuthOrgObjects(String rightObjId, String rightDefineKey, String rightType);

    /**
     * 获取授权对象的组织结构对象集合
     *
     * @param rightObjId     对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    @Deprecated
    List<OrgObjectDTO> findAuthOrgObjects(String rightObjId, String rightDefineKey, String rightType);


    /**
     * 获取授权对象的组织结构对象集合
     *
     * @param rightObjId
     * @param rightDefineKey
     * @param rightType
     * @return
     */
    List<String> findAuthOrgIdList(String rightObjId, String rightDefineKey, String rightType);

    /**
     * 读取缓存中权限列表
     *
     * @param orgId         对象id
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     * @param rightType      权限
     * @return
     */
    List<RightAuthDTO> findEnableAuthedObjects(String orgId, String rightDefineKey, String rightType, List<RightAuthDTO> outlist);

    /**
     * 合并所有权限
     * @param orgId 对象id
     * @param rightType 权限类型
     * @param rightDefineKey 授权模块定义(SPRT_RIGHTDEF)
     */
    void recoverOrgAuth1(String orgId, String rightType, String rightDefineKey);

    /**
     * 删除授权对象的授权数据（给别人授权）
     *
     * @param orgObject         授权组织结构对象
     * @param rightDefineKey 授权定义key值:权限模块定义(SPRT_RIGHTDEF)
     * @param rightTypes     授权类型
     */
    void deleteRightAuth(OrgObjectDTO orgObject, String rightDefineKey, String[] rightTypes);

    /**
     * 新增/修改 RightAuth
     * @param rightAuth
     */
    void saveOrUpdateRightAuth(RightAuthDTO rightAuth);


    /**
     * 获取自身、上级节点继承，的权限类型
     * @param orgId 组织结构id
     * @param rightDefineKey 权限种类
     * @param rightType 权限控制类型
     * @param type<br/>      1=仅自身
     *                       2=仅上级节点
     *                       3=自身+上级节点
     * @return
     */
    List<RightAuthDTO> findAllAuthList(String orgId, String rightDefineKey, String[] rightType, int type, boolean excludereject);

    /**
     * 获取组织结构自身的多种分类权限列表
     * @param orgId
     * @param rightDefineKeyList
     * @return
     */
    List<RightAuthDTO> findSelfAuthListByRightDefineKeyList(String orgId, List<String> rightDefineKeyList);

}
