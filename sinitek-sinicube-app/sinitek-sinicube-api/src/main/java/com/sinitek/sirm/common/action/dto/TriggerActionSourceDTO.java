package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.action.message.ActionMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/12/13
 */
@Data
@Schema(description = "调度绑定动作信息")
public class TriggerActionSourceDTO {

    @NotEmpty(message = "{" + ActionMessageCode.TRIGGER_ACTION_NAME_NULL + "}")
    @Schema(description = "动作名称")
    private String name;

    @NotEmpty(message = "{" + ActionMessageCode.TRIGGER_ACTION_CODE_NULL + "}")
    @Schema(description = "动作code")
    private String code;

    @Schema(description = "动作url")
    private String url;

    @NotEmpty(message ="{" + ActionMessageCode.TRIGGER_ACTION_COMPONENT_DATA_NULL + "}")
    @Schema(description = "动作表单数据")
    private String componentData;


}
