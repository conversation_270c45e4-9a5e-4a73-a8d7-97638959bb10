package com.sinitek.sirm.org.event;

import com.sinitek.sirm.common.event.annotation.EventDefinition;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.org.dto.RoleDTO;

/**
 * <AUTHOR>
 * @date 2021/6/1
 * 删除角色事件
 */
@EventDefinition(type = "SiniCube", module = "角色管理", name = "角色删除事件", brief = "当删除的时候会触发")
public class RoleDeletedEvent extends SiniCubeEvent<RoleDTO> {

    public RoleDeletedEvent(RoleDTO source){
        super(source);
    }
}
