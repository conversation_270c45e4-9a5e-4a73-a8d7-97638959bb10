package com.sinitek.sirm.framework.utils;

import com.sinitek.sirm.common.encryption.algorithm.asymmetric.IAsymmetricEncryption;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.service.IUploaderExtService;
import com.sinitek.sirm.framework.web.CustomCommonsMultipartFile;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传通用操作工具类
 *
 * <AUTHOR>
 * @date 2023/04/23
 */
@Component
public class UploadCommonUtils {

    @Lazy
    @Autowired
    IAsymmetricEncryption asymmetricEncryption;

    @Lazy
    @Autowired
    IUploaderExtService uploaderExtService;

    @SneakyThrows
    public UploadFileDTO uploadTempFile(File file) {
        MultipartFile multipartFile = assembleCommonsMultipartFile(file);
        if (multipartFile == null) {
            return null;
        }
        String fileTempId = uploaderExtService.uploadFile(multipartFile, 0 + "", "");
        String md5 = uploaderExtService.countMd5(Files.newInputStream(file.toPath()));
        String fileName = file.getName();
        return assembleUploadedTempFile(fileTempId, fileName, md5, multipartFile.getSize());
    }

    /**
     * 组装临时上传文件的结果为 UploadFileDTO
     * @param fileTempId
     * @param fileName
     * @param md5
     * @return
     */
    public UploadFileDTO assembleUploadedTempFile(String fileTempId, String fileName, String md5, Long fileSize) {
        UploadFileDTO uploadFile = new UploadFileDTO();

        Map<String,Object> map = new HashMap<>();
        map.put("id",fileTempId);
        uploadFile.setResponse(map);
        uploadFile.setName(fileName);
        md5 = asymmetricEncryption.encryptByPublicKey(md5);
        uploadFile.setMd5(md5);
        uploadFile.setSize(NumberTool.safeToInteger(fileSize, 0));
        return uploadFile;
    }

    /**
     * 将文件组装为CommonsMultipartFile
     * @param file
     * @return
     */
    @SneakyThrows
    public MultipartFile assembleCommonsMultipartFile(File file) {
        if (null == file) {
            return null;
        }
        return new CustomCommonsMultipartFile(file.getName(),file.getName(), null, new FileInputStream(file));
    }
}
