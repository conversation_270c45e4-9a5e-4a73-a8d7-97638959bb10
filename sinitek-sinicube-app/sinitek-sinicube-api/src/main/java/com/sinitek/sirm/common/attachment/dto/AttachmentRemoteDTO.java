package com.sinitek.sirm.common.attachment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AttachmentRemoteDTO extends AttachmentDTO{

    @Schema(description = "文件主体")
    private MultipartFile file;
}
