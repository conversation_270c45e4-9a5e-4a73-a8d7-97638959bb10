package com.sinitek.sirm.org.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * File Desc:   岗位
 * Product Name: SIRM
 * Module Name: org
 * Author:      潘虹
 * History:     11-5-10 created by 潘虹
 */

@Data
@Schema(description = "岗位信息模型")
@EqualsAndHashCode(callSuper = true)
public class Position extends OrgCommonInfo {

    @Schema(description = "岗位Id")
    private String orgid;

    @Schema(description = "岗位名称")
    private String name;

    private String supBusinId;

    @Schema(description = "行政上级Id")
    private String supExecuteId;

    @Schema(description = "岗位描述")
    private String description;

    @Schema(description = "岗位上级")
    private String parentId;
}
