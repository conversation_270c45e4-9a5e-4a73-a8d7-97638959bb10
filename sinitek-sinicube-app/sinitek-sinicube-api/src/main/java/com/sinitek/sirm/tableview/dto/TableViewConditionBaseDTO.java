package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.tableview.message.TableViewConditionMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 表格视图条件 基础DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@Schema(description = "表格视图条件-基础DTO")
public class TableViewConditionBaseDTO {

    @Schema(description = "所属组Id")
    private Long groupId;

    @Schema(description = "字段名称", required = true)
    @NotBlank(message = "{"+ TableViewConditionMessage.FIELDNAME_CAN_NOT_NULL +"}")
    @Length(max = 30,message = "{"+ TableViewConditionMessage.FIELDNAME_CAN_NOT_EXCEED +"}")
    private String fieldName;

    @Schema(description = "条件值", required = true)
    @NotBlank(message = "{"+ TableViewConditionMessage.CONDITIONVALUE_CAN_NOT_NULL +"}")
    @Length(max = 30,message = "{"+ TableViewConditionMessage.CONDITIONVALUE_CAN_NOT_EXCEED +"}")
    private String conditionValue;

    @Schema(description = "字段值", required = true)
    @NotBlank(message = "{"+ TableViewConditionMessage.FIELDVALUE_CAN_NOT_NULL +"}")
    @Length(max = 300,message = "{"+ TableViewConditionMessage.FIELDVALUE_CAN_NOT_EXCEED +"}")
    private String fieldValue;

    @Schema(description = "字段类型(当明确字符串值不是字符串时，需要传递。如int)")
    @Length(max = 30,message = "{"+ TableViewConditionMessage.FIELDTYPE_CAN_NOT_EXCEED +"}")
    private String fieldType;

    @Length(max = 300,message = "{"+ TableViewConditionMessage.COMPONENT_CAN_NOT_EXCEED +"}")
    @Schema(description = "绑定的组件信息")
    private String component;
}
