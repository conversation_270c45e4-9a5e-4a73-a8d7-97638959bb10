package com.sinitek.sirm.menu.service;

import com.sinitek.base.common.pinyin.PinYinHelper;
import com.sinitek.sirm.common.utils.StringUtil;
import com.sinitek.sirm.menu.dto.CheckMenuAuthResultDTO;
import com.sinitek.sirm.menu.dto.UserMenuDTO;
import com.sinitek.sirm.route.dto.RouteDTO;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户有权限菜单相关操作接口
 *
 * <AUTHOR>
 * @date 2021-02-01
 */
public interface IUserMenuService {

    /**
     * 返回当前用户有权限的菜单列表
     *
     * @return
     *返回当前用户有权限的菜单列表
     */
    List<UserMenuDTO> findCurrentUserMenuList();

    /**
     * 判断当前用户是否拥有对应的菜单权限
     *@param dto         路由信息
     *@param request     请求
     * @return
     * 返回检查用户菜单权限的返回属性
     */
    CheckMenuAuthResultDTO checkMenuAuth(RouteDTO dto, HttpServletRequest request);

    /**
     * 根据菜单id判断当前用户是否拥有对应的菜单权限
     * @param menuId
     * @return
     */
    boolean checkMenuAuthByMenuId(Long menuId);

    /**
     * 根据搜索条件返回当前用户有权限的菜单列表
     *
     * @param match   匹配
     * @param name    名称
     * @return
     * 返回当前用户有权限的菜单列表
     */
     default List<UserMenuDTO> findCurrentUserMenuList(String match, String name) {
         List<UserMenuDTO> hasRightMenus = findCurrentUserMenuList();
         // 筛选菜单中 url 不为空
         hasRightMenus = hasRightMenus.stream().filter(userMenuDTO -> {
             String url = StringUtil.safeToString(userMenuDTO.getUrl(), "");
             if (StringUtils.isNotBlank(url)) {
                 return true;
             } else {
                 return false;
             }
         }).collect(Collectors.toList());

         // 根据文本框输入的数据匹配
         if (StringUtils.isNotBlank(name)) {
             hasRightMenus = hasRightMenus.stream().filter(userMenuDTO -> {
                 String title = StringUtil.safeToString(userMenuDTO.getName(), "");
                 if (name.equals(title)) {
                     return true;
                 } else {
                     return false;
                 }
             }).collect(Collectors.toList());
         }
         if (StringUtils.isNotBlank(match)) {
             hasRightMenus = hasRightMenus.stream().filter(userMenuDTO -> {
                 String title = StringUtil.safeToString(userMenuDTO.getName(), "");
                 String titleSimplePinYin = PinYinHelper.getInstance().getSimplePinYin(title);
                 if (title.contains(match) || titleSimplePinYin.contains(match.toLowerCase())) {
                     return true;
                 } else {
                     return false;
                 }
             }).collect(Collectors.toList());
         }
         return hasRightMenus;
     }
}
