package com.sinitek.sirm.org.dto;

import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by IntelliJ IDEA.
 *
 * User: 王志华
 *
 * Date: 2020/2/16
 *
 * Time: 4:12 下午
 */
@Data
@Schema(description = "组织结构选择项目")
@EqualsAndHashCode(callSuper = true)
public class OrgAutoCompleteDTO extends TenantIdParam {
    @Schema(description = "ID")
    private String id;

    @Schema(description = "KEY")
    private String key;

    @Schema(description = "简拼")
    private String py;

    @Schema(description = "全拼")
    private String pinyin;

    @Schema(description = "值")
    private String value;

    @Schema(description = "显示")
    private String text;

    @Schema(description = "组织结构类型")
    private Integer orgtype;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "是否有效")
    private Boolean inservice;

    @Schema(description = "机构名称")
    private String tenantName;
}
