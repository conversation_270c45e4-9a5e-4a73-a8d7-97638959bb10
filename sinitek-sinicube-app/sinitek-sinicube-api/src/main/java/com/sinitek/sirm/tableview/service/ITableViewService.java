package com.sinitek.sirm.tableview.service;

import com.sinitek.sirm.tableview.dto.TableViewManageRespDTO;
import com.sinitek.sirm.tableview.dto.TableViewMoveDTO;
import com.sinitek.sirm.tableview.dto.TableViewRespDTO;
import com.sinitek.sirm.tableview.dto.TableViewSaveDTO;

import java.util.List;

/**
 * 表格视图 Service 接口
 *
 * <AUTHOR>
 * date 2023-07-11
 */
public interface ITableViewService {

    /**
     * 获取视图列表根据用户Id和表格Id过滤
     * @param code
     * @param userOrgId
     * @return
     */
    List<TableViewRespDTO> findTableViewListByTableIdAndUserId(String code, String userOrgId);

    /**
     * 根据Id获取视图详细信息
     * @param id
     * @return
     */
    TableViewRespDTO getTableViewDetailById(Long id);

    /**
     * 添加或修改表格视图信息
     * @param tableViewSaveDTO
     * @return
     */
    Long saveOrUpdateTableView(TableViewSaveDTO tableViewSaveDTO);

    /**
     * 判断视图名称是否存在, 第二个参数为忽略的主键
     * @param name 视图名称
     * @param ignoreId 忽略的主键
     * @param tableId 表格唯一标识
     * @param userId 用户Id
     * @return
     */
    boolean checkNameDoesItExistByTableIdAndUserId(String name, Long ignoreId, String tableId, String userId);

    /**
     * 根据idList批量删除
     * @param idList
     */
    void deleteTableViewByIdList(List<Long> idList);

    /**
     * 根据视图Id获取到视图的动态SQL
     *
     * @param viewId
     * @return
     */
    String getViewDynamicSqlById(Long viewId);

    /**
     * 基于viewId清除视图的动态SQL缓存
     * @param viewId
     */
    void evictViewDynamicSqlCache(Long viewId);

    /**
     * 查询管理视图列表
     *  - 包含 展示列表和隐藏列表
     * @param code
     * @param userOrgId
     * @return
     */
    TableViewManageRespDTO getTableViewManageViewRespDTO(String code, String userOrgId);

    /**
     * 移动视图 - 展示或隐藏
     * @param code
     * @param userOrgId
     * @param tableViewMoveDTO
     */
    void move(String code, String userOrgId, TableViewMoveDTO tableViewMoveDTO);
}
