package com.sinitek.sirm.common.attachment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 附件信息数据对象
 *
 * <AUTHOR>
 * @date 2022/1/6
 */
@Data
@Schema(description = "附件复制参数")
public class AttachmentCopyDTO {

    @Schema(description = "要复制的附件IdList")
    private List<Long> attachmentIdList;

    @Schema(description = "复制后附件所使用的sourceId")
    private Long newSourceId;

    @Schema(description = "复制后附件所使用的sourceEntity")
    private String newSourceEntity;
}
