package com.sinitek.sirm.common.function.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.common.function.message.FunctionMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2022/6/1
 */
@Data
@Schema(description = "功能信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class FunctionDTO {

    @Schema(description = "功能id")
    private long objid;

    @Length(max = 100, message = "{"+ FunctionMessageCode.CODE_LENGTH_TOO_BIG +"}")
    @NotBlank(message = "{"+ FunctionMessageCode.FUNCTION_VALUE_CAN_NOT_NULL +"}")
    @Schema(description = "功能编码")
    private String code;

    @NotBlank(message = "{"+ FunctionMessageCode.FUNCTION_NAME_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ FunctionMessageCode.NAME_LENGTH_TOO_BIG +"}")
    @Schema(description = "功能名")
    private String name;

    @Length(max = 1000, message = "{"+ FunctionMessageCode.URL_LENGTH_TOO_BIG +"}")
    @Schema(description = "url")
    private String url;

    @Length(max = 100, message = "{"+ FunctionMessageCode.BRIEF_LENGTH_TOO_BIG +"}")
    @Schema(description = "描述")
    private String brief;

    @Schema(description = "分组id")
    private Long groupId;

    @Length(max = 100, message = "{"+ FunctionMessageCode.GROUP_NAME_LENGTH_TOO_BIG +"}")
    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "功能类型")
    private int type;

    @Schema(description = "关联菜单id")
    private Long menuId;

    @Length(max = 100, message = "{"+ FunctionMessageCode.SELECTOR_LENGTH_TOO_BIG +"}")
    @Schema(description = "css类型选择器")
    private String selector;

    @Schema(description = "对应菜单路由")
    private String menuRoutePath;

    @Schema(description = "所属页面路由")
    private String pageRoutePath;

    @Schema(description = "功能页面路由")
    @Length(max = 100, message = "{"+ FunctionMessageCode.FUNCTION_PAGE_CANT_BEYOND_100 +"}")
    private String functionPageRoutePath;

    @Schema(description = "排序")
    private int sort;

    @Schema(description = "控制页面埋点标识")
    private boolean pageAccessControl;

}
