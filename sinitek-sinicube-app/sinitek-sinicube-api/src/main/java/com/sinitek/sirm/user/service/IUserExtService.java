package com.sinitek.sirm.user.service;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.user.dto.ResetPasswordDTO;
import com.sinitek.sirm.user.dto.UserInfoDTO;
import com.sinitek.sirm.user.dto.UserResultDTO;

/**
 * 统一用户相关接口
 *
 * <AUTHOR>
 * @date 2021-03-12
 */
public interface IUserExtService {

    /**
     * 根据用户id获取到用户的详细信息
     *
     * @param orgId    用户id
     * @return
     * 获取用户的详细信息
     */
    UserResultDTO getUserDetailById(String orgId);

    /**
     * 根据用户id获取到用户多个机构的详细信息
     *
     * @param orgId    用户id
     * @param isMultiRole 是否查询多机构的用户
     * @return
     * 获取用户的详细信息
     */
    UserResultDTO getUserDetailById(String orgId, boolean isMultiRole);

    /**
     * 获取用户头像的Base64编码
     * @param orgId   用户id
     * @return
     * 获取用户头像的Base64编码
     */
    String getPhotoImageData(String orgId);

    /**
     * 保存用户信息
     * @param dto   用户信息
     * @return
     * 返回用户信息
     */
    default String save(UserInfoDTO dto) {
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * 生成密码
     * @param forceDefaultPwd 是否强制生成默认密码
     * @return
     */
    String generatePassword(boolean forceDefaultPwd);

    /**
     * 重置用户密码
     * @param employee
     * @param dto
     */
    String resetUserPwdByEmployee(Employee employee, ResetPasswordDTO dto);

    void tempExample();

    /**
     * 基于 userId 验证用户状态
     *  - 用户是否离职
     *  - 用户是否到期
     *  - 用户是否锁定
     * @param userId
     */
    void checkUserStatusByUserId(String userId);
}
