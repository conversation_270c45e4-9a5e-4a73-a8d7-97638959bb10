package com.sinitek.sirm.framework.service;

import com.sinitek.sirm.common.utils.EncryptUtil;
import com.sinitek.sirm.framework.dto.ChunkUploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;


/**
 * 上传附件相关接口
 * <AUTHOR>
 * @date 2023/1/29
 */
public interface IUploaderExtService {

    /**
     * 上传文件到临时目录下
     * @param file 文件
     * @param fsize 文件大小
     * @param suffixes 文件扩展名
     * @return 文件名
     */
    String uploadFile(MultipartFile file, String fsize, String suffixes);

    /**
     * 检查分片
     * @param chunk 分片块
     * @return 分片号
     */
    List<Integer> checkChunk(ChunkUploadDTO chunk);

    /**
     * 上传分片
     * @param chunk 分片
     * @return 错误信息
     */
    String uploadChunk(ChunkUploadDTO chunk);

    /**
     * 清除临时文件
     */
    void cleanTempDir();

    /**
     * 获取临时文件流
     * @param uploadFileDTO
     * @return
     */
    InputStream getTempFile(UploadFileDTO uploadFileDTO);

    /**
     * 计算上传文件的md5
     * @param inputStream
     * @return
     */
    default String countMd5(InputStream inputStream) {
        return EncryptUtil.getFileMD5(inputStream);
    }

}
