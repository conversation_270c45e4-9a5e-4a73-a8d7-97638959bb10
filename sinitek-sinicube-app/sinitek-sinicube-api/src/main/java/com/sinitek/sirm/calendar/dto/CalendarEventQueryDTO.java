package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/11/16
 */
@Schema(description = "查询参数对象")
@Data
public class CalendarEventQueryDTO {

    @Schema(description= "日程类型")
    private String type;

    @Schema(description= "日历类型")
    private String calendarType;

    @Schema(description= "日程开始时间")
    private Date startTime;

    @Schema(description= "日程结束时间")
    private Date endTime;

    @Schema(description= "录入人ID")
    private String inputId;

    @Schema(description= "日程主题")
    private String title;

    @Schema(description= "日程来源对象")
    private String sourceName;

    @Schema(description= "日程来源ID")
    private Long sourceId;

    @Schema(description= "人员ID")
    private String empId;

    @Schema(description= "内容")
    private String content;

    @Schema(description= "url")
    private String url;

    @Schema(description= "日程所有者ID")
    private Long ownerId;

    @Schema(description= "日程所有者名称")
    private String ownerName;

    @Schema(description= "是否重复")
    private Integer repeatFlag;
}
