package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.message.template.support.IMessageTextHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 触发动作的上下文数据对象
 *
 * <AUTHOR>
 * @date 2021/9/20
 */
@Schema(description = "触发动作的上下文数据对象")
@Data
public class TriggerActionContextDTO<T, K> {

    @Schema(description = "初始数据")
    private T initData;


    @Schema(description = "动作绑定表单数据")
    private K componentData;

    @Schema(description = "来源对象Key")
    private String sourceKey;

    @Schema(description = "来源对象name")
    private String sourceName;

    @Schema(description = "所属对象Key")
    private String ownerKey;

    @Schema(description = "所属对象name")
    private String ownerName;

    @Schema(description = "自定义占位符替换handler")
    private IMessageTextHandler messageTextHandler;

}
