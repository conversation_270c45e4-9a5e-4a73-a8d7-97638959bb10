
package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * User: wyzhang
 * Date: 2018/4/3
 */
@Data
@Schema(description = "组织结构树展示信息模型")
public class OrgTreeDTO {

    @Schema(description = "id")
    private String id = null;

    @Schema(description = "标题")
    private String label = null;

    @Schema(description = "类型")
    private String type = null;

    @Schema(description = "主键,使用orgId:orgName:orgType的格式")
    private String key;

    @Schema(description = "组织结构类型")
    private int orgType = 0;

    @Schema(description = "子组织结构")
    private List<OrgTreeDTO> children = null;

    @Schema(description = "父id")
    private String parentId = null;

    @Schema(description = "图标样式")
    private String icon = null;

    @Schema(description = "是否能编辑")
    private boolean editAble = false;

    @Schema(description = "是否能删除")
    private boolean removeAble = false;

    @Schema(description = "节点顺序")
    private int sort = 0;

    @Schema(description = "禁用状态")
    private boolean disabled = false;

}
