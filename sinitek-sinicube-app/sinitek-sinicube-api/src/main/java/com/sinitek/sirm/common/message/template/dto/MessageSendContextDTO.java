package com.sinitek.sirm.common.message.template.dto;

/**
 * File Desc:
 * Product Name:
 * Module Name:
 * Author:      王志华
 * History:     11-6-16 上午10:06
 */
public class MessageSendContextDTO extends MessageContextDTO {
    private String title = null;
    private String content = null;

    /** 流程处理人 */
    private Long exampleownerid;

    /** 模板类型(来源工作流) */
    private int templatetypeid;

    /** 模板类型(来源模板) */
    private int templatetype;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }


    public int getTemplatetypeid() {
        return templatetypeid;
    }

    public void setTemplatetypeid(int templatetypeid) {
        this.templatetypeid = templatetypeid;
    }

    public int getTemplatetype() {
        return templatetype;
    }

    public void setTemplatetype(int templatetype) {
        this.templatetype = templatetype;
    }

    public Long getExampleownerid() {
        return exampleownerid;
    }

    public void setExampleownerid(Long exampleownerid) {
        this.exampleownerid = exampleownerid;
    }
}
