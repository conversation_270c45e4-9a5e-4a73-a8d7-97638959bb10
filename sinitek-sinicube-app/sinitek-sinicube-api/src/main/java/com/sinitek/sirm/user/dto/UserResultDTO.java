package com.sinitek.sirm.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/14
 */
@Data
@NoArgsConstructor
@Schema(description  = "用户信息详情描述")
public class UserResultDTO{
    @Schema(description = "来源id")
    private String sourceId;

    @Schema(description = "来源名称")
    private String postname;

    @Schema(description = "所属角色名称")
    private String rolename;

    @Schema(description = "所属小组名称")
    private String teamname;

    @Schema(description = "行政上级名称")
    private String superiorname;

    @Schema(description = "下属名称")
    private String subordinatename;

    @Schema(description = "用户ID")
    private String orgid;

    @Schema(description = "登陆名")
    private String username;

    @Schema(description = "员工名称")
    private String empname;

    @Schema(description = "员工姓名拼音")
    private String empnamepy;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "办公电话")
    private String tel;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "职位")
    private String job;

    @Schema(description = "在职状态")
    private String inservice;

    @Schema(description = "入职日期")
    private String entrydate;

    @Schema(description = "离职日期")
    private String leavedate;

    @Schema(description = "工作地")
    private String  workplace;

    @Schema(description = "个人简介")
    private String introduction = null;

    @Schema(description = "所属岗位")
    private List<List<String>> postids;

    @Schema(description = "所属角色")
    private String roleids;

    @Schema(description = "所属小组")
    private String teamids;

    @Schema(description = "行政上级")
    private String superior;

    @Schema(description = "到期时间")
    private Date expireTime;

    @Schema(description = "到期时间格式化后")
    private String expireTimeStr;

    @Schema(description = "所属机构名称")
    private String tenantName;

    @Schema(description = "用户源")
    private String dataSrc;

    @Schema(description = "用户源objid")
    private String origId;

    @Schema(description = "提供给外部扩展参数")
    private Map<String, Object> extendMap;

}
