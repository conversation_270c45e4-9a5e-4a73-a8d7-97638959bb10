package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.tableview.message.TableViewMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 表格视图-排序DTO
 *
 * <AUTHOR>
 * date 2023-07-14
 */
@Data
@Schema(description = "表格视图-排序DTO")
public class TableViewSortDTO {

    @Schema(description = "表格唯一标识")
    @NotBlank(message = "{"+ TableViewMessage.TABLE_ID_CAN_NOT_NULL +"}")
    private String code;

    @Schema(description = "排序的视图id列表")
    private List<Long> viewIdList;
}
