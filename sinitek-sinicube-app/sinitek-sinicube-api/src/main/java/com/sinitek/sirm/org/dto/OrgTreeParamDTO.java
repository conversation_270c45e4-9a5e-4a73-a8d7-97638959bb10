package com.sinitek.sirm.org.dto;

import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * User: wyzhang
 * Date: 2018/4/23.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "组织结构树查询条件")
public class OrgTreeParamDTO extends TenantIdParam {

    @Schema(description = "是否包含角色")
    private boolean hasRole = false;

    @Schema(description = "是否包含员工")
    private boolean hasEmp = false;

    @Schema(description = "是否包含岗位。默认true")
    private boolean hasPosition = true;

    @Schema(description = "是否可对树节点进行操作")
    private boolean operation = false;

    @Schema(description = "是否只查询员工")
    private boolean hasUser = false;

    @Schema(description = "权限定义key")
    private String rightDefineKey;

    @Schema(description = "授权类型")
    private String rightType;

    @Schema(description = "组织结构ID")
    private String id;
}
