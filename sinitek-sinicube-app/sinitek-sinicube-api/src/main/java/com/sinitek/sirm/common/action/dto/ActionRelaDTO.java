package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.action.message.ActionMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 动作数据对象
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "动作绑定数据对象")
public class ActionRelaDTO extends ActionDTO{

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "动作id，关联SIRM_ACTION.id")
    private Long actionId;

    @NotBlank(message = "{"+ ActionMessageCode.CODE_CAN_NOT_NULL +"}")
    @Schema(description = "绑定对象id")
    private String sourceId;

    @NotBlank(message = "{"+ ActionMessageCode.CODE_CAN_NOT_NULL +"}")
    @Schema(description = "绑定对象名")
    private String sourceName;

    @Schema(description = "表单数据(json)")
    private String componentData;

    @Schema(description = "执行表达式")
    private String executeExpression;

}
