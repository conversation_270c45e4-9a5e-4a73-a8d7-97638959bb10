package com.sinitek.sirm.menu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *  菜单-图标管理
 *
 * <AUTHOR>
 * @date 2022/11/30
 */
@Data
@Schema(description = "移动图标DTO")
public class MenuMobileIconDTO {

    @Schema(description = "图标id")
    private Long id;

    @Schema(description = "菜单权限校验")
    private String verification;

    @Schema(description = "跳转类型")
    private String jumpType;

    @Schema(description = "跳转路径")
    private String jumpPath;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "图标名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "标号")
    private String menuId;

    @Schema(description = "图标类型")
    private String type;
}
