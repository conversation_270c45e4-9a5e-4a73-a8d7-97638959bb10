package com.sinitek.sirm.common.sirmenum.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by IntelliJ IDEA.
 * User: 王志华
 * Date: 2018/4/1
 * Time: 下午10:34
 * To change this template use File | Settings | File Templates.
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "枚举列表查询参数模型")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EnumListDTO extends PageDataParam {

    @Schema(description = "模块名称")
    private String catalog = null;

    @Schema(description = "枚举类型")
    private String type = null;

    @Schema(description = "枚举名称")
    private String name = null;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "id")
    private Long objid;

    @Schema(description = "组织模块")
    private String originalCatalog;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "枚举值")
    private String value;

    @Schema(description = "枚举值类型")
    private Integer valuetype;

    @Schema(description = "字符串值")
    private String strvalue;

}
