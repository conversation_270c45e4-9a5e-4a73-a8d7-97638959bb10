package com.sinitek.sirm.common.sysmenu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/1 10:39
 */
@Data
@Schema(description = "菜单树参数描述")
public class MenuCascaderDTO {
    @Schema(description = "菜单id")
    private Long id;

    @Schema(description = "菜单顺序")
    private int ord;

    @Schema(description = "菜单名称")
    private String label;

    @Schema(description = "菜单类型")
    private String type;

    @Schema(description = "菜单是否禁用")
    private boolean disabled = true;

    @Schema(description = "子菜单")
    private List<MenuCascaderDTO> children;

}
