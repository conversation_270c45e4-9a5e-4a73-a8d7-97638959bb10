package com.sinitek.sirm.common.action.service;

import com.sinitek.sirm.common.action.dto.*;

import java.util.List;

/**
 * 动作的对外服务接口类
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
public interface IActionExService {


    /**
     * 动作绑定接口
     *
     * @param dto      绑定动作的参数
     *             actionId         动作id
     *             componentData    动作Vue表单数据
     *             sourceId         绑定对象id
     *             sourceName       绑定对象名
     */
    <T> void bindAction(BindActionDTO<T> dto);


    /**
     * 动作解除绑定接口
     *
     * @param dto      绑定动作的参数
     *             actionId         动作id
     *             sourceId         绑定对象id
     *             sourceName       绑定对象名
     */
    void unBindAction(UnBindActionDTO dto);

    /**
     * 动作绑定解除接口
     * @param ids 绑定关系id集合
     */
    void unBindAction(List<Long> ids);


    /**
     * 检查是否绑定过动作
     *
     * @param sourceId      绑定对象id
     * @param sourceName    绑定对象名
     * @return
     */
    boolean isBindAction(String sourceId, String sourceName);


    /**
     * 动作触发接口
     *
     * @param dto           触发动作的参数
     *          actionId        动作实例id
     *          data            动作初始参数
     */
    <T> List<ActionExResultDTO> triggerAction(TriggerActionDTO<T> dto);

    /**
     * 查询绑定的动作集合
     *
     * @param sourceId      绑定对象id
     * @param sourceName    绑定对象名
     * @return
     */
    List<ActionRelaDTO> findBindActions(String sourceId, String sourceName);


}
