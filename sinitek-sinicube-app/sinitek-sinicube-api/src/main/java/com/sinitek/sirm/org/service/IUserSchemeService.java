package com.sinitek.sirm.org.service;

import com.sinitek.sirm.org.entity.Department;
import com.sinitek.sirm.org.entity.Employee;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/6 10:50
 */
public interface IUserSchemeService {

    /**
     * 根据编号获取选人控件实体
     *
     * @param schemeId 方案ID
     * @param param 参数
     * @return schemeId=0时返回null
     */
    List<Employee> findEmployeeBySchemeId(Long schemeId, Map<String,Object> param);

    /**
     * 根据方案代码 查询人员列表（默认方案）
     *
     * @param schemeCode 方案代码
     * @return
     */
    List<Employee> findEmployeeBySchemeCode(String schemeCode);

    /**
     * 根据方案代码 查询人员列表（本地接口和远程接口）
     *
     * @param schemeCode 方案代码
     * @param param
     * @return
     */
    List<Employee> findEmployeeBySchemeCode(String schemeCode, Map<String,Object> param);

    /**
     * 根据方案代码 查询部门列表
     *
     * @param schemeCode 方案代码
     * @return
     */
    List<Department> findDepartmentsBySchemeCode(String schemeCode);

    /**
     * 根据方案代码集合 批量查询人员列表（本地接口和远程接口）
     *
     * @param schemeCodes 方案代码集合
     * @param param
     * @return
     */
    List<Employee> findEmployeesBySchemeCodes(List<String> schemeCodes, Map<String,Object> param);
}
