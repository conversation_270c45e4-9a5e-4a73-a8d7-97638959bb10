package com.sinitek.sirm.routine.holiday.dto;

import com.sinitek.sirm.routine.holiday.message.HolidayMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * Created by user on 2018/6/26.
 */
@Data
@Schema(description = "节假日保存模型")
public class HolidaysConfigDTO {
    /**
     * 保存的所有节假日
     */
    @Schema(description = "保存的所有节假日")
    private String checkdays = null;
    /**
     * 保存的节假日所属年
     */
    @Min(value = 1, message = "{"+ HolidayMessageCode.SAVE_YEARS_ERROR +"}")
    @Schema(description = "保存的节假日所属年")
    private int year = 0;

    @Schema(description = "导入的方式")
    private String importType;

    /**
     * 节假日方案Code
     */
    @Schema(description = "节假日方案Code")
    private String schemeCode;

    public String getCheckdays() {
        return checkdays;
    }

    public void setCheckdays(String checkdays) {
        this.checkdays = checkdays;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }


}
