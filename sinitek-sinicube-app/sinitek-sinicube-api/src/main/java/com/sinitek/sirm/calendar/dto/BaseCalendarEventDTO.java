package com.sinitek.sirm.calendar.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.remind.dto.RepeatTimeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/2/16
 */
@Data
@Schema(description = "日程基类dto")
public class BaseCalendarEventDTO {

    @Schema(description = "日程id")
    private Long id;

    @Schema(description = "日程类型")
    private String type;

    @Schema(description = "所属员工id")
    private String empId;

    @Schema(description = "员工姓名")
    private String orgName;

    @Schema(description = "日程标题")
    private String title;

    @JsonFormat(shape =JsonFormat.Shape.STRING,pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    @Schema(description = "日程开始时间")
    private Date startTime;

    @JsonFormat(shape =JsonFormat.Shape.STRING,pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    @Schema(description = "日程结束时间")
    private Date endTime;

    @Schema(description = "日程提醒时间")
    private Integer[] remindTime;

    @Schema(description = "日程内容")
    private String content;

    @Schema(description = "url")
    private String url;

    @Schema(description = "日程关联资源Id")
    private Long sourceId;

    @Schema(description = "日程关联资源名")
    private String sourceName;

    @Schema(description = "是否勾选全天")
    private Integer allDay;

    @Schema(description = "是否自定义提醒时间")
    private Integer customRemind;

    @Schema(description = "自定义提醒时间")
    @JsonFormat(shape =JsonFormat.Shape.STRING,pattern ="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date customRemindTime;

    @Schema(description = "是否重复")
    private Integer repeatFlag;

    @Schema(description = "重复规则dto")
    private RepeatTimeDTO repeatTimeDTO;

    @Schema(description = "重复规则对应的注释")
    private String repeatValue;
}

