package com.sinitek.sirm.menu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 检查用户菜单权限的返回属性
 *
 * <AUTHOR>
 * @date 2021-02-02
 */
@Data
@Schema(description = "检查用户菜单权限的返回属性")
public class CheckMenuAuthResultDTO {

    @Schema(description = "是否有菜单的访问权限", required = true)
    private Boolean hasAuth;

    @Schema(description = "访问菜单的名称")
    private String menuName;
}
