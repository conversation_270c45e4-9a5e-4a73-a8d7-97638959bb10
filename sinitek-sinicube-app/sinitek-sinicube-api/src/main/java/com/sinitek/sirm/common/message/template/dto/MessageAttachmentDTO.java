package com.sinitek.sirm.common.message.template.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/6/16 11:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageAttachmentDTO {


    /**
     * 文件显示名 （原 fileData）
     */
    private String fileName;

    /**
     * 文件路径  （原 fileName）
     */
    private String filePath;


    /**
     * 附件 objId
     */
    private Long attachmentId;
}
