package com.sinitek.sirm.common.action.enumerate;

/**
 * 动作类型枚举
 *
 * <AUTHOR>
 * @date 2021/9/8
 */
public enum ActionType {


    /**
     * 本地动作
     */
    LOCAL("本地动作", 1),

    /**
     * 远程动作
     */
    REMOTE("远程动作", 2);

    private String name;

    private int value;

    ActionType(String name, int value){
        this.name = name;
        this.value = value;
    }

    public String getName(){
        return name;
    }

    public int getValue(){
        return value;
    }

    public static String getName(int value){
        for(ActionType type : ActionType.values()){
            if(value == type.getValue()){
                return type.getName();
            }
        }
        return  "";
    }

}
