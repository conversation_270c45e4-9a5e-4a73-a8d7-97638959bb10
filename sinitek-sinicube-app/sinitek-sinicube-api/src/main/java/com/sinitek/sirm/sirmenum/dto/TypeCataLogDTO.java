package com.sinitek.sirm.sirmenum.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/10/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "枚举类型、枚举分类实体对象")
public class TypeCataLogDTO {

    @Schema(description = "枚举类型")
    private String type;

    @Schema(description = "枚举分类")
    private String cataLog;
}
