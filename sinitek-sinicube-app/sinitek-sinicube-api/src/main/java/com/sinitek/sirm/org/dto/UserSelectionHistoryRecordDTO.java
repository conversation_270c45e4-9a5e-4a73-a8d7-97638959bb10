package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户选择历史记录 - DTO
 *
 * <AUTHOR>
 * date 2024-03-01
 */
@Data
@Schema(description = "用户选择历史记录DTO")
public class UserSelectionHistoryRecordDTO {

    @Schema(description = "是否开启用户选择历史记录")
    private boolean enableUserSelectionHistory;

    @Schema(description = "记录场景")
    private String scene;
}
