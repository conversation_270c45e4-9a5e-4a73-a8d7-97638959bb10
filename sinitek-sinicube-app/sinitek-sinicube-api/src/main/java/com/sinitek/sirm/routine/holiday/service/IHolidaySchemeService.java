package com.sinitek.sirm.routine.holiday.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.routine.holiday.dto.HolidaySchemeDTO;
import com.sinitek.sirm.routine.holiday.dto.HolidaySchemeQueryDTO;
import com.sinitek.sirm.routine.holiday.dto.HolidaySchemeQueryResultDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/8
 */
public interface IHolidaySchemeService {

    /**
     * 查询节假日方案列表
     * @param query 列表查询条件
     * @return 返回查询结果
     */
    IPage<HolidaySchemeQueryResultDTO> searchHolidayScheme(HolidaySchemeQueryDTO query);

    /**
     * 删除节假日方案
     *
     * @param dtoList 方案List
     */
    void deleteHolidaySchemeByList(List<HolidaySchemeDTO> dtoList);

    /**
     * 新增/修改 节假日方案
     *
     * @param holidaySchemeDTO 节假日方案信息参数
     * @return 返回方案id
     */
    Long saveHolidayScheme(HolidaySchemeDTO holidaySchemeDTO);

    /**
     * 根据id获取节假日方案信息
     *
     * @param id 方案id
     * @return 返回方案信息
     */
    HolidaySchemeDTO getHolidaySchemeById(Long id);

    /**
     * 根据code获取节假日方案信息
     *
     * @param code 方案code
     * @return 返回方案信息
     */
    HolidaySchemeQueryResultDTO getHolidaySchemeByCode(String code);

    /**
     * 新增默认的节假日方案（default_flag = 1）
     * @return 默认方案id
     */
    Long insertDefaultHolidayScheme();

    /**
     * 获取默认类型的节假日方案（default_flag = 1）
     * @return HolidaySchemeDTO
     */
    HolidaySchemeDTO getDefaultHolidayScheme();

    /**
     * 获取系统中所有类型的节假日方案
     *
     * @return 节假日方案集合
     */
    List<HolidaySchemeDTO> findHolidaySchemes();
}
