package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.action.message.ActionMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 动作数据对象
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "动作数据对象")
public class ActionDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "动作名称")
    @NotBlank(message = "{"+ ActionMessageCode.NAME_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ ActionMessageCode.NAME_LENGTH_TOO_BIG +"}")
    private String name;

    @Schema(description = "动作唯一编码")
    @NotBlank(message = "{"+ ActionMessageCode.CODE_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ ActionMessageCode.CODE_LENGTH_TOO_BIG +"}")
    private String code;

    @Schema(description = "动作类型：1 本地，2 远程")
    @Range(min = 1, max = 2, message = "{" + ActionMessageCode.ACTION_TYPE_ERROR + "}")
    private int type;

    @Schema(description = "动作类型显示名")
    private String typeName;

    @Schema(description = "动作实现类名")
    private String handler;

    @Schema(description = "远程接口url，远程动作类型为有效")
    @Length(max = 200, message = "{"+ ActionMessageCode.URL_LENGTH_TOO_BIG +"}")
    private String url;


    @Schema(description = "是否关联vue组件")
    private Boolean bindVueFlag;

    @Schema(description = "绑定的vue组件名")
    private String vueComponentName;

}
