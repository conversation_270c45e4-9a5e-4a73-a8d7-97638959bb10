package com.sinitek.sirm.org.dto;


import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "组织结构是否需要初始化参数")
public class InitCheckParamDTO extends TenantIdParam {

    @Schema(description = "是否检查过")
    private boolean checked = false;


}
