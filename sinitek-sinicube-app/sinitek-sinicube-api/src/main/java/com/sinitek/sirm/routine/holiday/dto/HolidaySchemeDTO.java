package com.sinitek.sirm.routine.holiday.dto;

import com.sinitek.sirm.routine.holiday.message.HolidayMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2022/10/8
 */
@Data
@Schema(description = "节假日方案数据模型")
public class HolidaySchemeDTO {

    @Schema(description = "方案id")
    private Long id;

    @Length(max = 50, message = "{"+ HolidayMessageCode.SCHEME_CODE_LENGTH_TOO_BIG +"}")
    @NotBlank(message = "{"+ HolidayMessageCode.SCHEME_CODE_NULL +"}")
    @Schema(description = "方案编码")
    private String code;

    @Length(max = 30, message = "{"+ HolidayMessageCode.SCHEME_NAME_LENGTH_TOO_BIG +"}")
    @NotBlank(message = "{"+ HolidayMessageCode.SCHEME_NAME_NULL +"}")
    @Schema(description= "方案名称")
    private String name;

    @Schema(description= "是否为默认值（0：否，1；是）")
    private Integer defaultFlag;
}
