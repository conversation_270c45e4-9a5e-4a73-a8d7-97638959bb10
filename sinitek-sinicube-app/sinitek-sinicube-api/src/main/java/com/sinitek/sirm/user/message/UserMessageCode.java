package com.sinitek.sirm.user.message;

/**
 * 用户系列的Message Code 常量
 *
 * <AUTHOR>
 * @date 2020-08-20
 */
public class UserMessageCode {

    /**
     * 默认登录密码
     */
    public final static String DEFAULT_LOGON_PSW = "010401";

    /**
     * 批量离职成功
     */
    public final static String BATCH_RESIGN_SUCCESS = "010402";

    /**
     * 当前密码输入错误！
     */
    public final static String CURRENT_PSW_ERROR = "010403";

    /**
     * 请输入新密码！
     */
    public final static String NEW_PSW_CAN_NOT_NULL = "010404";

    /**
     * 请输入确认新密码！
     */
    public final static String NEW_PSW_CONFIRM = "010405";

    /**
     * 新密码和确认新密码不一致！
     */
    public final static String NEW_PSW_CONFIRM_ERROR = "010406";

    /**
     * 新修改的密码与前[{0}]次修改过的密码中的一个重复，请重新设置新密码！
     */
    public final static String NEW_PW_EXIST_REPEAT = "010407";

    /**
     * 密码长度为[{0}]位以上
     */
    public final static String PSW_LENGTH_N_ABOVE = "010408";

    /**
     * 参数【ORGSIRM018】配置错误，只允许输入1-4的整数。或修改组织机构参数【以上4选N (n大于0小于4)】配置项。
     */
    public final static String ORGSIRM018_ERROR = "010409";

    /**
     * 密码必须包含：
     */
    public final static String PSW_STRENGTH = "010410";

    /**
     * 数字，
     */
    public final static String DIGITAL = "010411";

    /**
     * 小写字母，
     */
    public final static String LOWER_CASE_LETTERS = "010412";

    /**
     * 大写字母，
     */
    public final static String UPPERCASE_LETTER = "010413";

    /**
     * 特殊字符，
     */
    public final static String SPECIAL_CHARACTERS = "010414";

    /**
     * 以上条件中的
     */
    public final static String AMONG_THE_ABOVE_CONDITIONS = "010415";

    /**
     * 个
     */
    public final static String INDIVIDUAL = "010416";

    /**
     * 密码不能包含用户名
     */
    public final static String PSW_CANNOT_CONTAIN_USERNAME = "010417";

    /**
     * 0,您的密码是默认密码
     */
    public final static String YOU_PSW_IS_DEFAULT = "010418";

    /**
     * 1,您的密码是默认密码，必须先修改密码才能正常使用本系统。
     */
    public final static String YOU_PSW_IS_DEFAULT_NEED_UPDATE = "010419";

    /**
     * 旧密码不能为空
     */
    public final static String OLD_PASSWORD_CON_NOT_BE_BLANK = "010480";


    /**
     * 新密码不能为空
     */
    public final static String NEW_PASSWORD_CON_NOT_BE_BLANK = "010481";

    /**
     * 确认新密码不能为空
     */
    public final static String CONFIRM_NEW_PASSWORD_CON_NOT_BE_BLANK = "010482";
    /**
     * [{}]用户已经为锁定状态,无需锁定！
     */
    public final static String USER_ALREADY_LOCKING = "010420";

    /**
     * 部分数据异常
     */
    public final static String SOME_DATA_IS_ABNORMAL = "010421";

    /**
     * 增加员工[{}]的下属员工
     */
    public final static String SAVE_SUBORDINATE_EMPLOYEE = "010422";

    /**
     * 根节点下增加下属员工
     */
    public final static String ROOT_SAVE_SUBORDINATE_EMPLOYEE = "010423";

    /**
     * 导入失败：请首先设置用户初始密码！
     */
    public final static String IMPORT_ERROR_NEED_SET_USER_DEFAULT_PSW = "010424";

    /**
     * [{0}]对应的组织结构方案数据不存在
     */
    public final static String ORG_SCHEME_NOT_EXIST = "010425";

    /**
     * 模板下载失败！
     */
    public final static String TEMPLATE_DOWNLOAD_FAIL = "010426";

    /**
     * [{0}] 用户还未到入职日期，不能离职！
     */
    public final static String USER_NOT_ENTRY = "010427";

    /**
     * 登陆名不能为空
     */
    public final static String LOGIN_NAME_CAN_NOT_NULL = "010428";

    /**
     * 员工名称不能为空
     */
    public final static String EMP_NAME_CAN_NOT_NULL = "010429";

    /**
     * 登录名不能超过50个字符
     */
    public final static String LOGIN_NAME_LENGTH_TOO_BIG = "010430";

    /**
     * 登录名[{0}]：登录名不能超过50个字符
     */
    public final static String IMPORT_LOGIN_NAME_LENGTH_TOO_BIG = "com.sinitek.sirm.user.dto.import_login_name_length_too_big";

    /**
     * 员工姓名不能超过30个字符
     */
    public final static String EMP_NAME_LENGTH_TOO_BIG = "010431";

    /**
     * 登录名[{0}]：员工姓名不能超过30个字符
     */
    public final static String IMPORT_EMP_NAME_LENGTH_TOO_BIG = "com.sinitek.sirm.user.dto.import_emp_name_length_too_big";

    /**
     * 员工姓名拼音不能超过200个字符
     */
    public final static String EMP_NAME_PY_LENGTH_TOO_BIG = "010432";

    /**
     * 邮箱不能超过100个字符
     */
    public final static String EMAIL_LENGTH_TOO_BIG = "010433";

    /**
     * 办公电话不能超过30个字符
     */
    public final static String OFFICE_PHONE_LENGTH_TOO_BIG = "010434";

    /**
     * 手机不能超过30个字符
     */
    public final static String PHONE_LENGTH_TOO_BIG = "010435";

    /**
     * 职位不能超过30个字符
     */
    public final static String POSITION_LENGTH_TOO_BIG = "010436";

    /**
     * 个人简历不能超过1000个字符
     */
    public final static String RESUME_NAME_LENGTH_TOO_BIG = "010437";

    /**
     * OrgId有误,没有对应的组织结构数据
     */
    public final static String ORG_ID_ERROR_NOT_DATA = "010438";

    /**
     * OrgId不能为空
     */
    public final static String ORG_ID_CAN_NOT_NULL = "010439";

    /**
     * [{0}]等用户保存失败,用户部分信息存在丢失,请联系管理员修复
     */
    public final static String EMP_INFO_LOST = "010440";

    /**
     * 读取的结果为空,请检查文件的类型或数据
     */
    public final static String READ_DATA_IS_NULL = "010441";

    /**
     * 请检查数据的完整性:登录名和姓名为必填项。
     */
    public final static String LOGIN_NAME_MUST = "010442";

    /**
     * 登录名[{0}]：无法导入离职状态的用户，请检查！
     */
    public final static String CAN_NOT_IMPORT_DEPARTURE_USER = "010443";

    /**
     * 登录名[{0}]已存在，请检查！
     */
    public final static String LOGIN_NAME_EXISTING = "010444";

    /**
     *登录名不能使用$开头 美元符
     */
    public final static String LOGIN_NAME_CAN_NOT_DOLLAR_SIGN = "010445";

    /**
     * 登录名不能使用@开头
     */
    public final static String LOGIN_NAME_CAN_NOT_SPECIAL_CHARACTERS = "010446";

    /**
     * 登录名[{0}]:登录名由字母数字下划线英文句号@$等组成
     */
    public final static String LOGIN_NAME_ONLY_REGX = "010447";

    /**
     * 登录名[{0}]：岗位部门名称[{1}]不存在，请确认路径
     */
    public final static String POSITION_PATH_NOT_EXIST = "010448";

    /**
     * 以上邮箱不正确！
     */
    public final static String ABOVE_EMAIL_IS_INCORRECT = "010449";

    /**
     * 以上岗位不存在！
     */
    public final static String ABOVE_POSITION_DOES_NOT_EXIST = "010450";

    /**
     * 以上小组不存在！
     */
    public final static String ABOVE_TEAM_DOES_NOT_EXIST = "010451";

    /**
     * 以上角色不存在！
     */
    public final static String ABOVE_ROLE_DOES_NOT_EXIST = "010452";

    /**
     * 您当前的密码将于[{0}]天后失效，预计过期时间为[{1}]
     */
    public final static String YOUR_PSW_N_DAYS_AFTER_INVALID = "010453";

    /**
     * 您使用的账户是首次登录本系统，必须先修改密码才能正常使用本系统.
     */
    public final static String FIRST_LOGIN_PROMPT = "010454";

    /**
     * 用户{0}已锁定,{1}分钟后自动解锁，您也可以联系管理员人工解锁.
     */
    public final static String USER_LOCK_TIME_PROMPT = "010455";

    /**
     * 用户名或密码错误，再有 {0} 次错误输入，该用户将被锁定
     */
    public final static String PD_ERROR_PROMPT = "010456";

    /**
     * 登录名[{0}]：员工姓名不能以下划线（_）开头
     */
    public final static String EMP_NAME_CAN_NOT_UNDERSCORE_START = "010457";

    /**
     * 登录名[{0}]：输入的字符由中文、英文字母、数字、下划线（_）组成
     */
    public final static String EMP_NAME_CONTENT_LIMIT = "010458";

    /**
     * 登录名[{0}]：所属机构名称为必填项
     */
    public final static String TENANT_NAME_MUST = "010459";

    /**
     * 登录名[{0}]：导入失败，没有机构[{0}]的操作权限
     */
    public final static String TENANT_AUTH_NOT_EXIST = "010460";

    /**
     * 登录名[{0}]：所属机构[{1}]不存在
     */
    public final static String TENANT_NOT_EXIST = "010461";

    /**
     * 请检查数据的正确性：数据库中用户[{0}]所属机构不能为空
     */
    public final static String DB_USER_TENANT_NOT_EXIST = "010462";

    /**
     * 用户[{0}]已经存在所属机构，不能更改为其他机构
     */
    public final static String USER_TENANT_EXIST = "010463";

    /**
     * 手机号由空格、数字、加号和连接符(-）组成
     */
    public final static String USER_PHONE_CHECK = "010464";

    /**
     * 邮箱由@符号组成
     */
    public final static String USER_EMAIL_CHEK = "010465";

    /**
     * 新密码和旧密码不能相同！
     */
    public final static String NEW_OLD_PWD_CANT_SAME = "010466";

    /**
     * 新密码和初始密码不能相同
     */
    public final static String NEW_ORI_PWD_CANT_SAME = "010467";

    /**
     * 用户标识令牌无法解析
     */
    public final static String USER_FLAG_TOKEN_ERROR = "010468";

    /**
     * 账号不存在
     */
    public final static String ACCOUNT_EXIST_NOT = "010469";


    /**
     * 邮箱已存在
     */
    public final static String EMAIL_ALREADY_EXIST = "010471";

    /**
     * 手机已存在
     */
    public final static String PHONE_ALREADY_EXIST = "010472";

    /**
     * 重置失败: 用户邮箱不存在!
     */
    public final static String RESET_PWD_FAIL_EMAIL_NOT_EXIST = "010473";

    /**
     * 重置成功,密码已发送到用户邮箱!
     */
    public final static String RESET_PWD_SUCCESS = "010474";

    /**
     * 存在无邮箱的用户,无邮箱用户不会进行密码重置,是否确认继续?
     */
    public final static String USER_HAVE_NOT_EMAIL_CONFIRM = "010475";

    /**
     * 用户: [{0}]的登陆名存在重复,请调整
     */
    public final static String IMPORT_REPEAT_NAME_USER_LIST = "010476";

    /**
     * 邮箱内容不能为空
     */
    public final static String EMAIL_BOX_NOT_BLANK = "010477";

    /**
     * 当前用户为离职状态，只做密码重置，不发送邮件
     */
    public final static String NO_SEND_EMAIL = "010478";

    /**
     * 用户重置密码提醒消息模板{0}不存在,请联系管理员补充该提醒模板
     */
    public final static String USER_RESET_PASSWORDS_REMIND_TEMPLATE_NOT_EXIST = "010600";

    /**
     * 用户: [{0}]的邮箱存在重复,请调整
     */
    public final static String IMPORT_REPEAT_EMAIL_USER_LIST = "010483";

    /**
     * 邮箱[{0}]已存在，请检查！
     */
    public final static String EMAIL_EXISTING = "010484";

    /**
     * 登录名[{0}]：邮箱[{1}]不能超过100个字符
     */
    public final static String IMPORT_EMAIL_LONG = "0104783";

    /**
     * 批量离职成功
     */
    public final static String BATCH_INSERVICE_SUCCESS = "010485";

    /**
     * 用户: [{0}]的手机存在重复,请调整
     */
    public final static String IMPORT_REPEAT_MOBILE_USER_LIST = "010486";

    /**
     * excel数据设置不正确,请参考模板提示填写
     */
    public final static String IMPORT_USER_EXCEL_FIELD_ERROR = "010487";

    /**
     * 用户: [{0}]的到期时间不能超过自身机构[{1}]的到期时间
     */
    public final static String EXPIRE_TIME_NO_MORE_THAN_MECHANISMS = "010488";

    /**
     * 以下用户的到期时间不能超过自身机构的到期时间：{0}
     */
    public final static String USERS_EXPIRE_TIME_NO_MORE_THAN_MECHANISMS = "010489";

    /**
     * 请先配置用户来源枚举配置,COMMON.user-datasrc
     */
    public final static String SET_USER_DATASRC_ENUM = "010490";

    /**
     * 请先配置用户来源枚举项：{0}
     */
    public final static String SET_USER_DATASRC_ENUM_ITEM = "010491";

    /**
     * 导入文件的第[{0}]行，第[{1}]列：数据[{2}]数据格式设置不正确,请参考模板提示填写
     */
    public final static String IMPORT_USER_EXCEL_FORMAT_ERROR = "010492";

    /**
     * 登录名不能以$,@开头
     */
    public final static String USERNAME_CAN_NOT_STAR_WITH = "010493";

    /**
     * 登录名由字母数字下划线英文句号@$等组成
     */
    public final static String USERNAME_CHECK = "010494";

    /**
     * 员工姓名只能由中文、英文字母、数字、下划线组成，并且不能以下划线开头
     */
    public static final String EMP_NAME_CHECK = "0609025";

    /**
     * 用户: [{0}]到期日期设置错误，请检查输入的到期时间数据
     */
    public final static String EXPIRE_TIME_ERROR = "010495";

    /**
     * 系统公共参数中，用户默认初始密码不能为空
     */
    public final static String DEFAULT_PSW_CAN_NOT_NULL = "010496";

    /**
     * 选择用户不能为空
     */
    public final static String SELECT_USER_CAN_NOT_NULL = "010497";

    /**
     * 目标用户不能为空
     */
    public final static String TARGET_USER_CAN_NOT_NULL = "010498";

    /**
     * 要转移的用户涉及范围不能为空
     */
    public final static String TRANSFER_ID_MAP_CAN_NOT_NULL = "010499";

    /**
     * 员工姓名拼音只能由英文字母、数字、下划线和连字符组成，必须以字母开头
     */
    public final static String NAME_PY_VALIDATE= "010500";

}
