package com.sinitek.sirm.framework.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/30
 * 上传文件DTO
 */
@Data
@Schema(description = "上传文件DTO")
public class UploaderFileDTO {

    @Schema(description = "附件id")
    private String id;

    @Schema(description = "附件名称")
    private String name;

    @Schema(description = "附件类型")
    private Integer type;

    @Schema(description = "访问token")
    private String accesstoken;

    @Schema(description = "附件大小")
    private Long size;

    @Schema(description = "附件的md5")
    private String md5;

    @Schema(description = "附件上传人Id")
    private String ownerId;

    @Schema(description = "附件上传人名称")
    private String ownerName;

    @Schema(description = "附件上传时间")
    private Date createTime;

}
