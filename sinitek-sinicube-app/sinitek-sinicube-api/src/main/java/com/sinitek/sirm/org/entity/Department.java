package com.sinitek.sirm.org.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * File Desc:   部门
 * Product Name: SIRM
 * Module Name: org
 * Author:      潘虹
 * History:     11-5-10 created by 潘虹
 */
@Data
@Schema(description = "部门信息模型")
@EqualsAndHashCode(callSuper = true)
public class Department extends OrgCommonInfo {

    @Schema(description = "部门ID")
    private String orgid;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "部门描述")
    private String description;

    private String parentId;
}
