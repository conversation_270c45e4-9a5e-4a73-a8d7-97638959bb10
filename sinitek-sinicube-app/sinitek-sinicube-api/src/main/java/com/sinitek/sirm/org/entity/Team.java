package com.sinitek.sirm.org.entity;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: com.sinitek.sirm.org.busin.entity
 * Author:      潘虹
 * History:     11-5-26 created by 潘虹
 */
@SuppressWarnings("squid:EntityContentCheck")
@Schema(description = "小组信息模型")
public class Team extends OrgCommonInfo {

    @Schema(description = "小组Id")
    private String teamId;

    @Schema(description = "小组名称")
    private String teamName;

    @Schema(description = "小组描述")
    private String teamDescription;

    @Schema(description = "是否是研究小组")
    private boolean researchFlag;

    @Schema(description = "小组的上级组织结构")
    private String parentId;

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamDescription() {
        return teamDescription;
    }

    public void setTeamDescription(String teamDescription) {
        this.teamDescription = teamDescription;
    }

    /**
     * 是否是研究小组
     *
     * @return
     */
    public boolean isResearchFlag() {
        return researchFlag;
    }

    public void setResearchFlag(boolean researchFlag) {
        this.researchFlag = researchFlag;
    }
}
