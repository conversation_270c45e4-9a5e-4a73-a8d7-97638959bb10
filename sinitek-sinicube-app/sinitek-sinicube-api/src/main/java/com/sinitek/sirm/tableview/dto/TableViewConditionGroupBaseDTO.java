package com.sinitek.sirm.tableview.dto;

import com.sinitek.sirm.tableview.message.TableViewConditionGroupMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 表格视图条件组 基础DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@Schema(description = "表格视图条件组-基础DTO")
public class TableViewConditionGroupBaseDTO {

    @Schema(description = "所属视图Id")
    private Long viewId;

    @Schema(description = "组内关系, 1=全部、2=任何", required = true)
    @NotNull(message = "{" + TableViewConditionGroupMessage.GROUP_RELATION_CAN_NOT_NULL + "}")
    private Integer groupRelation;


}
