package com.sinitek.sirm.right.dto;

import com.sinitek.cloud.sirmapp.right.dto.RightAuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 用于findEnableAuthedObjects返回值
 * <AUTHOR>
 * @date 2022/5/9
 */

@Schema(description = "用于findEnableAuthedObjects返回值的DTO")
@Data
public class RightAuthEnableResultDTO {

    @Schema(description = "所有权限")
    private List<RightAuthDTO> allRightAuth;

    @Schema(description = "自身权限")
    private List<RightAuthDTO> selfRightAuth;
}
