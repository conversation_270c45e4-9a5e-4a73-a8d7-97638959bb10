package com.sinitek.sirm.calendar.message;

/**
 * <AUTHOR>
 * @Date 2021/12/8
 */
public class CalendarEventMessageCode {

    /**
     * 日程信息sourceId【{0}】有误,请输入长整型数据
     */
    public static final String SOURCEID_DATA_ERROR = "0403001";

    /**
     * 日程类型为空
     */
    public static final String CALENDAR_EVENT_TYPE_NULL = "0403002";

    /**
     * 日程开始时间为空
     */
    public static final String CALENDAR_EVENT_START_NULL = "0403003";

    /**
     * 日程结束时间为空
     */
    public static final String CALENDAR_EVENT_END_NULL = "0403004";

    /**
     * 日程标题为空
     */
    public static final String CALENDAR_EVENT_TITLE_NULL = "0403005";

    /**
     * 日程内容为空
     */
    public static final String CALENDAR_EVENT_CONTENT_NULL = "0403006";

    /**
     * 未找到名称为【{0}】的bean容器
     */
    public static final String CALENDAR_EVENT_REMIND_ERROR = "0403007";

    /**
     * 通过beanName【{0}】调用接口返回的员工数据为空
     */
    public static final String CALENDAR_EVENT_REMIND_NULL = "0403008";

    /**
     * 日程标题最大长度为100
     */
    public static final String CALENDAR_EVENT_TITLE_LENGTH = "0403009";

    /**
     * 日程是否提醒为空
     */
    public static final String CALENDAR_EVENT_REMIND_IS_NULL = "0403010";

    /**
     * 日程删除失败
     */
    public static final String CALENDAR_EVENT_DELETE_ERROR = "0403011";


    /**
     * 企业微信保存日程失败
     */
    public static final String CALENDAR_EVENT_SYNC_WX_WORK_ERROR = "0403013";

    /**
     * json数据转map集合异常
     */
    public static final String JSON_TO_MAP = "0403014";

    /**
     * 企业微信更新日程失败
     */
    public static final String CALENDAR_EVENT_SYNC_WX_WORK_UPDATE_ERROR = "0403015";

    /**
     * 企业微信删除日程失败
     */
    public static final String CALENDAR_EVENT_SYNC_WX_WORK_DELETE_ERROR = "0403016";

    /**
     * 自定义日程保存失败
     */
    public static final String CALENDAR_EVENT_CUSTOME_SAVE_ERROR = "0403017";

    /**
     * 日程数据为空
     */
    public static final String CALENDAR_EVENT_DATA_IS_UNLL = "0403018";

    /**
     * 自定义日程类型未找到对应的执行类，无法获取日程数据
     */
    public static final String CALENDAR_EVENT_CUSTOME_CLASS_NOT_FIND = "0403019";

    /**
     * 日程内容最大长度为500
     */
    public static final String CALENDAR_EVENT_CONTENT_LENGTH = "0403020";

    /**
     * 日程
     */
    public static final String CALENDAR_EVENT= "0403021";

    /**
     * 日程开始时间不能大于结束时间
     */
    public static final String CALENDAR_START_GREATER_END= "0403022";

    /**
     * 编辑日程，重复编辑类型必须为必填：0(只编辑此日程)，1(编辑此日程及后续日程)
     */
    public static final String CALENDAR_EVENT_EDIT_TYPE_NOT_NULL= "0403023";

}
