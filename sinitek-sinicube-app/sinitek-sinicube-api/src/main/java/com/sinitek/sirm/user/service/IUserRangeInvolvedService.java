package com.sinitek.sirm.user.service;

import java.util.List;

/**
 * 用户涉及范围顶层接口
 *  - 每个模块实现该接口提供查询和修改服务
 *
 * <AUTHOR>
 * date 2023-08-29
 */
public interface IUserRangeInvolvedService<T> {

    /**
     * 获取实现类的标识编码
     * @return
     */
    String getFlagCode();

    /**
     * 获取业务日志的显示名
     * @return
     */
    String getBusinessLogDisplayName();

    /**
     * 根据用户OrgId获取用户涉及范围
     * @param userOrgId
     * @return
     */
    List<T> findUserRangeInvolvedListByUserOrgId(String userOrgId);

    /**
     * 转移用户
     * @param idList
     * @param originUserOrgId
     * @param targetUserOrgId
     */
    void transferUserRangeInvolved(List<String> idList,String originUserOrgId, String targetUserOrgId);
}
