package com.sinitek.sirm.org.enumerate;

/**
 * 各个组织结构的ICON图标, 统一使用枚举管理,而不是每个地方都单独定义
 *
 * <AUTHOR>
 * @date 2021-01-29
 */
public enum OrgIconEnumerate {

    /**
     * 公司/部门 的图标
     */
    COMPANY_ICON("iconfont icon-gongsi", "公司/部门 的图标"),

    /**
     * 岗位/角色 的图标
     */
    POST_ICON("iconfont icon-gangwei", "岗位/角色 的图标"),

    /**
     * 小组 的图标
     */
    GROUP_ICON("iconfont icon-xiaozu", "小组 的图标"),

    /**
     * 员工 的图标
     */
    PERSON_ICON("iconfont icon-renyuan", "员工 的图标");

    private String code;
    private String desc;

    OrgIconEnumerate(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
