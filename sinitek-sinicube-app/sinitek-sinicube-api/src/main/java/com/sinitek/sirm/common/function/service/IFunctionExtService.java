package com.sinitek.sirm.common.function.service;

import com.sinitek.sirm.common.function.dto.FunctionDTO;

import java.util.List;

/**
 * 功能权限统一API
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface IFunctionExtService {

    /**
     * 根据功能代码查询功能
     *
     * @param code
     * @return
     */
    public FunctionDTO getFunctionByCode(String code);

    /**
     * 获取当前用户所有的被授权的功能
     * @return 当前用户没有被授权的功能
     */
    List<FunctionDTO> findAuthorizedFunction(String orgId);

    /**
     * 查询所有功能权限
     *
     * @return
     */
    public List<FunctionDTO> findAllFunctions();
}
