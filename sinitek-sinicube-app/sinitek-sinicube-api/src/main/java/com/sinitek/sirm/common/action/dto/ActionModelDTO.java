package com.sinitek.sirm.common.action.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.common.action.support.ISiniCubeAction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;

/**
 * 动作模型数据对象
 *
 * <AUTHOR>
 * @date 2021/9/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "动作模型数据对象")
public class ActionModelDTO {

    @Schema(description = "动作名称")
    private String name;

    @Schema(description = "动作唯一编码")
    private String code;

    @Schema(description = "动作类型：1 本地，2 远程")
    private int type;

    @Schema(description = "是否需要Vue组件")
    private boolean needVueComponent;

    @JsonIgnore
    @Schema(description = "动作处理器实例")
    private ISiniCubeAction<T, Object> instant;

}
