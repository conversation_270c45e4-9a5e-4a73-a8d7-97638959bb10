package com.sinitek.sirm.tableview.message;

/**
 * 表格视图条件组 MessageCode
 *
 * <AUTHOR>
 * date 2023-07-11
 */
public class TableViewConditionGroupMessage {

    private TableViewConditionGroupMessage() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 组内关系, 1=全部、2=任何不能为空
     */
    public static final String GROUP_RELATION_CAN_NOT_NULL = "com.sinitek.sirm.tableview.group_relation_can_not_null";

    /**
     * 不支持第{0}组的组内关系: {1}
     */
    public static final String GROUP_RELATION_NOT_SUPPORT = "com.sinitek.sirm.tableview.group_relation_not_support";
}
