package com.sinitek.sirm.routine.holiday.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/8/25
 */
@Data
@Schema(description = "节假日导入信息")
public class ImportHolidayDTO {

    @Schema(description = "选择的年份")
    private String year;

    @Schema(description = "上传的附件")
    private UploadDTO importFile;

    @Schema(description = "导入方式")
    private String importType;

    @Schema(description= "节假日方案Code")
    private String schemeCode;
}
