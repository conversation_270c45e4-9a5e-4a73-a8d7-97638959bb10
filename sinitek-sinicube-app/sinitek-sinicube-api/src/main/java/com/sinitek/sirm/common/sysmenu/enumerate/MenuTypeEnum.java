package com.sinitek.sirm.common.sysmenu.enumerate;

/**
 * 菜单类型 枚举
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
public enum MenuTypeEnum {

    /**
     * 菜单组
     */
    GROUP("1", "菜单组"),

    /**
     * 路由菜单
     */
    ROUTE("2","路由菜单"),

    /**
     * 外部菜单
     */
    EXTERNAL("3", "外部菜单"),

    /**
     * 兼容菜单
     */
    COMPATIBLE("4", "兼容菜单");

    private String menuType;

    private String desc;

    MenuTypeEnum(String menuType, String desc) {
        this.menuType = menuType;
        this.desc = desc;
    }

    public String getMenuType() {
        return menuType;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据menuType获取到desc
     * @param menuType
     * @return
     */
    public static String getDescByMenuType(String menuType) {
        for (MenuTypeEnum menuTypeEnum : MenuTypeEnum.values()) {
            if (menuTypeEnum.getMenuType().equals(menuType)) {
                return menuTypeEnum.getDesc();
            }
        }
        return "";
    }
}

