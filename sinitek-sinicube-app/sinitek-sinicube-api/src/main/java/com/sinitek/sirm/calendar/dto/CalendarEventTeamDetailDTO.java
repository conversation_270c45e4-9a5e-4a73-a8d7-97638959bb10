package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * Date  2022/10/17
 */
@Data
@Schema(description = "日程团队详情DTO")
@EqualsAndHashCode(callSuper = true)
public class CalendarEventTeamDetailDTO extends CalendarEventDetailDTO {

    @Schema(description = "员工id集合")
    List<String> empIds;
}
