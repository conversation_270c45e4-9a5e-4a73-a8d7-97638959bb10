package com.sinitek.sirm.common.event.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/6/25
 */
@Data
@Schema(description = "事件触发历史结果dto")
public class SiniCubeEventPublishLogResultDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "跟踪id")
    private String traceId;

    @Schema(description = "事件对象")
    private String name;

    @Schema(description = "事件对象名称")
    private String displayName;

    @Schema(description = "触发源数据")
    private String sourceData;

    @Schema(description = "事件模式")
    private Integer type;

    @Schema(description = "本地发布对应的value")
    private String localValue;

    @Schema(description = "远程发布对应的value")
    private String remoteValue;

    @Schema(description = "触发结果(数据库保存)")
    private Integer result;

    @Schema(description = "触发结果(返回给前端)")
    private String resultValue;

    @Schema(description = "触发开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;

    @Schema(description = "触发结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;

    @Schema(description = "发布本地失败原因")
    private String localReason;

    @Schema(description = "发布远程失败原因")
    private String remoteReason;
}
