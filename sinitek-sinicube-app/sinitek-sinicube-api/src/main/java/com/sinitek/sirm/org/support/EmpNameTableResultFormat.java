package com.sinitek.sirm.org.support;

import com.google.common.base.Splitter;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.support.data.BaseSingleFieldTableResultFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.org.service.IOrgService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * User: 王志华
 *
 * Date: 2020/2/7
 *
 * Time: 3:59 下午
 */
public class EmpNameTableResultFormat<T> extends BaseSingleFieldTableResultFormat<T> {

    public EmpNameTableResultFormat(String formattingFieldName,
                                    String formatedFieldName) {
        super(formattingFieldName, formatedFieldName);
    }

    @Override
    protected Map<Object, Object> getDataMappingByFormattingFieldValues(List formattingFieldValues) {
        Map<Object, Object> dataMapping = new HashMap<>();
        IOrgService orgService = SpringFactory.getBean(IOrgService.class);

        List<String> finalFormattingFieldValueList = new ArrayList<>();
        for (Object formattingFieldValue : formattingFieldValues) {
            List<String> valueList = Splitter.on(GlobalConstant.COMMA).splitToList(formattingFieldValue.toString());
            if (CollectionUtils.isEmpty(valueList)) {
                continue;
            }
            finalFormattingFieldValueList.addAll(valueList);
        }

        Map<String, String> orgNameMap = orgService.getOrgNameMapByOrgIdList(finalFormattingFieldValueList);

        for (Object formattingFieldValue : formattingFieldValues) {
            List<String> valueList = Splitter.on(GlobalConstant.COMMA).splitToList(formattingFieldValue.toString());
            if (CollectionUtils.isEmpty(valueList)) {
                continue;
            }
            StringBuilder orgName = new StringBuilder();
            for (String value : valueList) {
                orgName.append(orgNameMap.get(value))
                        .append(GlobalConstant.COMMA);
            }
            dataMapping.put(formattingFieldValue, StringUtils.removeEnd(orgName.toString(), GlobalConstant.COMMA));
        }
        return dataMapping;
    }
}
