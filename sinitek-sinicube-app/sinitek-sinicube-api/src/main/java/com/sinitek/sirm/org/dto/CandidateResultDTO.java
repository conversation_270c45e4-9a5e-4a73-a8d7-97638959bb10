package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@Data
@Schema(description = "组织结构对象")
public class CandidateResultDTO {
    @Schema(description = "组织结构id")
    private String id;
    @Schema(description = "组织结构name")
    private String label;

    private int orgtype;

    private String key;

    @Schema(description = "是否有效")
    private Boolean inservice;

    @Schema(description = "用户情况下为用户名,其他类型同label一样")
    private String text;

    @Schema(description = "性别,仅组织结构为用户时有值")
    private Integer sex;

    @Schema(description = "工作地,仅组织结构为用户时有值")
    private String city;

    @Schema(description = "用户所属岗位,仅组织结构为用户时有值")
    private List<EmpParentPositionDTO> positionList;

    @Schema(description = "用户邮箱,仅组织结构为用户时有值")
    private String email;
}
