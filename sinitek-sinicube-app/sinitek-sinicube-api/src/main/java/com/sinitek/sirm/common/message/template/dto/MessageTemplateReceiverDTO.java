package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 描述
 *
 * <AUTHOR>
 * @date 2021/6/17
 */
@Schema(description = "消息模板接收人数据对象")
@Data
public class MessageTemplateReceiverDTO {

    @Schema(description= "消息模板Id")
    private Long templateId;

    @Schema(description= "组织结构Id")
    private String orgId;

    @Schema(description= "组织结构类型")
    private Integer orgType;

    @Schema(description= "类型")
    private String type;

    @Schema(description = "提醒对象类型对应的值：选人方案-方案编码，选人控件-组织结构id拼接")
    private String value;

    @Schema(description = "接收人类型,0:抄送对象 1:提醒对象")
    private Integer receiverType;

}
