package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.action.message.ActionMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * 解除动作绑定的参数对象
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
@Schema(description = "解除动作绑定的参数对象")
@Data
public class UnBindActionDTO {

    @Schema(description = "要解绑的动作id集合")
    private List<Long> actionIds;

    @Schema(description = "绑定对象id")
    @NotBlank(message = "{"+ ActionMessageCode.BIND_SOURCE_ID_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ ActionMessageCode.BIND_SOURCE_ID_LENGTH_TOO_BIG +"}")
    private String sourceId;

    @Schema(description = "绑定对象名")
    @NotBlank(message = "{"+ ActionMessageCode.BIND_SOURCE_NAME_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ ActionMessageCode.BIND_SOURCE_NAME_LENGTH_TOO_BIG +"}")
    private String sourceName;

    @Schema(description = "删除绑定对象关联的所有动作，默认false：只删除指定动作")
    private Boolean deleteAll = false;

}
