package com.sinitek.sirm.common.message.template.enumerate;

import com.sinitek.base.enumsupport.AbstractEnumItem;

/**
 * 消息发送状态枚举类
 * Created by IntelliJ IDEA.
 * User: 李琦明
 * Date: 14-11-04
 * Time: 上午16:12
 */
public class MessageImportantLevelEnum extends AbstractEnumItem {
    protected MessageImportantLevelEnum(String enumItemName, int enumItemValue,
                                        String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }
    public static final MessageImportantLevelEnum LEVEL_HIGH = new MessageImportantLevelEnum("1", 1, "紧急", null);
    public static final MessageImportantLevelEnum LEVEL_MIDDLE = new MessageImportantLevelEnum("3", 3, "普通[默认]", null);
    public static final MessageImportantLevelEnum LEVEL_LOW = new MessageImportantLevelEnum("3", 5, "低", null);
}
