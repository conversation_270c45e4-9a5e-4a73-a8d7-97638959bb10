package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/17
 */
@Schema(description ="发送消息失败的接收人结果DTO")
@Data
public class MessageSendFailedReceiverDTO{

    @Schema(description = "员工id")
    private String empId;

    @Schema(description = "接收对象名称")
    private String receiverName;

    @Schema(description = "错误原因")
    private String reason;

    @Schema(description = "发送方式值")
    private Integer sendType;

    @Schema(description = "发送方式对应的名称值")
    private String sendTypeValue;
}
