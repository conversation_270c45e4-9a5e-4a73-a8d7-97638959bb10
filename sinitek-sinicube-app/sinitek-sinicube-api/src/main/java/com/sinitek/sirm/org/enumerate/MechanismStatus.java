package com.sinitek.sirm.org.enumerate;

import com.sinitek.base.enumsupport.AbstractEnumItem;

/**
 * @className: MechanismStatus
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2019-07-09 17:49
 **/
public class MechanismStatus extends AbstractEnumItem {

    public static final MechanismStatus MECHANISM_DISABLE= new MechanismStatus("0", 0, "停用", (String)null);
    public static final MechanismStatus MECHANISM_ENABLE  = new MechanismStatus("1", 1, "启用", (String)null);

    protected MechanismStatus(String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }

}
