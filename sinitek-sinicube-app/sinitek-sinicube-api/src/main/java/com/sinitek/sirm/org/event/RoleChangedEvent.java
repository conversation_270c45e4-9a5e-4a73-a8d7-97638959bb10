package com.sinitek.sirm.org.event;

import com.sinitek.sirm.common.event.annotation.EventDefinition;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.org.dto.RoleDTO;

/**
 * <AUTHOR>
 * @date 2021/6/1
 * 角色改变事件驱动模型
 */
@EventDefinition(type = "SiniCube", module = "角色管理", name = "角色改变事件", brief = "当新增的时候会触发")
public class RoleChangedEvent extends SiniCubeEvent<RoleDTO> {

    public RoleChangedEvent(RoleDTO source) {
        super(source);
    }
}
