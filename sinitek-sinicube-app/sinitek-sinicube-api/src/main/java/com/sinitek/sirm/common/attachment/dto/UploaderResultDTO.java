package com.sinitek.sirm.common.attachment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 文件上传返回值DTO
 *
 * <AUTHOR>
 * @date 2023-03-10
 */
@Data
public class UploaderResultDTO {

    @Schema(description = "临时文件的唯一名称")
    private String uniqueName;

    @Schema(description = "已上传文件的编号列表")
    private List<Integer> chunkFileNumList;

    /**
     * 扩展上传Id
     *  - 用于部分文件上传场景，需要后端来生成一次文件的唯一标识。
     */
    private String extUploadId;
}
