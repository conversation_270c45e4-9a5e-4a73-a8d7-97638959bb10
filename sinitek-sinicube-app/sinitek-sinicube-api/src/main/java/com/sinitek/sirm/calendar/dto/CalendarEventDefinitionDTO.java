package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Date  2022/2/25
 */
@Data
@Schema(description = "日程类型对应的实体dto")
@NoArgsConstructor
@AllArgsConstructor
public class CalendarEventDefinitionDTO {

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "类型对应的编码")
    private String typeCode;

    @Schema(description = "类型对应的颜色")
    private String typeColor;

    @Schema(description = "注解对应的类路径")
    private String className;
}
