package com.sinitek.sirm.common.action.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/6/27
 */
@Data
@Schema(description = "动作执行历史dto")
public class ActionExcuteHistoryResultDTO{

    @Schema(description = "id")
    private Long id;

    @Schema(description = "动作id")
    private Long actionId;

    @Schema(description = "动作名称")
    private String name;

    @Schema(description = "执行动作的源数据")
    private String sourceData;

    @Schema(description = "执行类型")
    private String sourceName;

    @Schema(description = "执行类型值")
    private String typeValue;

    @Schema(description = "执行状态")
    private Integer status;

    @Schema(description = "执行状态值")
    private String statusValue;

    @Schema(description = "执行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;

    @Schema(description = "执行结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;

    @Schema(description = "执行失败原因")
    private String reason;

}

