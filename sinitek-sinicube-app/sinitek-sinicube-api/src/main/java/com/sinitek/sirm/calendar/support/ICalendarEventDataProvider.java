package com.sinitek.sirm.calendar.support;

import com.sinitek.sirm.calendar.dto.CalendarEventDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 个人日程展示需要其他业务提供数据的接口
 * <AUTHOR>
 * Date 2021/11/16
 */
public interface ICalendarEventDataProvider {

    /**
     *  提供日程信息数据
     * @param startTime  日程开始时间
     * @param endTime  日程结束时间
     * @param empId   员工id
     * @return
     */
    List<CalendarEventDTO> findCalendarEvent(Date startTime, Date endTime, String empId);

    /**
     *  提供某个月所有日程对应日期的集合
     * @param startTime 日程开始时间
     * @param endTime  日程结束时间
     * @param empId   员工id
     * @return
     */
    Set<Date> findDaysOfCalendarEvent(Date startTime, Date endTime, String empId);

}
