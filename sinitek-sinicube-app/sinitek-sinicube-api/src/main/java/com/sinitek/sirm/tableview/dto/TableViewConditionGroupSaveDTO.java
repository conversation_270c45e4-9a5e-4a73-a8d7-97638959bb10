package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 表格视图条件组-保存DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图条件组-保存DTO")
public class TableViewConditionGroupSaveDTO extends TableViewConditionGroupBaseDTO {

    @Schema(description = "主键")
    private Long id;

    @Valid
    @Schema(description = "条件列表")
    private List<TableViewConditionSaveDTO> conditionList;
}
