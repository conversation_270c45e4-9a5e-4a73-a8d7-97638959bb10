package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/16
 */
@Data
@Schema(description = "按照类型分类的日程结果")
public class CalendarEventResultDTO {

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "类型对应的编码")
    private String typeCode;

    @Schema(description = "类型对应的颜色")
    private String typeColor;

    @Schema(description = "类型相关的日程结果集合")
    private List<CalendarEventDTO> calendarEventDTOList;
}
