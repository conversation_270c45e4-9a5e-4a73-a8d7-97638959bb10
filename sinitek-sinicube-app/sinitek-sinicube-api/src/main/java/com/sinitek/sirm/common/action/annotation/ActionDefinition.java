package com.sinitek.sirm.common.action.annotation;

import com.sinitek.sirm.common.action.enumerate.ActionType;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义动作的描述注解类
 *
 * <AUTHOR>
 * @date 2021/9/7
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface ActionDefinition {

    /**
     * 动作分类，枚举，LOCAL：本地动作；REMOTE：远程动作
     * @return
     */
    ActionType type();

    /**
     * 动作业务编码，需要唯一，绑定前端vue组件的话，与vue组件的code参数对应
     * @return
     */
    String code();

    /**
     * 动作中文名称，例如发送消息、发起流程、添加日程
     * @return
     */
    String name();

    /**
     * 是否需要Vue组件，用于绑定动作时的属性设置，默认true，为true时会关联前端的vue组件（通过code关联）
     * @return
     */
    boolean needVueComponent() default true;

}
