package com.sinitek.sirm.route.dto;

import com.sinitek.sirm.common.menu.message.MenuMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/5/22
 * @Time 16:08
 */
@Getter
@Setter
@Schema(description = "路由信息DTO")
public class RouteDTO {

    @Schema(description = "路由路径")
    private String path;

    @NotBlank(message = "{"+ MenuMessageCode.TITLE_CAN_NOT_NULL +"}")
    @Schema(description = "路由标题")
    private String title;

    @Schema(description = "路由id")
    private Long objid;

    @NotNull(message = "{"+ MenuMessageCode.PARENT_PATH_CAN_NOT_NULL +"}")
    @Schema(description = "父路由id")
    private Long parentId;

    @Schema(description = "路由名称")
    private String name;

    @Schema(description = "路由组件")
    private String component;

    @Schema(description = "菜单id")
    private String menuId;
}
