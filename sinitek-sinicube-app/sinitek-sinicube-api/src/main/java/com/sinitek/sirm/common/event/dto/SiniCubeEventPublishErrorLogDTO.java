package com.sinitek.sirm.common.event.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Date  2023/2/28
 */
@Data
@Schema(description = "发布错误日志dto")
@NoArgsConstructor
@AllArgsConstructor
public class SiniCubeEventPublishErrorLogDTO {

    @Schema(description = "事件发布记录id")
    private Long publishLogId;

    @Schema(description = "发布类型")
    private Integer type;

    @Schema(description = "发布错误原因")
    private String reason;
}
