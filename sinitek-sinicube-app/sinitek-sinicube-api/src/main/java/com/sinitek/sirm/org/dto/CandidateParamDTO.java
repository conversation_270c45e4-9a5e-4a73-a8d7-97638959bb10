package com.sinitek.sirm.org.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 *  <AUTHOR>
 * Date: 2018/4/26.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "组织结构查询参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class CandidateParamDTO extends CandidateSelectOrgDTO {

    @Schema(description = "查询模式")
    private String mode = null;
    private String id = null;
    private String match = null;
    @Schema(description = "是否显示离职人员")
    private boolean showresign = false;
    @Schema(description = "是否对离职人员进行排序")
    private boolean downResign = false;
    private boolean multi = true;

    @Schema(description = "选人控件方案代码")
    private String code = "";

    @Schema(description = "选人控件上不展示的人员，存放orgid")
    private List<String> excludeEmpIds = new ArrayList<>();
}
