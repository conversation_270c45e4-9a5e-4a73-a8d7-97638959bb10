package com.sinitek.sirm.tableview.support;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.tableview.service.ITableViewCallbackService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 表格视图自定义Service层获取支持
 *
 * <AUTHOR>
 * date 2024/08/15
 */
@Component
public class TableViewSupport {

    /**
     * 根据code获取到ITableViewCallbackService
     * @param code
     * @return
     */
    public ITableViewCallbackService getTableViewCallbackService(String code) {
        List<ITableViewCallbackService> tableViewCallbackServiceList = SpringFactory.getBeans(ITableViewCallbackService.class);
        if (CollectionUtils.isEmpty(tableViewCallbackServiceList)) {
            return null;
        }

        for (ITableViewCallbackService tableViewCallbackService : tableViewCallbackServiceList) {
            if (tableViewCallbackService.isTheTableCode(code)) {
                return tableViewCallbackService;
            }
        }
        return null;
    }
}
