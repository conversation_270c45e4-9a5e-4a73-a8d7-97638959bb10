package com.sinitek.sirm.common.quartz.constant;

/**
 * Quartz相关操作的常量
 *
 * <AUTHOR>
 * @date 2021/04/06
 */
public class QuartzConstant {

    public static final String JOB_NAME = "JOB_NAME";

    public static final String TRIGGER_CATALOG = "COMMON";

    public static final String TRIGGER_TYPE = "trigger-types";

    public static final String TRIGGER_STATE = "trigger-states";

    public static final String ACTION_SOURCE_TYPE = "trigger-action-source-types";

    public static final String DEFAULT_TYPE = "DEFAULT";

    public static final String SYSTEM_TYPE = "SYSTEM";

    public static final String ACTION_TYPE = "ACTION";

    public static final String ACTION = "action";

    public static final String ROOT = "root";

    public static final String TRIGGER = "trigger";

    public static final String SOURCE_CLASS_NAME="sourceClassName";

    public static final String JOB_CONFIG_BEAN_NAME = "actionJobDetail";

    public static final String DEFAULT_TYPE_STR= "DEFAULT";

    public static final String TRIGGER_ACTION_SOURCE_NAME = "MyQuartzScheduler";

    public static final String CRON_EXPRESSION = "cronExpression";

    public static final String JOB_CLASS_NAME = "jobClassName";

    public static final String TRIGGER_NAME = "triggerName";

    public static final String COMPONENT_DATA = "componentData";

    public static final String NAME = "name";

    public static final String CODE = "code";

    public static final String TYPE = "type";

    public static final String DESCRIPTION = "description";

    public static final String URL = "url";

    public static final String DESCRIPTION_STR = "########$$%%:";

    public static final String ID = "id";

    public static final String IMPORT_UPDATE = "0";

    public static final String IMPORT_SAVE = "1";

    public static final String ACTION_SAVE = "actionSave";

    public static final String ACTION_UPDATE = "actionUpdate";

    public static final String FILE_NAME = "调度导出.xml";

    public static final String XML = "xml";

    public static final String SETTING_MODULE = "DEMO";

    public static final String SETTING_NAME = "USER_EXPIRE_REMIND";

    public static final String CLASS = "class";

    public static final String CALENDAR_NAME = "calendarName";

    public static final String TRIGGER_CALENDAR = "Holidays";

    public static final Integer SKIP_HOLIDAYS = 1;

    public static final Integer NOT_SKIP_HOLIDAYS = 0;

    public static final String HOLIDAY_STRATEGY = "holidayStrategy";

    public static final String OLD_IMPORT_TEMPLATE_ROOT = "Map";

    public static final int DOING = 1;

    public static final int DONE = 2;

    public static final int ERROR = 3;

    public static final int BREAK = 4;

    public static final int AUTO = 1;

    public static final int MANUAL = 2;

}
