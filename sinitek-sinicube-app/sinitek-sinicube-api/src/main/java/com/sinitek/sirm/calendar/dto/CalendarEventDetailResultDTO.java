package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/3/10
 */
@Data
@Schema(description = "日程查询结果dto")
public class CalendarEventDetailResultDTO {

    @Schema(description = "日程id")
    private Long id;

    @Schema(description = "日程开始时间")
    private Date startTime;

    @Schema(description = "日程结束时间")
    private Date endTime;

    @Schema(description = "日程类型对应的颜色")
    private String color;

    @Schema(description = "日程标题")
    private String title;

    @Schema(description = "日程标题")
    private String orgName;
}
