package com.sinitek.sirm.org.dto;

import com.sinitek.spirit.um.server.userdb.UserBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 在线用户信息DTO
 *
 * <AUTHOR>
 * @date 2020/06/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "在线用户信息")
public class OnLineUserDTO extends UserBase {

    @Schema(description = "用户orgId")
    private String id;

    @Schema(description = "员工姓名")
    private String displayName;

    @Schema(description = "性别", example = "1/2")
    private Integer sex;

    @Schema(description = "性别显示值", example = "男/女/保密")
    private String sexValue;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "办公电话")
    private String tel;

    @Schema(description = "登陆时间")
    private Date logonTime;

    @Schema(description = "最后一次访问时间")
    private Date lastAccessTime;
}
