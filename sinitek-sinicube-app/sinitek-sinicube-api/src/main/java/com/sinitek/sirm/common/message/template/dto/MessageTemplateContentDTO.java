package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/12
 */
@Data
@Schema(description = "消息模版内容-返回DTO")
public class MessageTemplateContentDTO {

    @Schema(description = "消息模版id，关联sirm_messagetemplate.objid")
    private Long templateId;

    @Schema(description = "消息提醒方式, 1：邮件、2：短信、4：站内信、8：企业微信。 以此类推，规则为2的n次方")
    private Integer sendType;

    @Schema(description =  "消息模版内容")
    private String content;
}
