package com.sinitek.sirm.user.factory;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.user.service.IUserRangeInvolvedService;

import java.util.List;

/**
 * 用户涉及范围服务工厂
 *
 * <AUTHOR>
 * date 2023-08-29
 */
public class UserRangeInvolveServiceFactory {

    private UserRangeInvolveServiceFactory() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 根据flagCode找到对应的用户范围实现
     * @param flagCode
     * @return
     */
    @SuppressWarnings("squid:S3740")
    public static IUserRangeInvolvedService getUserRangeInvolvedServiceByFlagCode(String flagCode) {
        List<IUserRangeInvolvedService> userRangeInvolvedServiceList = SpringFactory.getBeans(IUserRangeInvolvedService.class);

        for (IUserRangeInvolvedService<?> userRangeInvolvedService : userRangeInvolvedServiceList) {
            String finalFlagCode = userRangeInvolvedService.getFlagCode();
            if (flagCode.equalsIgnoreCase(finalFlagCode)) {
                return userRangeInvolvedService;
            }
        }
        return null;
    }
}
