package com.sinitek.sirm.org.enumerate;

/**
 * <AUTHOR>
 * @date 2025/5/14
 */
public enum DepartmentEmployeeQueryScopeEnum {

    ALL(1, "所有；存在子父级关系时，以顶层的部门为主"),
    PEERS(2, "仅同级"),
    SUBORDINATES(3, "仅下级；包含所有下级"),
    ADJACENT_DEPARTMENTS(4, "相邻部门");


    private Integer code;

    private String desc;

    DepartmentEmployeeQueryScopeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DepartmentEmployeeQueryScopeEnum getByCode(Integer code) {
        for (DepartmentEmployeeQueryScopeEnum item : DepartmentEmployeeQueryScopeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
