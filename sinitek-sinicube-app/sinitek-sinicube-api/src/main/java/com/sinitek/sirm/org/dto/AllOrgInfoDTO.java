package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @date 2025/2/18 16:58
 */
@EqualsAndHashCode
@Data
@Schema(description = "所有人员、部门、岗位、角色、小组展示信息模型")
public class AllOrgInfoDTO {
    @Schema(description = "Unit类型：员工=NULL，角色=ROLE，小组=TEAM ，岗位=POSITION，部门=UNIT")
    private String unitType;

    @Schema(description = "用户Id")
    private String orgId;

    @Schema(description = "用户名称")
    private String orgName;
}
