package com.sinitek.sirm.common.event.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.event.dto.SiniCubeEventPublishErrorLogDTO;
import com.sinitek.sirm.common.event.dto.SiniCubeEventPublishLogDTO;
import com.sinitek.sirm.common.event.dto.SiniCubeEventPublishLogQueryDTO;
import com.sinitek.sirm.common.event.dto.SiniCubeEventPublishLogResultDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.support.CommonMessageCode;

import java.util.List;

/**
 * <AUTHOR>
 * Date  2022/12/6
 */
public interface ISiniCubeEventPublishLogService {

    /**
     * 保存事件发布记录
     * @param siniCubeEventPublishLogDTO
     */
    Long saveOrUpdateSiniCubeEventPublishLog(SiniCubeEventPublishLogDTO siniCubeEventPublishLogDTO);

    /**
     * 获取事件发布记录
     * @param param
     * @return
     */
    default IPage<SiniCubeEventPublishLogResultDTO> searchSiniCubeEventPublishLog(SiniCubeEventPublishLogQueryDTO param) {
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * 批量保存事件发布错误日志
     * @param errorLogDTOList
     */
    void saveBatchSiniCubeEventPublishErrLog(List<SiniCubeEventPublishErrorLogDTO> errorLogDTOList);

    /**
     * 获取事件发布错误日志
     * @return
     */
    default List<SiniCubeEventPublishErrorLogDTO> findSiniCubeEventPublishErrorLog() {
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }
}
