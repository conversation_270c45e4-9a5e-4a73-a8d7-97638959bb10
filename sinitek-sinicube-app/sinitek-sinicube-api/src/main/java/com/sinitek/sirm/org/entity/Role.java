package com.sinitek.sirm.org.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: com.sinitek.sirm.org.busin.entity
 * Author:      潘虹
 * History:     11-7-26 created by 潘虹
 */
@Data
@Schema(description = "角色信息模型")
@EqualsAndHashCode(callSuper = true)
public class Role extends OrgCommonInfo {

    @Schema(description = "角色Id")
    private String objid;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "小组的上级组织结构")
    private String parentId;
}
