package com.sinitek.sirm.setting.service;

import com.sinitek.cloud.sirmapp.setting.dto.SettingDTO;
import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;

import java.util.Map;

/**
 * Setting 统一Api
 *
 * <AUTHOR>
 * @date 2021-08-26
 */
public interface ISettingExtService {

    /**
     * 获取临时文件路径
     * @return
     */
    String getTempDir();

    /**
     * 根据 module 和 name获取SirmSetting
     *
     * @param module
     * @param name
     * @return
     */
    SettingDTO getSettingByModuleAndName(String module, String name);

    /**
     * 根据 module 和 name获取 Value
     *
     * @param module
     * @param name
     * @param defaultValue
     * @return
     */
    String getSettingValueByModuleAndName(String module, String name, String defaultValue);

    /**
     * 根据module 获取全部setting map
     * @param module
     * @return
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<String, SettingDTO> getSettingsByModule(String module);

    /**
     * 根据模块名称、参数名称、参数值添加或修改参数
     * @param module
     * @param name
     * @param value
     */
    void saveSetting(String module, String name, String value);

    /**
     * 保存或修改Setting
     * @param sirmSetting
     */
    void saveSetting(SettingDTO sirmSetting);

}
