package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/3/7
 */
@Data
@Schema(description = "日程删除dto")
public class CalendarEventDeleteDTO {

    @Schema(description = "日程id")
    private Long id;

    @Schema(description = "删除日期")
    private Date deleteDate;

    @Schema(description = "删除方式(重复日程：0-此日程，1-此日程及后续日程，2-所有日程)")
    private Integer deleteType;
}
