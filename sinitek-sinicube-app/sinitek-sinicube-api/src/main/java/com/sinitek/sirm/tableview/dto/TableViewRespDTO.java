package com.sinitek.sirm.tableview.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 表格视图-返回DTO
 *
 * <AUTHOR>
 * date 2023-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "表格视图-返回DTO")
public class TableViewRespDTO extends TableViewBaseDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "条件组列表")
    private List<TableViewConditionGroupRespDTO> conditionGroupList;

    @Schema(description = "字段列表")
    private List<TableViewColumnRespDTO> columnList;

    @Schema(description = "扩展数据Map, 如每个视图能匹配的数量等等")
    private Map<String, Object> extensionDataMap;
}
