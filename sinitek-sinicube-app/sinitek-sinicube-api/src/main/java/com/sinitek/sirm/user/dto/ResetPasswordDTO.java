package com.sinitek.sirm.user.dto;

import com.sinitek.sirm.user.message.UserMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2021/11/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetPasswordDTO {
    @Schema(description = "orgid")
    private String orgid;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "邮件内容")
    private String emailContent;

    @Length(max = 100, message = "{"+ UserMessageCode.EMAIL_LENGTH_TOO_BIG +"}")
    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "抄送人邮箱")
    private CarbonCopyingDTO[] ccEmail;

    @Schema(description = "消息模板")
    private String messageTemplate;
}
