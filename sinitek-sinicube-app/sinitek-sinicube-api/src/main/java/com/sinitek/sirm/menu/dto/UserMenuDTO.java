package com.sinitek.sirm.menu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/6/18
 * 当前用户的菜单DTO
 */
@Data
@Schema(description = "用户菜单信息DTO")
public class UserMenuDTO {

    @Schema(description = "菜单id")
    private Long objid;

    @Schema(description = "Url地址")
    private String url;

    @Schema(description = "父菜单ID")
    private Long parentId;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "顺序")
    private int ord;

    @Schema(description = "系统代码")
    private String sysCode;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "菜单类型")
    private String menuType;

    @Schema(description = "绑定的语言文本项编码")
    private String i18n;
}
