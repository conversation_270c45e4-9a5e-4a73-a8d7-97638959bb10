package com.sinitek.sirm.org.entity;

import com.sinitek.sirm.tenant.support.TenantIdParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 包含 um_userinfo、sprt_orgobject
 *
 * <AUTHOR>
 * @date 2019-12-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户核心信息实体")
public class UserCoreInfo extends TenantIdParam {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "登陆名")
    private String userName;

    @Schema(description = "登陆密码")
    private String password;

    @Schema(description = "盐")
    private String salt;

    @Schema(description = "是否锁定")
    private String lockFlag;

    @Schema(description = "员工名称,对应sprt_orgobject的orgname")
    private String empName;

    @Schema(description = "在职状态")
    private Integer inservice;

    @Schema(description = "用户组织结构ID")
    private String orgId;

    @Schema(description = "到期时间")
    private Date expireTime;
}
