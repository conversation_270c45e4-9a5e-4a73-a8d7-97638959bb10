package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * Date  2022/12/9
 */
@Data
@Schema(description = "日程重复结果dto")
public class CalendarEventRepeatResultDTO {

    @Schema(description = "日程id")
    private Long id;

    @Schema(description = "日程实体dto")
    private CalendarEventDTO calendarEventDTO;

    @Schema(description = "重复日程开始时间结束时间map")
    private Map<Date,Date> dateMap;
}
