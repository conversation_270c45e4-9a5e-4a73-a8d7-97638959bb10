package com.sinitek.sirm.common.action.message;

/**
 * <AUTHOR>
 * @date
 */
public class ActionMessageCode {

    /**
     * 本地接口地址对应的类不存在
     */
    public static final String ACTION_ACHIEVE_FAIL = "12000001";

    /**
     * 动作名称不能为空
     */
    public static final String NAME_CAN_NOT_NULL = "12030001";

    /**
     * 动作名称过长
     */
    public static final String NAME_LENGTH_TOO_BIG = "12030002";

    /**
     * 动作编码不能为空
     */
    public static final String CODE_CAN_NOT_NULL = "12030003";

    /**
     * 动作编码过长
     */
    public static final String CODE_LENGTH_TOO_BIG = "12030004";

    /**
     * 远程动作的url过长
     */
    public static final String URL_LENGTH_TOO_BIG = "12030005";

    /**
     * 找不到编码为[{0}]的动作模板
     */
    public static final String ACTION_MODEL_CAN_NOT_FIND = "12030006";

    /**
     * 绑定的动作名字为空,【name】节点不存在或内容为空
     */
    public static final String TRIGGER_ACTION_NAME_NULL = "12030008";

    /**
     * 绑定的动作code为空,【code】节点不存在或内容为空
     */
    public static final String TRIGGER_ACTION_CODE_NULL = "12030009";

    /**
     * 绑定的动作表单数据为空,【componentData】节点不存在或内容为空
     */
    public static final String TRIGGER_ACTION_COMPONENT_DATA_NULL = "12030010";



    /**
     * 绑定动作时，对象id不能为空
     */
    public static final String BIND_SOURCE_ID_CAN_NOT_NULL = "12040001";


    /**
     * 绑定动作时，对象id过长
     */
    public static final String BIND_SOURCE_ID_LENGTH_TOO_BIG = "12040002";


    /**
     * 绑定动作时，对象名称不能为空
     */
    public static final String BIND_SOURCE_NAME_CAN_NOT_NULL = "12040003";


    /**
     * 绑定动作时，对象名称过长
     */
    public static final String BIND_SOURCE_NAME_LENGTH_TOO_BIG = "12040004";

    /**
     * 绑定动作时，找不到该动作[id={0}]
     */
    public static final String BIND_ACTION_ID_CAN_NOT_FOUND = "12040005";


    /**
     * 绑定动作[{0}]时，关联了Vue组件，但是没有传对应的表单内容
     */
    public static final String BIND_COMPONENT_DATA_IS_NULL = "12040006";


    /**
     * 绑定动作[{0}]时，Vue表单内容转换出现异常
     */
    public static final String BIND_COMPONENT_DATA_TRANSFORM_ERROR = "12040007";

    /**
     * 动作【{0}】已经被绑定，请不要重复绑定
     */
    public static final String BIND_ACTION_IS_REPEAT = "12040008";

    /**
     * 执行成功
     */
    public static final String ACTION_EXCUTE_SUCCESS = "12050001";

    /**
     * 执行失败
     */
    public static final String ACTION_EXCUTE_FALIED = "12050002";

    /**
     * 事件
     */
    public static final String EVENT_EXCUTE_ACTION = "12050003";

    /**
     * 定时任务
     */
    public static final String SCHEDULE_EXCUTE_ACTION = "12050004";

    /**
     * 消息发送动作的发送方式为空，跳过该消息的发送
     */
    public static final String SEND_MESSAGE_ACTION_SEND_TTPE_IS_NULL = "12050005";

    /**
     * 消息发送动作的消息内容为空，跳过该消息的发送
     */
    public static final String SEND_MESSAGE_ACTION_SEND_CONTENT_IS_NULL = "12050006";

    /**
     * 发送消息动作转换初始参数异常
     */
    public static final String SEND_MESSAGE_ACTION_INIT_DATA_IS_ERROR = "12050007";

    /**
     * 收件人为空，跳过该消息的发送
     */
    public static final String SEND_MESSAGE_ACTION_RECEIVER_IS_NULL = "12050008";

    /**
     * 添加日程动作转换初始参数异常
     */
    public static final String ADD_CALENDAR_ACTION_INIT_DATA_IS_ERROR = "12050009";

    /**
     * 发起流程动作转换初始参数异常
     */
    public static final String START_PROCESS_ACTION_INIT_DATA_IS_ERROR = "12050010";

    /**
     * 未通过sysCode获取到对应的流程模板，跳过该动作
     */
    public static final String START_PROCESS_ACTION_SYS_CODE_IS_ERROR = "12050011";

    /**
     * 发起流程动作中不存在关联实体数据模型，跳过该动作
     */
    public static final String START_PROCESS_ACTION_SOURCED_NOT_EXIST = "12050012";


    /**
     * 远程调用动作转换初始参数异常
     */
    public static final String REMOTE_INVOKE_ACTION_INIT_DATA_IS_ERROR = "12050013";

    /**
     * 远程调用动作请求头转换异常，跳过该动作
     */
    public static final String REMOTE_INVOKE_ACTION_HEADER_DATA_ERROR = "12050014";

    /**
     * 远程调用动作请求参数转换异常，跳过该动作
     */
    public static final String REMOTE_INVOKE_ACTION_BODY_DATA_ERROR = "12050015";

    /**
     * 发送消息动作标题最大长度为100
     */
    public static final String SEND_MESSAGE_ACTION_TITLE_LENGTH = "12050016";

    /**
     * 标题不允许为空
     */
    public static final String SEND_MESSAGE_ACTION_TITLE_NOT_NULL = "12050017";

    /**
     * 内容不允许为空
     */
    public static final String SEND_MESSAGE_ACTION_CONTENT_NOT_NULL = "12050018";

    /**
     * 发送消息动作提醒对象不允许为空
     */
    public static final String SEND_MESSAGE_ACTION_REMIND_NOT_NULL = "12050019";

    /**
     * 发送消息动作提醒对象最大长度为100
     */
    public static final String SEND_MESSAGE_ACTION_REMIND_LENGTH = "12050020";

    /**
     * 远程调用动作请求url不允许为空
     */
    public static final String REMOTE_INVOKE_ACTION_URL_NOT_NULL = "12050021";

    /**
     * 远程调用动作请求url最大长度为200
     */
    public static final String REMOTE_INVOKE_ACTION_URL_LENGTH = "12050022";

    /**
     * 远程调用动作请求头最大长度为200
     */
    public static final String REMOTE_INVOKE_ACTION_HEADER_LENGTH = "12050023";

    /**
     * 远程调用动作请求参数最大长度为200
     */
    public static final String REMOTE_INVOKE_ACTION_BODY_LENGTH = "12050024";

    /**
     * 绑定动作失败，表单数据异常
     */
    public static final String BIND_ACTION_DATA_ERROR = "12050025";

    /**
     * 动作执行条件最大长度为100
     */
    public static final String BIND_ACTION_EXECUTE_EXPRESSION_LENGTH = "12050026";

    /**
     * 提醒对象不允许为空
     */
    public static final String SEND_MESSAGE_ACTION_REMIND_TYPE_NOT_NULL = "12050027";

    /**
     * 提醒对象数据不允许为空
     */
    public static final String SEND_MESSAGE_ACTION_REMIND_DATA_NOT_NULL = "12050028";


    /**
     * 动作类型有误
     */
    public static final String ACTION_TYPE_ERROR = "12050029";
}
