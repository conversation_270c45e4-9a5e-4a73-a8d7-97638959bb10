package com.sinitek.sirm.org.support;


import com.google.common.base.Splitter;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.org.dto.EmpCompOrgInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * OrgId 支持层
 *  - 各种场景下获取orgId
 *
 * <AUTHOR>
 * date 2023-08-11
 */
@Component
public class OrgIdSupport {

    private static final Integer ORG_INFO_NUM = 3;

    private static final Integer ORG_ID_NUM = 0;

    private static final Integer ORG_NAME_NUM = 1;

    private static final Integer ORG_TYPE_NUM = 2;

    /**
     * 获取orgId从选人控件的值中获取
     *
     * @param empCompValue orgId:orgName:orgType
     * @return orgId
     */
    public String getOrgIdByEmpCompValue(String empCompValue) {
        if (StringUtils.isBlank(empCompValue)) {
            return "";
        }
        List<String> orgInfoList = Splitter.on(":").splitToList(empCompValue);
        return orgInfoList.get(0);
    }

    /**
     * 将选人控件传递的组织结构字符串 转换为 集合
     * @param empCompValue
     * @return
     */
    public List<EmpCompOrgInfoDTO> findEmpCompOrgInfoList(String empCompValue) {
        if (StringUtils.isBlank(empCompValue)) {
            return new ArrayList<>();
        }
        String[] parts = StringUtils.split(empCompValue, GlobalConstant.COMMA);

        List<EmpCompOrgInfoDTO> resultList = new ArrayList<>();
        for (String part : parts) {
            String[] subParts = StringUtils.split(part, GlobalConstant.COLON);
            if (subParts.length < ORG_INFO_NUM) {
                continue;
            }

            String orgId = subParts[ORG_ID_NUM];
            String orgName = subParts[ORG_NAME_NUM];
            int orgType = Integer.parseInt(subParts[ORG_TYPE_NUM]);

            EmpCompOrgInfoDTO empCompOrgInfo = new EmpCompOrgInfoDTO();
            empCompOrgInfo.setOrgId(orgId);
            empCompOrgInfo.setOrgName(orgName);
            empCompOrgInfo.setOrgType(orgType);
            resultList.add(empCompOrgInfo);
        }
        return resultList;
    }
}
