package com.sinitek.sirm.common.message.template.dto;


import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.common.message.template.enumerate.MessageImportantLevelEnum;
import com.sinitek.sirm.common.message.template.enumerate.SendModeTypeEnum;
import com.sinitek.sirm.common.message.template.support.IMessageTextHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/6/16 11:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageContextDTO {

    @Schema(description = "消息模版代码", required = true)
    private String code;

    /**
     * 发送人
     */
    private String sendId;

    /**
     * 设置的发送模式，优先于消息模板中设置的
     */
    private Integer sendMode = 0;

    private String title;

    /**
     * 邮件内容
     */
    private String emailContent;

    private String content;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 定时消息覆蓋标识
     * 如果为true，则会清空相同sourceId与sourceEntity的未发送(status=1)的定时消息
     */
    private Boolean timingMsgOverwriteFlag = false;

    /**
     * 接收人
     */
    private List<MessageReceiverTemplateDTO> receivers = new ArrayList<>();

    /**
     * 附件
     */
    private List<MessageAttachmentDTO> attachments = new ArrayList<>();

    /**
     * 额外属性配置列表，支持多种发送方式的配置同时存在
     */
    List<MessageSendConfigDTO> sendConfigs = new ArrayList<>();

    /**
     * 占位符参数
     */
    private Map<String,Object> params = new HashMap<>();


    private String mailFrom;

    /**
     * 消息重要度 1:高，3：中(默认)，5：低
     */
    private int importantLevel = MessageImportantLevelEnum.LEVEL_MIDDLE.getEnumItemValue();

    /**
     * 来源记录id
     */
    private Long sourceId = 0L;

    /**
     * 来源记录名称
     */
    private String sourceEntity;

    /**
     * 机构id
     */
    private String tenantId;

    /**
     * 默认异步发送
     */
    private Boolean syncFlag = false;

    /**
     * 消息模板内容
     */
    private List<MessageTemplateContentDTO> contents;

    /**
     * 自定义占位符替换handler
     */
    private IMessageTextHandler messageTextHandler;


    public void addParam(String name, Object value) {
        this.params.put(name, value);
    }

    public Object getParam(String name) {
        return this.params.get(name);
    }

    /**
     *  自定义发送方式
     * @param types 发送方式的集合
     */
    @JsonIgnore
    public void initSendMode(SendModeTypeEnum... types) {
        int sendModeT = 0;
        if(ObjectUtil.isNotEmpty(types)){
            for (SendModeTypeEnum sendModeTypeEnum:types) {
                sendModeT += sendModeTypeEnum.getEnumItemValue();
            }
        }
        this.sendMode = sendModeT;
    }

}
