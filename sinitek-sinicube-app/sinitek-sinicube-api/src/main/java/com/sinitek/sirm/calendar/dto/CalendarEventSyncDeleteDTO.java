package com.sinitek.sirm.calendar.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/12/15
 */
@Data
@Schema(description = "日程同步删除dto")
@NoArgsConstructor
@AllArgsConstructor
public class CalendarEventSyncDeleteDTO {

    @Schema(description = "软件保存日程id")
    private String scheduleId;

    @Schema(description = "重复日程删除：0-此日期日程，1-此日程及后续日程，2-所有日程")
    private Integer deleteType;

    @Schema(description = "重复日程删除日期")
    private Date deleteDate;
}
