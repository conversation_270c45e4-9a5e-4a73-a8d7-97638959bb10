package com.sinitek.sirm.common.message.template.enumerate;

public enum SendTypeEnum {
    /**
     *
     */
    SYSTEM("系统消息", 1),
    /**
     *
     */
    EMAIL("邮件", 4),
    /**
     *
     */
    WXWORK("企业微信", 8);


    private String name;

    private int value;

    SendTypeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

}
