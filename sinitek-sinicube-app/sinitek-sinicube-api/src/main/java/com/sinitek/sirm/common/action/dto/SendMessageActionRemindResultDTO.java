package com.sinitek.sirm.common.action.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "发送消息动作提醒dto")
@NoArgsConstructor
@AllArgsConstructor
public class SendMessageActionRemindResultDTO {

    @Schema(description = "提醒类型名称对应的编码")
    private String code;

    @Schema(description = "提醒类型名称")
    private String name;

    @Schema(description = "提醒类型")
    private Integer sendType;
}
