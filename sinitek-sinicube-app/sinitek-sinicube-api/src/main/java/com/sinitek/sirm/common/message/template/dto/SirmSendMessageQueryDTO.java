package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Schema(description= "系统消息查询DTO")
@Data
public class SirmSendMessageQueryDTO {

    @Schema(description= "消息状态集合")
    private List<Integer> statuses;

    @Schema(description= "消息来源id集合")
    private List<Long> sourceIds;

    @Schema(description= "消息来源实体集合")
    private List<String> sourceEntitys;
}
