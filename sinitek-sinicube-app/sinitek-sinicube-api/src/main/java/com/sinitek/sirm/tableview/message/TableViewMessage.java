package com.sinitek.sirm.tableview.message;

/**
 * 表格视图 MessageCode
 *
 * <AUTHOR>
 * date 2023-07-11
 */
public class TableViewMessage {

    private TableViewMessage() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 视图名称不能为空
     */
    public static final String NAME_CAN_NOT_NULL = "com.sinitek.sirm.tableview.name_can_not_null";
    /**
     * 视图名称不能超过10个字符
     */
    public static final String NAME_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.name_can_not_exceed";
    /**
     * 视图颜色不能超过30个字符
     */
    public static final String COLOR_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.color_can_not_exceed";

    /**
     * 表格唯一标识不能为空
     */
    public static final String TABLE_ID_CAN_NOT_NULL = "com.sinitek.sirm.tableview.table_id_can_not_null";

    /**
     * 表格的唯一标识不能超过100个字符
     */
    public static final String TABLEID_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.tableid_can_not_exceed";
    /**
     * 用户Id不能为空
     */
    public static final String USERID_CAN_NOT_NULL = "com.sinitek.sirm.tableview.userid_can_not_null";
    /**
     * 用户Id不能超过20个字符
     */
    public static final String USERID_CAN_NOT_EXCEED = "com.sinitek.sirm.tableview.userid_can_not_exceed";

    /**
     * 视图名称已存在
     */
    public static final String NAME_ALREADY_EXIST = "com.sinitek.sirm.tableview.name_already_exist";

    /**
     * 最多展示n个视图
     *  - 有一个内置的全部视图，所以实际数据库为n - 1即达到了n个视图的限制
     */
    public static final String DISPLAY_MAXIMUM_VIEWS = "com.sinitek.sirm.tableview.display_maximum_views";

}
