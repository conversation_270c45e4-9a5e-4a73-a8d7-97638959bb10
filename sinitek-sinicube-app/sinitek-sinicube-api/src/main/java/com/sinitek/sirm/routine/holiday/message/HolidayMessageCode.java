package com.sinitek.sirm.routine.holiday.message;

/**
 * 节假日操作系列的Message Code 常量
 *
 * <AUTHOR>
 * @date 2020-08-21
 */
public class HolidayMessageCode {

    /**
     * 选中年份为空
     */
    public final static String SELECT_YEARS_IS_NULL = "0110001";

    /**
     * 年份错误
     */
    public final static String YEARS_ERROR = "0110002";

    /**
     * 保存的节假日所属年输入不对
     */
    public final static String SAVE_YEARS_ERROR = "0110003";

    /**
     * 保存节假日出错
     */
    public final static String SAVE_HOLIDAY_ERROR = "0110004";

    /**
     * 我的日程提醒成功
     */
    public final static String MY_SCHEDULE_REMINDER_SUCCEEDED = "0110005";

    /**
     *  节假日导入文件为空
     */
    public final static String HOLIDAY_IMPORT_IS_NULL = "0110006";

    /**
     *  读取的结果为空，请检查文件的类型或数据
     */
    public final static String READ_DATA_IS_NULL = "0110007";

    /**
     *  第[{0}]行日期错误
     */
    public final static String DATE_ERROR = "0110008";

    /**
     *  文件导入错误，请选择正确的模板或参照正确的模板进行修改
     */
    public final static String TEMPLATE_ERROR = "0110009";

    /**
     * 节假日导入模板.xls
     */
    public final static String TEMPLATE_NAME = "0110010";

    /**
     *  文件读取失败
     */
    public final static String READ_FILE_ERROR = "0110011";

    /**
     *  方案名称不能超过30个字符
     */
    public final static String SCHEME_NAME_LENGTH_TOO_BIG = "0110012";

    /**
     *  方案编码不能超过50个字符
     */
    public final static String SCHEME_CODE_LENGTH_TOO_BIG = "0110013";

    /**
     *  系统默认方案不能删除
     */
    public static final String DEFAULT_SCHEME_CANNOT_DELETE = "0110014";

    /**
     *  方案编码重复
     */
    public static final String SCHEME_CODE_REPEAT = "0110015";

    /**
     *  方案名称重复
     */
    public static final String SCHEME_NAME_REPEAT = "0110016";

    /**
     *  方案编码不能为空
     */
    public static final String SCHEME_CODE_NULL = "0110017";

    /**
     *  系统中不存在编码[{0}]对应节假日方案
     */
    public static final String NO_EXIST_SCHEME_CODE = "0110018";

    /**
     *  方案名称不能为空
     */
    public static final String SCHEME_NAME_NULL = "0110019";
}
