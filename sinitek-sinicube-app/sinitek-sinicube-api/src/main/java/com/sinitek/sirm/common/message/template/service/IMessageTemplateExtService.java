package com.sinitek.sirm.common.message.template.service;

import com.sinitek.sirm.common.message.template.dto.*;

import java.util.List;

/**
 * 消息模板统一接口
 *
 * <AUTHOR>
 * @date 2021/6/17
 */
public interface IMessageTemplateExtService {


    /**
     * 根据消息模板发送消息
     * @param context 消息上下文
     * 消息上下文中：
     * １．查询配置的发送人员
     * ２．如果调用的模板是订阅模板，就查询传过来的人员是否是订阅人员如果不是则剔除
     * ３．去除重复人员
     * 若消息发送成功，则会显示出“消息发送成功”
     * 若消息发送失败，则会显示出“消息发送失败”
     */
    List<MessageSendFailedReceiverDTO> sendMessage(MessageContextDTO context);



    /**
     * 获取所有消息模板
     * @return
     * 返回消息模板代码和消息模板名称　
     */
    List<MessageTemplateDTO> findAllMessageTemplate();


    /**
     * 根据流程类型获取消息模板
     * @param processType　流程类型
     * @return
     * 返回消息模板代码和消息模板名称　
     */
    List<MessageTemplateDTO> findMessageTemplateByProcessType(Long processType);


    /**
     * 根据code获取消息模板信息
     * @param code　消息模板code
     * @return
     * 返回消息模板数据　（objid、code、name、sendMode）
     */
    MessageTemplateDTO getMessageTemplateByCode(String code);


    /**
     * 根据code获取消息模板详细信息
     * @param code　消息模板code
     * @return
     * 返回消息模板数据　
     */
    MessageTemplateDetailDTO getMessageTemplateDetailByCode(String code);


    /**
     * 获取消息模板接收人
     * @param templateId
     * @return
     */
    List<MessageReceiverTemplateDTO> findMessageReceiverByTemplateId(Long templateId);


    /**
     * 获取消息模板接收人
     *
     * @param queryDTO
     * @return
     */
    List<MessageReceiverTemplateDTO> findMessageReceiversByQueryDTO(MessageSendReceiverQueryDTO queryDTO);

    /**
     * 获取系统消息收件人详情
     * @param sourceId 来源记录id
     * @param sourceName 来源记录实体
     * @return 收件人详情
     */
    List<ReceiveMessageDTO> findSysReceiveMessagesBySourceIdAndSourceName(Long sourceId, String sourceName);

    /**
     * 系统消息重发
     * @param dto 参数dto
     *           sendMessageId          系统消息id 必填
     *           sendMessageDetailIds   系统消息详情ids 非必填  如果该参数不为空，则只重发指定ids的消息记录
     *           allSendFlag           所有的收件人是否都重新发送；true：都发送；false：只发送状态为失败的；如果指定sendMessageDetailIds则该参数失效
     *           sourceId            数据来源id 非必填 如果没有指定和之前消息记录保持一致
     *           sourceEntity        数据来源实体 非必填 如果没有指定和之前消息记录保持一致
     */
    void resendSirmSendMessage(SirmSendMessageResendDTO dto);

    /**
     *  查询系统消息记录
     * @param sirmSendMessageQueryDTO 查询参数
     * @return 系统消息详情
     */
    List<ExtSirmSendMessageDTO> findSirmSendMessage(SirmSendMessageQueryDTO sirmSendMessageQueryDTO);



    /**
     * 根据关联对象查询消息
     *
     * @param sourceid          关联的实体id
     * @param sourceentity      关联的实体名
     * @param type              提醒方式
     * @return 消息记录
     */
    public List<SirmSendMessageDTO> findSirmSendMessage(Long sourceid, String sourceentity, int type);


    /**
     * 保存发送消息信息
     * @param sirmSendMessage 消息记录
     */
    public void saveSirmSendMessage(SirmSendMessageDTO sirmSendMessage);

}
