package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * User: wyzhang
 * Date: 2018/4/27.
 */
@Data
@Schema(description = "行政上级树")
public class OrgCascaderDTO {

    @Schema(description = "值")
    private String value = null;

    @Schema(description = "节点名称")
    private String label = null;

    @Schema(description = "禁用状态")
    private boolean disabled = false;

    @Schema(description = "子节点")
    private List<OrgCascaderDTO> children = null;

}
