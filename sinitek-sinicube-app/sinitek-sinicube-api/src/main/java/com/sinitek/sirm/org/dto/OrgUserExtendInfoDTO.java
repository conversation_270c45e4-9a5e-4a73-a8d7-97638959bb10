package com.sinitek.sirm.org.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * OrgUserExtendInfo实体对应的 DTO
 *
 * <AUTHOR>
 * @date 2021-01-29
 */
@Data
public class OrgUserExtendInfoDTO {

    @Schema(description = "主键")
    private Long objId;

    @Schema(description = "实体类")
    private String entityName;

    @Schema(description = "创建时间")
    private Date createTimeStamp;

    @Schema(description = "修改时间")
    private Date updateTimeStamp;

    @Schema(description = "乐观锁")
    private Integer version;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "办公电话")
    private String tel;

    @Schema(description = "办公电话2")
    private String tel2;

    @Schema(description = "职位")
    private String job;

    @Schema(description = "手机")
    private String mobilePhone;

    @Schema(description = "用户编号")
    private String userid;

    @Schema(description = "手机2")
    private String mobilephone2;

    @Schema(description = "家庭电话1")
    private String familytelephone;

    @Schema(description = "家庭电话2")
    private String familytelephone2;

    @Schema(description = "其他电话1")
    private String otherphone;

    @Schema(description = "其他电话2")
    private String otherphone2;

    @Schema(description = "BP机")
    private String bp;

    @Schema(description = "办公室")
    private String office;

    @Schema(description = "公司传真1")
    private String fax;

    @Schema(description = "公司传真2")
    private String fax2;

    @Schema(description = "家庭传真")
    private String familyfax;

    @Schema(description = "家庭传真2")
    private String familyfax2;

    @Schema(description = "公司地址")
    private String companyaddress;

    @Schema(description = "公司邮编")
    private String companyzip;

    @Schema(description = "家庭地址")
    private String familyaddress;

    @Schema(description = "家庭邮编")
    private String familyzip;

    @Schema(description = "其他地址")
    private String otheraddress;

    @Schema(description = "其他邮编")
    private String otherzip;

    @Schema(description = "其他Email1")
    private String email1;

    @Schema(description = "其他Email2")
    private String email2;

    @Schema(description = "主页")
    private String homepage;

    @Schema(description = "工作地")
    private Integer where1;

    @Schema(description = "QQ")
    private String qq;

    @Schema(description = "MSN")
    private String msn;

    @Schema(description = "通讯录")
    private String addressbook;

    @Schema(description = "个人简介")
    private String introduction;

    @Schema(description = "密码修改时间")
    private String passwordUpdateTime;

    @Schema(description = "用户锁定时间")
    private String userLockTime;

    @Schema(description = "岗位编号")
    private String postid;

    @Schema(description = "从业资格编号")
    private String qualifyno;

    @Schema(description = "从业资格类型")
    private Integer qualifytype;

    @Schema(description = "员工姓名拼音")
    private String namePy;

    @Schema(description = "员工姓名简拼，首字母拼音")
    private String simpleNamePy;

    @Schema(description = "离职日期")
    private String lzrq;

    @Schema(description = "入职日期")
    private String rzrq;

    @Schema(description = "个人英文简介")
    private String englishIntroduction;

    @Schema(description = "员工英文名称")
    private String englishName;

    @Schema(description = "所属机构Id")
    private String tenantId;
}
