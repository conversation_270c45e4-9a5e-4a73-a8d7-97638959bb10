package com.sinitek.sirm.common.event.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * Date  2022/6/25
 */
@Data
@Schema(description = "事件发布记录dto")
public class SiniCubeEventPublishLogDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "事件对象")
    private String name;

    @Schema(description = "触发源数据")
    private String sourceData;

    @Schema(description = "事件模式")
    private Integer type;

    @Schema(description = "触发结果")
    private Integer result;

    @Schema(description = "触发开始时间")
    private Date startTime;

    @Schema(description = "触发结束时间")
    private Date endTime;
}
