package com.sinitek.sirm.calendar.service;


import com.sinitek.sirm.calendar.dto.*;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.support.CommonMessageCode;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/10/27
 */
public interface ICalendarEventService {

    /**
     * 动作保存、更新日程
     * @param calendarEventDTO
     */
    default void saveOrUpdateCalendarEvent(CalendarEventDTO calendarEventDTO){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * (个人日历展示日程)获取某日的所有日程信息
     * @param calendarDate
     * @return　
     * 若存在对应记录，则返回对应的结果列表
     */
    default List<CalendarEventResultDTO> findPersonalCalendarEvent(Date calendarDate, String empId){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * (个人日历展示日程)获取某月所有日程对应的日期
     * @param calendarEventLoadDTO
     * @return
     * 若存在对应记录，则返回对应的结果列表
     */
    default Set<Date> findDaysOfPersonalCalendarEvent(CalendarEventLoadDTO calendarEventLoadDTO, String empId){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * 根据sourceId、sourceName、empId删除对应日程信息
     * @param sourceId
     * @param sourceName
     */
    default void deleteCalendarEvent(Long sourceId, String sourceName, String empId){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * 根据条件获取日程信息（所有业务）
     * @param calendarEventQueryDTO 查询参数对象
     * @return
     * 若存在对应记录，则返回对应的结果列表
     */
    default List<CalendarEventDTO> findCalendarEvent(CalendarEventQueryDTO calendarEventQueryDTO){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * 根据员工id获取指定时间范围内的所有日程信息，并按照日程类型进行分类
     * @param dto
     * @param empId
     * @return
     */
    List<CalendarEventResultDTO> findAllCalendarEvent(CalendarEventDetailDTO dto, String empId);

    /**
     * 获取日程类型数据
     * @return
     */
    default List<CalendarEventDefinitionDTO> findCalendarEventType(){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }

    /**
     * 根据日程id获取日程信息
     * @param dto
     * @return
     */
    BaseCalendarEventDTO getCalendarEventById(RepeatCalendarEventLoadDTO dto);


    /**
     *  日程管理保存日程、更新日程接口
     * @param calendarEventDataDTO
     * @param empId
     */
    void saveOrUpdateCalendarEvent(CalendarEventDataDTO calendarEventDataDTO, String empId);

    /**
     *  日程管理删除日程接口
     * @param dto
     */
    void deleteCalendarEvent(CalendarEventDeleteDTO dto);

    /**
     * 获取日程同步方式
     * @return
     */
    default List<CalendarEventSyncDTO> findCalendarEventSyncType(){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }


    /**
     * 通过员工id集合获取对应的日程集合
     * @param dto
     * @return
     */
    default List<CalendarEventResultDTO> findCalendarEventByEmpIds(CalendarEventTeamDetailDTO dto){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }



}
