package com.sinitek.sirm.tableview.service;

import com.sinitek.sirm.tableview.dto.TableViewConditionGroupRespDTO;
import com.sinitek.sirm.tableview.dto.TableViewConditionGroupSaveDTO;

import java.util.List;
/**
 * 表格视图条件组 Service 接口
 *
 * <AUTHOR>
 * date 2023-07-11
 */
public interface ITableViewConditionGroupService {

    /**
     * 根据viewId获取条件组列表
     * @param viewId
     * @return
     */
    List<TableViewConditionGroupRespDTO> findTableViewConditionGroupList(Long viewId);

    /**
     * 批量新增列表
     * @param saveList
     */
    void saveBatchList(List<TableViewConditionGroupSaveDTO> saveList, Long viewId);

    /**
     * 删除绑定viewId的条件组列表
     * @param viewId
     */
    void deleteTableViewConditionGroupListByViewId(Long viewId);

}
