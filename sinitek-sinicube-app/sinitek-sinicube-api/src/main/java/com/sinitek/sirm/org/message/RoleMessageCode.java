package com.sinitek.sirm.org.message;

/**
 * <AUTHOR>
 * @date 2020/11/26
 */
public class RoleMessageCode {

    /**
     * 角色名称不能为空
     */
    public final static String ROLE_NAME_IS_BLANK = "0111001";

    /**
     * 角色名称长度不能超过30
     */
    public final static String ROLE_NAME_LENGTH_TOO_BIG = "0111002";

    /**
     * 角色描述长度不能超过100
     */
    public final static String ROLE_DESC_LENGTH_TOO_BIG = "0111003";

    /**
     * 管理员角色不能删除
     */
    public final static String ADMIN_CAN_NOT_DELETE = "0111004";

    /**
     * 【{0}】角色下还有人员不能删除
     */
    public final static String HAVE_EMPLOYEE_NOT_DELETE = "0111005";

    /**
     * 管理员角色不能修改
     */
    public final static String ADMIN_CAN_NOT_UPDATE = "0111006";

}
