package com.sinitek.sirm.business.log.dto;

import com.sinitek.cloud.sirmapp.base.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 业务日志 DTO
 *
 * <AUTHOR>
 * @date 2021-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "业务日志DTO")
public class BusinessLogDTO extends BaseDTO {

    @Schema(description = "用户编号")
    private String userId;

    @Schema(description= "操作类型")
    private Integer operateType;

    @Schema(description= "操作描述")
    private String description;

    @Schema(description= "操作描述")
    private String bigDescription;

    @Schema(description= "操作开始时间")
    private Date startTime;

    @Schema(description= "操作结束时间")
    private Date endTime;

    @Schema(description= "操作URL")
    private String url;

    @Schema(description= "菜单ID")
    private Long menuId;

    @Schema(description= "用户物理地址")
    private String physicalAddress;

    @Schema(description= "用户IP地址")
    private String ipAddress;

    @Schema(description= "模块名")
    private String moduleName;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "服务端Ip")
    private String serverIp;

    @Schema(description = "用户终端类型")
    private String terminal;

    @Schema(description = "客户端操作系统")
    private String operatingSystem;

    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "操作类型的时机，通过逗号分割")
    private String timing;
}
