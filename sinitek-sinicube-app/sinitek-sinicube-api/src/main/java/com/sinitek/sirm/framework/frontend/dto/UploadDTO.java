package com.sinitek.sirm.framework.frontend.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Schema(description = "附件数据传输对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadDTO {

    @NotBlank(message = "{"+ CommonMessageCode.ATTACHMENT_SOURCE_ENTITY_CAN_NOT_NULL +"}")
    @Schema(description = "来源实体")
    private String sourceEntity;

    @NotNull(message = "{"+ CommonMessageCode.ATTACHMENT_SOURCE_ID_CAN_NOT_NULL +"}")
    @Schema(description = "来源id")
    private Long sourceId;

    @Schema(description = "文件存储类型")
    private Integer type;

    @Schema(description = "上传模式")
    private String mode;

    @Schema(description = "附件文件列表")
    private List<UploadFileDTO> uploadFileList = new ArrayList<>();

    @Schema(description = "删除附件文件列表")
    private List<UploadFileDTO> removeFileList = new ArrayList<>();

    @Schema(description = "是否强制生成MD5")
    private boolean forcedGenerationMd5 = false;

    @Schema(description ="提供给外部扩展参数")
    private Map<String, Object> extendMap;

    public Map<String, Object> getExtendMap() {
        return extendMap;
    }

    public void setExtendMap(Map<String, Object> extendMap) {
        this.extendMap = extendMap;
    }


    public List<UploadFileDTO> getUploadFileList() {
        return uploadFileList;
    }

    public void setUploadFileList(List<UploadFileDTO> uploadFileList) {
        this.uploadFileList = uploadFileList;
    }

    public List<UploadFileDTO> getRemoveFileList() {
        return removeFileList;
    }

    public void setRemoveFileList(List<UploadFileDTO> removeFileList) {
        this.removeFileList = removeFileList;
    }

    public String getSourceEntity() {
        return sourceEntity;
    }

    public void setSourceEntity(String sourceEntity) {
        this.sourceEntity = sourceEntity;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public boolean isForcedGenerationMd5() {
        return forcedGenerationMd5;
    }

    public void setForcedGenerationMd5(boolean forcedGenerationMd5) {
        this.forcedGenerationMd5 = forcedGenerationMd5;
    }

    @Override
    public String toString() {
        return "UploadDTO{" +
                "sourceEntity='" + sourceEntity + '\'' +
                ", sourceId=" + sourceId +
                ", type=" + type +
                ", mode='" + mode + '\'' +
                ", uploadFileList=" + uploadFileList +
                '}';
    }
}
