package com.sinitek.sirm.framework.dto;

import com.sinitek.sirm.framework.message.UploadMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.beans.Transient;

/**
 * <AUTHOR>
 * @date
 */
@Data
@Schema(description = "分片上传参数模型")
public class ChunkUploadDTO {

    /**
     * 当前文件块，从1开始
     */
    private Integer chunkNumber;
    /**
     * 总大小
     */
    private Long totalSize;
    /**
     * 文件标识
     */
    @NotBlank(message = "{" + UploadMessageCode.FILE_IDENTIFIER_CAN_NOT_BLANK + "}")
    private String identifier;
    /**
     * 文件名
     */
    private String filename;
    /**
     * 总块数
     */
    private Integer totalChunks;

    /**
     * 块内容
     */
    private MultipartFile file;

    /**
     * 文件块的md5
     */
    private String chunkMd5;

    /**
     * 组件限制的上传文件大小
     */
    private String size;

    /**
     * 组件限制的上传文件类型
     */
    private String suffixes;

    /**
     * 扩展上传Id
     *  - 用于部分文件上传场景，需要后端来生成一次文件的唯一标识。
     */
    private String extUploadId;

    /**
     * @Transient 表示该方法不参与 序列化
     * @return 文件
     */
    @Transient
    public MultipartFile getFile() {
        return file;
    }
}
