package com.sinitek.sirm.routine.holiday.service;

import com.sinitek.sirm.routine.holiday.dto.HolidayDTO;
import com.sinitek.sirm.routine.holiday.dto.HolidaysConfigDTO;
import com.sinitek.sirm.routine.holiday.dto.ImportHolidayDTO;
import com.sinitek.sirm.routine.holiday.dto.ImportHolidayResultDTO;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: 潘虹
 * Date: 12-5-2
 * Time: 下午7:15
 */
public interface IHolidaysService {

    /**
     * 查询给定年份的所有的节假日
     *
     * @param year  年份
     * @return 若存在，返回节假日的列表
     * 若不存在，不返回null
     */
     List<HolidayDTO> findAllHolidays(int year);


    /**
     * 根据节假日的objid查询节假日的详细信息
     *
     * @param objid  对象id
     * @return
     * 返回该节假日的详细信息
     */
    HolidayDTO getHolidays(Long objid);

    /**
     * 根据输入的年份，删除该年份的节假日
     *
     * @param year 年份
     * 返回删除节假日后的结果
     */
    void delHolidays(int year);

    /**
     * 验证该天是否是节假日
     *
     * @param date 日期
     * @return
     * 若该天是节假日，则返回true
     * 若改天不是节假日，则返回false
     */
    boolean checkHolidays(Date date);

    /**
     * 根据输入的开始时间和结束时间，输出该时间段内的节假日
     *
     * @param startdate 开始日期(yyyy-MM-dd)
     * @param enddate   结束日期(yyyy-MM-dd)
     * @return 若存在节假日，则返回该节假日的列表
     * 若不存在，不返回null
     */
    List<HolidayDTO> findHolidays(Date startdate, Date enddate);


    /**
     * 根据输入的开始时间和结束时间，输出该时间段节假日的日期
     * @param startdate 开始日期(yyyy-MM-dd)
     * @param enddate   结束日期(yyyy-MM-dd)
     * @return
     * 若存在，则输出相关的结果列表
     * 若不存在，返回null
     */
    List<Date> findHolidaysByDate(Date startdate, Date enddate);


    /**
     *  点击保存节假日
     * @param holidaysConfigDTO  节假日保存模型
     * @param inputId 录入人id
     */
    void saveHoliday(HolidaysConfigDTO holidaysConfigDTO, String inputId);

    /**
     *   导入模板后保存节假日
     * @param importHolidayDTO  节假日导入信息
     * @param inputId 录入人id
     * @return   返回给前端的结果类
     */
    ImportHolidayResultDTO saveImportHoliday(ImportHolidayDTO importHolidayDTO, String inputId);

    /**
     *  查询给定年份和该年份之后的所有节假日
     * @param year 年份
     * @return 查询节假日信息结果
     */
    List<HolidayDTO> findHolidaysByYearGreaterThanEqual(int year);

    /**
     * 根据方案Code查询给定年份的所有的节假日
     *
     * @param year  年份
     * @param schemeCode 节假日方案Code
     * @return 若存在，返回节假日的列表
     * 若不存在，不返回null
     */
    List<HolidayDTO> findAllHolidaysBySchemeCode(int year, String schemeCode);

    /**
     * 根据输入的年份和方案Code，删除该年份的节假日
     *
     * @param year 年份
     * @param schemeCode 节假日方案Code
     * 返回删除节假日后的结果
     */
    void delHolidaysBySchemeCode(int year, String schemeCode);

    /**
     * 根据方案Code，验证该天是否是节假日
     *
     * @param date 日期
     * @param schemeCode 节假日方案Code
     * @return
     * 若该天是节假日，则返回true
     * 若改天不是节假日，则返回false
     */
    boolean checkHolidaysBySchemeCode(Date date, String schemeCode);

    /**
     * 根据方案Code，输入的开始时间和结束时间，输出该时间段内的节假日
     *
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate   结束日期(yyyy-MM-dd)
     * @param schemeCode  给定方案Code
     * @return 若存在节假日，则返回该节假日的列表
     * 若不存在，不返回null
     */
    List<HolidayDTO> findHolidaysBySchemeCode(Date startDate, Date endDate, String schemeCode);

    /**
     * 根据方案Code，根据输入的开始时间和结束时间，输出该时间段节假日的日期
     * @param startDate 开始日期(yyyy-MM-dd)
     * @param endDate   结束日期(yyyy-MM-dd)
     * @param schemeCode   给定方案Code
     * @return
     * 若存在，则输出相关的结果列表
     * 若不存在，返回null
     */
    List<Date> findHolidaysByDateAndSchemeCode(Date startDate, Date endDate, String schemeCode);

    /**
     * 点击保存节假日
     * @param holidaysConfigDTO  节假日保存模型
     * @param schemeCode  选定节假日方案Code
     * @param inputId 录入人id
     */
    void saveHolidayBySchemeCode(HolidaysConfigDTO holidaysConfigDTO, String schemeCode, String inputId);

    /**
     * 根据方案Code，查询给定年份和该年份之后的所有节假日
     * @param year 给定年份
     * @param schemeCode 给定节假日方案Code
     * @return 查询出的节假日信息
     */
    List<HolidayDTO> findHolidaysByYearGreaterThanEqual(int year, String schemeCode);

    /**
     *   导入模板后保存节假日, 到选定的方案下
     * @param importHolidayDTO  节假日导入信息
     * @param inputId 录入人id
     * @return   返回给前端的结果类
     */
    ImportHolidayResultDTO saveImportHolidayToScheme(ImportHolidayDTO importHolidayDTO, String inputId);


    /**
     * 通用节假日保存
     * @param dateList 日期集合(格式yyyy-MM-dd)
     * @param inputId 录入人id
     */
    void saveHoliday(List<Date> dateList, String inputId);

    /**
     * 通用节假日保存到指定方案
     * @param dateList 日期集合(格式yyyy-MM-dd)
     * @param inputId 录入人id
     * @param schemeCode 方案编码
     */
    void saveHoliday(List<Date> dateList, String inputId, String schemeCode);


}
