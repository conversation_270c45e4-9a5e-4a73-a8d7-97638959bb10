package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.common.action.message.ActionMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 绑定动作的参数对象
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
@Schema(description = "绑定动作的参数对象")
@Data
public class BindActionDTO<T> {

    @Schema(description = "绑定动作的关系id")
    private Long id;

    @Schema(description = "动作id")
    private Long actionId;

    @Schema(description = "动作表单数据")
    private T componentData;

    @Schema(description = "绑定对象id")
    @NotBlank(message = "{"+ ActionMessageCode.BIND_SOURCE_ID_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ ActionMessageCode.BIND_SOURCE_ID_LENGTH_TOO_BIG +"}")
    private String sourceId;

    @Schema(description = "绑定对象名")
    @NotBlank(message = "{"+ ActionMessageCode.BIND_SOURCE_NAME_CAN_NOT_NULL +"}")
    @Length(max = 100, message = "{"+ ActionMessageCode.BIND_SOURCE_NAME_LENGTH_TOO_BIG +"}")
    private String sourceName;

    @Schema(description = "执行表达式")
    @Length(max = 100, message = "{"+ ActionMessageCode.BIND_ACTION_EXECUTE_EXPRESSION_LENGTH +"}")
    private String executeExpression;



}
