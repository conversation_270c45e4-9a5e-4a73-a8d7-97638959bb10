package com.sinitek.sirm.calendar.dto;

import com.sinitek.sirm.calendar.message.CalendarEventMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/27
 */
@Schema(description = "日程保存模型")
@Data
public class CalendarEventDTO {

    @Schema(description = "日程id")
    private Long id;

    @NotBlank(message = "{" + CalendarEventMessageCode.CALENDAR_EVENT_TYPE_NULL + "}")
    @Schema(description = "日程类型")
    private String type;

    @Schema(description = "日历类型")
    private String calendarType = null;

    @NotNull(message = "{" + CalendarEventMessageCode.CALENDAR_EVENT_START_NULL + "}")
    @Schema(description = "日程开始时间")
    private Date startTime;

    @NotNull(message = "{" + CalendarEventMessageCode.CALENDAR_EVENT_END_NULL + "}")
    @Schema(description = "日程结束时间")
    private Date endTime;

    @Schema(description = "录入人ID")
    private String inputId;

    @NotBlank(message = "{" + CalendarEventMessageCode.CALENDAR_EVENT_TITLE_NULL + "}")
    @Length(max = 100, message = "{" + CalendarEventMessageCode.CALENDAR_EVENT_TITLE_LENGTH + "}")
    @Schema(description = "日程主题")
    private String title;

    @Schema(description = "日程来源对象")
    private String sourceName = null;

    @Schema(description = "日程来源ID")
    private Long sourceId = 0L;

    @Schema(description = "人员ID")
    private String empId;

    @Length(max = 500, message = "{" + CalendarEventMessageCode.CALENDAR_EVENT_CONTENT_LENGTH + "}")
    @Schema(description = "内容")
    private String content;

    @Schema(description = "url")
    private String url;

    @Schema(description = "日程所有者ID")
    private Long ownerId = 0L;

    @Schema(description = "日程所有者名称")
    private String ownerName = null;

    @Schema(description = "是否提醒")
    private Integer remindFlag = 0;

    @Schema(description = "提醒时间")
    private Integer[] remindTime;

    @Schema(description = "提醒方式")
    private Integer remindType;

    @Schema(description = "是否勾选全天")
    private Integer allDay;

    @Schema(description = "是否自定义提醒")
    private Integer customRemind = 0;

    @Schema(description = "是否重复")
    private Integer repeatFlag = 0;

    /**
     * 优先级比通过 {@link CalendarEventDTO#remindTime} 计算出的时间要高
     */
    @Schema(name = "发送时间", description = "优先级比通过 CalendarEventDTO#remindTime 计算出的时间要高")
    private List<Date> sendTimes;

}
