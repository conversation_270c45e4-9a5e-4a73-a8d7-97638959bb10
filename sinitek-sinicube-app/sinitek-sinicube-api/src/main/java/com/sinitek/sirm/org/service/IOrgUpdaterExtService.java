package com.sinitek.sirm.org.service;

import com.sinitek.sirm.org.entity.Department;
import com.sinitek.sirm.org.entity.Position;
import com.sinitek.sirm.org.entity.Role;
import com.sinitek.sirm.org.entity.Team;
import com.sinitek.spirit.org.core.dto.UnitDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
public interface IOrgUpdaterExtService {


    /**
     * 重置组织结构下的员工
     *
     * @param parentId      组织结构id
     * @param empIdList     新员工id集合
     * @return  返回找不到的员工id
     */
    List<String> resetOrgEmployee(String parentId, List<String> empIdList);

    /**
     * 修改节点名称
     *
     * @param parentId      父类部门id， 可以为空(默认是root部门)
     * @param dto           unit节点
     */
    void renameUnit(String parentId, UnitDTO dto);

    /**
     * 通用删除组织结构节点（包括下级，但不包括用户）、关联关系、相关权限
     * @param ids       组织结构id集合
     */
    void deleteOrgsByIds(String... ids);

    /**
     * 删除部门（包括下级，但不包括用户）、关联关系、相关权限
     * @param ids
     */
    void deleteUnitsByIds(String... ids);

    /**
     * 删除角色（包括下级，但不包括用户）、关联关系、相关权限
     * @param ids
     */
    void deleteRolesByIds(String... ids);


    /**
     * 删除岗位（包括下级，但不包括用户）、关联关系、相关权限
     * @param ids
     */
    void deletePositionsByIds(String... ids);

    /**
     * 删除小组（包括下级，但不包括用户）、关联关系、相关权限
     * @param ids
     */
    void deleteTeamsByIds(String... ids);

    /**
     * 组织结构下添加员工
     *
     * @param parentId      组织结构id
     * @param empIdList     员工id集合
     * @return  返回找不到的员工id
     */
    List<String> addOrgEmployee(String parentId, List<String> empIdList);

    /**
     * 岗位下添加员工
     * @param positionId
     * @param empIdList
     * @return
     */
    List<String> addPositionEmployee(String positionId, List<String> empIdList);

    /**
     * 小组下添加员工
     * @param teamId
     * @param empIdList
     * @return
     */
    List<String> addTeamEmployee(String teamId, List<String> empIdList);

    /**
     * 角色下添加员工
     * @param roleId
     * @param empIdList
     * @return
     */
    List<String> addRoleEmployee(String roleId, List<String> empIdList);

    /**
     * 新增/编辑 部门
     * @param department
     * @return
     */
    Department saveDepartment(Department department);

    /**
     * 新增/编辑岗位
     * @param position
     * @return
     */
    Position savePosition(Position position);

    /**
     * 新增/编辑 小组
     * @param team
     * @return 返回小组自身
     */
    Team saveTeam(Team team);

    /**
     * 新增/编辑角色
     * @param role
     * @return
     */
    Role saveRole(Role role);
}
