package com.sinitek.sirm.tableview.service;

import com.sinitek.sirm.tableview.dto.TableViewConditionRespDTO;
import com.sinitek.sirm.tableview.dto.TableViewConditionSaveDTO;

import java.util.List;
/**
 * 表格视图条件 Service 接口
 *
 * <AUTHOR>
 * date 2023-07-11
 */
public interface ITableViewConditionService {

    /**
     * 根据分组id列表获取条件列表
     * @param groupIdList
     * @return
     */
    List<TableViewConditionRespDTO> findTableViewConditionList(List<Long> groupIdList);

    /**
     * 批量新增列表
     * @param saveList
     */
    void saveBatchList(List<TableViewConditionSaveDTO> saveList);

    /**
     * 根据groupIdList删除条件列表
     */
    void deleteTableViewConditionListByGroupIdList(List<Long> groupIdList);
}
