package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Schema(description = "对外系统消息详情DTO")
@Data
public class ExtSirmSendMessageDetailDTO {

    @Schema(description= "发送消息Id")
    private Long sendMessageId;

    @Schema(description=  "消息地址")
    private String address;

    @Schema(description=  "消息发送状态")
    private Integer status;

    @Schema(description=  "发送完成时间")
    private Date sendTime;

    @Schema(description=  "收件人编号")
    private String empid;

    @Schema(description=  "收件人类型")
    private String receiverType;

    @Schema(description=  "收件人名称")
    private String empName;

    @Schema(description=  "失败原因")
    private String reason;
}
