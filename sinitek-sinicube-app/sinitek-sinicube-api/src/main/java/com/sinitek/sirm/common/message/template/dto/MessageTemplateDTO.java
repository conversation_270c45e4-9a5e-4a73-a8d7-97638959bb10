package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Schema(description = "消息模板数据对象")
@Getter
@Setter
public class MessageTemplateDTO {

    @Schema(description = "主键")
    private Long objId;


    @Schema(description = "消息模板code")
    private String code = "";


    @Schema(description = "消息模板名称")
    private String name = "";


    @Schema(description = "发送方式")
    private Integer sendMode;

    @Schema(description= "消息标题")
    private String title;


    @Schema(description= "配置类型")
    private Integer forceFlag;

    @Schema(description= "模板分类")
    private Integer catagory;

    @Schema(description= "消息模板说明")
    private String remark;


    @Schema(description= "消息重要度")
    private Integer importantLevel;


    @Schema(description = "提醒对象")
    private List<MessageTemplateReceiverDTO> messageSendRemindDTOList;


    @Schema(description = "抄送对象")
    private List<MessageTemplateReceiverDTO> messageSendCopyRemindDTOList;


    @Schema(description = "消息模版内容")
    private List<MessageTemplateContentDTO> contents;

}
