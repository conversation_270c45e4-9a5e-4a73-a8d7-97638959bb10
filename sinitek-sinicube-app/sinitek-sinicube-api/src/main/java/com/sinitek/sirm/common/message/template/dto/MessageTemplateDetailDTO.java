package com.sinitek.sirm.common.message.template.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息模板详细数据对象
 *
 * <AUTHOR>
 * @date 2021/5/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "消息模板详细数据对象")
public class MessageTemplateDetailDTO extends MessageTemplateDTO{

    @Schema(description= "消息标题")
    private String title;

    @Schema(description= "配置类型，强制、订阅")
    private Integer forceFlag;

    @Schema(description= "模板分类")
    private Integer catagory;

    @Schema(description= "消息模板说明")
    private String remark;

    @Schema(description= "流程类型，如果要和工作流关联，设置其流程类型，0为没有关联工作流")
    private Long processType;

    @Schema(description= "消息重要度")
    private Integer importantLevel;

}
