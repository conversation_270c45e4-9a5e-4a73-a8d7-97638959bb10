package com.sinitek.sirm.common.message.template.dto;

import cn.hutool.core.bean.BeanUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageSendConfigDTO{

    Integer sendType;

    Map<String, Object> attributes;

    public static MessageSendConfigDTO buildToMessageSendConfig(BaseMessageSpecificAttrDTO specificAttr) {
        return MessageSendConfigDTO.builder()
                .sendType(specificAttr.getSendType())
                .attributes(BeanUtil.beanToMap(specificAttr))
                .build();
    }
}
