package com.sinitek.sirm.common.action.dto;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date  2022/6/27
 */
@Data
@Schema(description = "动作执行历史查询参数dto")
@EqualsAndHashCode(callSuper = true)
public class ActionExcuteHistoryQueryDTO extends PageDataParam {

    @Schema(description = "动作id")
    private Long actionId;

    @Schema(description = "事件发布记录id")
    private String eventPublishLogId;

    @Schema(description = "执行对象类型")
    private String sourceName;

    @Schema(description = "执行状态")
    private Integer status;

    @Schema(description = "执行时间范围")
    private List<Date> time;

    @Schema(description = "触发开始时间")
    private Date startTime;

    @Schema(description = "触发结束时间")
    private Date endTime;
}
