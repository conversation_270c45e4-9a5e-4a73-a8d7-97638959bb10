package com.sinitek.sirm.menu.service;

import com.sinitek.sirm.common.sysmenu.dto.MenuCascaderDTO;
import com.sinitek.sirm.menu.dto.UserMenuDTO;

import java.util.List;
import java.util.Map;

/**
 * 菜单统一Api
 *
 * <AUTHOR>
 * @date 2022/11/28
 */
public interface ISysMenuExtService {


    /**
     * 根据ID获取系统菜单
     * @return
     */
    UserMenuDTO getSysMenuById(long menuId);

    /**
     * 根据菜单id列表获取到菜单列表
     * @param menuIdList
     * @return
     */
    List<UserMenuDTO> findSysMenuListByIdList(List<Long> menuIdList);

    /**
     * 获取当前用户有权限的菜单树结构的信息-用于表单项目
     * @return
     */
    List<MenuCascaderDTO> findRightAuthSysMenuTree();

    /**
     * 保存菜单
     * @param sysMenu
     */
    Long saveSysMenu(UserMenuDTO sysMenu);

    /**
     * 根据菜单编号查询上级菜单Id列表
     * @param menuId
     * @return
     */
    List<Long> findParentMenuIdListByMenuId(long menuId);

    /**
     * 删除菜单
     * @param id 菜单id
     */
    void delSysMenusById(long id);

    /**
     * 检查用户组织结构是否拥有这些菜单id的权限
     * @param orgId
     * @param menuIdList
     * @return
     */
    @SuppressWarnings("squid:ReturnMapCheck")
    Map<Long, Boolean> checkMenuIdListHaveAuth(String orgId, List<Long> menuIdList);
}
