package com.sinitek.sirm.tableview.service;

import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;
import com.sinitek.sirm.tableview.dto.TableViewConditionRespDTO;
import com.sinitek.sirm.tableview.dto.TableViewRespDTO;

import java.util.Map;

/**
 * 表格视图查询回调方法
 *
 * <AUTHOR>
 * date 2023-7-13
 */
public interface ITableViewCallbackService {

    /**
     * 判断是否是对应的表格
     * @param code
     * @return
     */
    boolean isTheTableCode(String code);

    /**
     * 表格视图查询回调 - 用于组装扩展数据
     * @param tableViewRespDTO
     * @return
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<String, Object> getExtensionDataMap(TableViewRespDTO tableViewRespDTO);

    /**
     * 自定义条件的SQL
     * @param conditionRespDTO
     * @return
     */
    default String customConditionSql(TableViewConditionRespDTO conditionRespDTO) {
        return null;
    }

    /**
     * 组装字段值
     * @param fieldValue
     * @return
     */
    default String toStringFieldValue(String fieldValue) {
        return "'" + fieldValue + "'";
    }
}
