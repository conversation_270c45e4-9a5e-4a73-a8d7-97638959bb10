package com.sinitek.cloud.sirmapp.function.dto;

import com.sinitek.cloud.sirmapp.base.BaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 功能分组 对应DTO
 *
 * <AUTHOR>
 * @date 2021/02/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "功能分组DTO")
public class SirmFunctionGroupDTO extends BaseInfoDTO {

    @Schema(description = "父类编号")
    private Long parentId;

    @Schema(description = "顺序")
    private int sort;

    @Schema(description = "分类说明")
    private String brief;

    @Schema(description = "分类名称")
    private String name;
}
