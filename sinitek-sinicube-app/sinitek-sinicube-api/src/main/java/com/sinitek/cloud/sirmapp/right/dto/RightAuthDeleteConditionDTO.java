package com.sinitek.cloud.sirmapp.right.dto;

import com.sinitek.cloud.sirmapp.right.message.RightMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 删除RightAuth条件DTO
 *
 * <AUTHOR>
 * @date 2021/02/04
 */
@Data
@Schema(description = "删除RightAuth条件DTO")
public class RightAuthDeleteConditionDTO {

    @NotBlank(message = "{"+ RightMessageCode.RIGHT_OBJ_ID_ID_CAN_NOT_NULL +"}")
    @Schema(description = "对象主键值",required = true)
    private String objectKey;

    @NotBlank(message = "{"+ RightMessageCode.RIGHT_DEFINE_KEY_CAN_NOT_NULL +"}")
    @Schema(description = "权限分类",required = true)
    private String rightDefineKey;

    @NotEmpty(message = "{"+ RightMessageCode.RIGHT_TYPES_CAN_NOT_NULL +"}")
    @Schema(description = "授权类型数组",required = true)
    private String[] rightTypes;
}
