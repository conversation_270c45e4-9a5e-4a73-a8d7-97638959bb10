package com.sinitek.cloud.sirmapp.right.message;

/**
 * 权限系列 MessageCode 常量
 *
 * <AUTHOR>
 * @date 2021-03-31
 */
public class RightMessageCode {

    /**
     * 授权组织结构Id不能为空
     */
    public final static String ORG_ID_CAN_NOT_NULL = "1000001";

    /**
     * 权限对象Id不能为空
     */
    public final static String RIGHT_OBJ_ID_ID_CAN_NOT_NULL = "1000002";

    /**
     * 权限分类不能为空
     */
    public final static String RIGHT_DEFINE_KEY_CAN_NOT_NULL = "1000003";

    /**
     * 是否拒绝权限不能为空
     */
    public final static String REJECT_FLAG_CAN_NOT_NULL = "1000004";

    /**
     * 授权类型不能为空
     */
    public final static String RIGHT_TYPES_CAN_NOT_NULL = "1000005";

    /**
     * 权限分类[{0}]不存在
     */
    public final static String RIGHT_DEFINE_KEY_DOES_NOT_EXIST  = "1000006";

    /**
     * 组织结构Id[{0}]不存在
     */
    public final static String ORG_ID_DOES_NOT_EXIST  = "1000007";
}
