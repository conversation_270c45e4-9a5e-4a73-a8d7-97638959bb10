package com.sinitek.cloud.sirmapp.attachment.dto;

import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "新增附件列表DTO")
public class SaveAttachmentDTO {

    @NotNull(message = "{"+ CommonMessageCode.ATTACHMENT_SOURCE_ID_CAN_NOT_NULL +"}")
    @Schema(description = "来源id")
    Long sourceId;

    @NotBlank(message = "{" + CommonMessageCode.ATTACHMENT_SOURCE_ENTITY_CAN_NOT_NULL + "}")
    @Schema(description = "来源实体")
    String sourceEntity;

    @Schema(description = "保存的附件列表")
    List<AttachmentDTO> attachmentList;
}
