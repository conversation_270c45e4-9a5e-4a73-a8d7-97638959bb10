package com.sinitek.cloud.sirmapp.right.dto;

import com.sinitek.cloud.sirmapp.right.message.RightMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 添加RightAuth属性DTO
 *
 * <AUTHOR>
 * @date 2021/02/04
 */
@Data
@Schema(description = "添加RightAuth属性DTO")
public class RightAuthSaveDTO {

    @NotBlank(message = "{"+ RightMessageCode.ORG_ID_CAN_NOT_NULL +"}")
    @Schema(description = "组织结构Id",required = true)
    private String orgId;

    @NotBlank(message = "{"+ RightMessageCode.RIGHT_OBJ_ID_ID_CAN_NOT_NULL +"}")
    @Schema(description = "权限对象Id",required = true)
    private String rightObjId;

    @NotBlank(message = "{"+ RightMessageCode.RIGHT_DEFINE_KEY_CAN_NOT_NULL +"}")
    @Schema(description = "权限分类",required = true)
    private String rightDefineKey;

    @NotNull(message = "{"+ RightMessageCode.REJECT_FLAG_CAN_NOT_NULL +"}")
    @Schema(description = "是否为拒绝权限",required = true)
    private Boolean rejectFlag;

    @NotEmpty(message = "{"+ RightMessageCode.RIGHT_TYPES_CAN_NOT_NULL +"}")
    @Schema(description = "授权类型数组",required = true)
    private String[] rightTypes;
}
