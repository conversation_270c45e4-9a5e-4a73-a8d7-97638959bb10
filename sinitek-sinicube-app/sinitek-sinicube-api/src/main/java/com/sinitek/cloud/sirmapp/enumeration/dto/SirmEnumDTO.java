package com.sinitek.cloud.sirmapp.enumeration.dto;

import com.sinitek.cloud.sirmapp.base.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/6/15 15:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SirmEnumDTO extends BaseDTO {

    private String cataLog;

    private String type;

    private String name;

    private Integer value;

    private String strvalue;

    private String description;

    private Integer sort;
}
