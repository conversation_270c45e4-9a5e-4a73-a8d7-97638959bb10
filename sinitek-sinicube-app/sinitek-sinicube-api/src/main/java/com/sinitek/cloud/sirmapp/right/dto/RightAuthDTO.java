package com.sinitek.cloud.sirmapp.right.dto;

import com.sinitek.cloud.sirmapp.base.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用权限RightAuth对应的DTO
 *
 * <AUTHOR>
 * @date 2021/02/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "通用权限RightAuth对应的DTO")
public class RightAuthDTO extends BaseDTO {

    @Schema(description = "授权组织结构ID")
    private String authOrgId;

    @Schema(description = "授权组织结构表达式")
    private String authOrgExp;

    @Schema(description = "对象关键值")
    private String objectKey;

    @Schema(description = "权限定义KEY")
    private String rightDefineKey;

    @Schema(description = "是否拒绝")
    private boolean rejectFlag;

    @Schema(description = "授权类型")
    private String rightType;

}
