package com.sinitek.cloud.sirmapp.org.dto;

import lombok.Data;

/**
 * UserCheckResult相关信息的DTO
 *
 * <AUTHOR>
 * @date 2021-03-02
 */
@Data
public class UserCheckResultDTO {

    /**
     * 用户会话检查成功
     */
    public static final  int CHECK_SESSION_SUCCESS = 11;
    /**
     * 用户会话检查失败
     */
    public static final  int CHECK_SESSION_FAILED = 10;
    /**
     * 用户名或密码错误
     */
    public static final  int CHECK_LOGON_INVALIDUSER = 20;
    /**
     * 用户被锁定
     */
    public static final  int CHECK_LOGON_LOCKUSER = 21;
    /**
     * 用户状态为离职
     */
    public static final  int CHECK_LOGON_INSERVICE = 22;
    /**
     * LDAP服务器连不上
     */
    public static final  int CHECK_LOGON_LDAPURL = 23;
    /**
     * 用户在系统中不存在
     */
    public static final  int CHECK_LOGON_NOSUCHUSER = 24;

    /**
     * ldap参数配置错误
     */
    public static final  int CHECK_LDAP_PARAM = 26;

    /**
     * success 状态的常量值
     */
    public static final  int SUCCESS_CODE = 1;

    /**
     * 检查结果
     */
    private int result = 0;
    /**
     * 成功标识(1=成功;0=失败)
     */
    private int success = 0;

    /**
     * 登陆返回的 accessToken
     */
    private String accessToken;
}
