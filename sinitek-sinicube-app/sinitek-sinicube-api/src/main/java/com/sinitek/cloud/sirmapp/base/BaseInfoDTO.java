package com.sinitek.cloud.sirmapp.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 *  和BaseEntity对应的 BaseDTO
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Data
public class BaseInfoDTO {

    @Schema(description = "主键")
    private Long objId;

    @Schema(description = "创建时间")
    private Date createTimeStamp;

    @Schema(description = "修改时间")
    private Date updateTimeStamp;

    @Schema(description = "版本号")
    private Integer version;
}
