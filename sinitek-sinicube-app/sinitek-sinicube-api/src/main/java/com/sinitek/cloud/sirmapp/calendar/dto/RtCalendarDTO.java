package com.sinitek.cloud.sirmapp.calendar.dto;

import com.sinitek.cloud.sirmapp.base.BaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 日程 RtCalendar 对应DTO
 *
 * <AUTHOR>
 * @date 2021/02/03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "日程对应DTO")
public class RtCalendarDTO extends BaseInfoDTO {

    @Schema(description = "日程类型")
    private Integer type;

    @Schema(description= "日程开始时间")
    private Date beginDate;

    @Schema(description= "日程结束时间")
    private Date endDate;

    @Schema(description= "录入人ID")
    private String inputId;

    @Schema(description= "日程主题")
    private String subject;

    @Schema(description= "触发外出的实体名称")
    private String sourceEntity;

    @Schema(description= "触发外出的实体ID")
    private Long sourceId;

    @Schema(description= "人员ID")
    private String empId;

    @Schema(description= "内容")
    private String content;

    @Schema(description= "日常大类型")
    private Integer columnType;

    @Schema(description= "日程状态")
    private Integer status;

    @Schema(description= "提醒标记")
    private Integer remindFlag;

    @Schema(description= "提醒方式")
    private String remindType;

    @Schema(description= "提醒类型")
    private Integer remindMethod;

    @Schema(description= "提醒时间值")
    private String remindValue;

    @Schema(description= "是否完成")
    private Integer completeStatus;

    @Schema(description= "url")
    private String url;

}
