/**
 * File Name:    IOrgService.java
 * <p>
 * File Desc:    组织结构图查询器
 * <p>
 * Product AB:   Spirit_1_0_0
 * <p>
 * Product Name: Spirit
 * <p>
 * Module Name:  组织结构图
 * <p>
 * Module AB:    ORG
 * <p>
 * Author:       何立勇
 * <p>
 * History:      2011-3-1 created by 何立勇
 */
package com.sinitek.spirit.org.core;

import com.sinitek.spirit.org.core.dto.EmployeeDTO;
import com.sinitek.spirit.org.core.dto.OrgSpiritObjectDTO;
import com.sinitek.spirit.org.core.dto.RelationshipDTO;
import com.sinitek.spirit.org.core.dto.UnitDTO;

import java.util.Collection;
import java.util.List;

/**
 * 组织结构图查询器
 * <p/>
 * 组织结构图查询器提供多种查询操作。所有的查询操作基于组织结构图查询表达式实现，
 * 在表达式查询的基础上，提供了一些常用的查询API，如获取子节点，查找用户等常用API。
 * <p/>
 * <code>getRoot()</code>方法用于获得组织结构图的根节点。
 * <p/>
 * <p/>
 * 组织结构图的查询路径表达式的使用方法如下：<br/>
 * <b>1、设计概念</b><br/>
 * 路径表达式用于表达与一次完整的组织结构图检索操作。组织结构图中所有的检索操作可以归纳为两种：即向上查找和向下遍历。<br/>
 * 无论是向上查找或向下遍历，除检索方向不同外，都含有一些通用的查询条件，
 * 例如：沿什么关系类型检索，被查找的对象叫什么名称，是否跨越多个层次（向上追溯或向下递归），查找对象是什么类型（是小组还是岗位，或者部门）等。<br/>
 * 组织结构查询表达式，通过一个操作函数表达一次向上或向下的检索操作，该操作中可以附加检索条件。
 * 由多个操作函数通过连接符连接起来可以构成整个组织结构查询表达式，一个表达式中的操作函数数量不限。
 * 例如，我们可以从指定的某一组织结构对象出发，先向上检索，然后向下遍历，最终找到我们要的对象。
 * <p/>
 *
 * <b>2、操作函数</b><br/>
 * 操作函数包括向上查找和向下查找两种操作，分别用UP和DOWN表示。语法规则为：<br/>
 * <code>
 * UP(查询条件1;查询条件2;……)<br/>
 * DOWN(查询条件1;查询条件2;……)
 * </code><br/>
 * 查询条件为可选参数，如果无查询条件，可直接表达为：UP()或DOWN()。<br/>
 * 多个操作函数之间用“:”连接。例如：<code>up():down(查询条件1;查询条件2)</code>，表示先进性UP操作，然后进行Down操作。
 * <p/>
 *
 * <b>3、查询条件</b><br/>
 * 系统支持以下查询条件和对应的查询操作：<br/>
 * <table border="1" width="100%">
 * <tr align=center style="font-weight:bold">
 * <td>查询条件</td><td>可用操作符</td>
 * <td>功能介绍</td><td>举例</td>
 * </tr>
 * <tr>
 * <td>orgname</td><td>=，like，!=，not like（通配符采用?和%）</td>
 * <td>检索组织结构对象名称，如员工名称，小组名称等</td>
 * <td><code>orgname='研发组'</code> 表示查找一个名为“研发组”的对象；或<code>orgname like '%研发%'</code> 表示查找一个名称中含有“研发”字样的组织结构节点</td>
 * </tr>
 * <tr>
 * <td>relationshiptype</td><td>=，in，!=，not in</td>
 * <td>检索关系类型，如下属关系或主管关系等。不使用此查询条件则表示所有关系关系类型</td>
 * <td><code>relationshiptype='SUPERVISION'</code> 表示查找SUPERVISION关系；
 * <code>relationshiptype in ['SUPERVISION','UNDERLINE']</code> 表示查找SUPERVISION或UNDERLINE关系。<br/>
 * 如果要表示否定，可以在“=”前加入“！”或者在“in”前面加入“not”。
 * 例如：<code>relactionshiptype != 'SUPERVISION'</code>或<code>relationshiptype not in ['SUPERVISION','UNDERLINE']</code></td>
 * </tr>
 * <tr>
 * <td>orgtype</td><td>=，in，!=, not in</td>
 * <td>检索组织结构类型，检索员工使用Employee，检索节点类型使用对应的字符，如TEAM，UNIT等，不使用该查询条件表示查询所有节点类型，包括员工。</td>
 * <td><code>orgtype='employee'</code>表示检索员工；<code>orgtype in ['team','position']</code>表示检索小组或岗位。同样可以使用“!”或“not”表示否定。写法同上。</td>
 * </tr>
 * <tr>
 * <td>level</td><td>=，&lt，&gt，&lt=，&gt=，!=</td>
 * <td>检索时的层级限制。不使用该查询条件表示只查找一个层次，不进行递归。使用<code>level = all</code>可表示检索时不限制层次，全递归。</td>
 * <td><code>level&lt=3</code>表示便利层次数量小于等于3层</td>
 * </tr>
 * </table><Br/>
 * <font color=blue>注意：查询条件名称大小写<b>不</b>敏感</font>
 * <p/>
 * 在使用检索条件时请注意以下语法规则：<br/>
 * <ol>
 * <li>多个查询条件之间用“;”分隔</li>
 * <li>查询条件采用的格式是：条件类型+操作符+判断条件组成，例如：<code>orgtype='employee'</code></li>
 * <li>判断条件如果是字符类数据，应使用“'”包括其内容，例如<code>orgname='技术部'</code></li>
 * </ol>
 * <p/>
 *
 * <b>4、使用方法举例</b><br/>
 * <table width="100%" border="1">
 * <tr align=center style="font-weight:bold">
 * <td>检索需求</td><td>检索表达式</td>
 * </tr>
 * <tr>
 * <td>找到某节点下的所有员工</td>
 * <td><code>DOWN(levels=all;orgtype='employee')</code></td>
 * </tr>
 * <tr>
 * <td>查找当前所在小组的组长<br/>
 * 假设从某员工作为查找起点，该员工属于某小组，小组的组长通过SUPERVISION关系制定。
 * </td>
 * <td><code>UP(orgtype='team'):DOWN(orgtype='employee';relationshiptype='SUPERVISION')</code><br/>
 * 解释：首先查找员工所在的小组，然后根据所在的小组查找组内由“主管”关系关联的员工。
 * </td>
 * </tr>
 * </table>
 * <p/>
 *
 * <b>5、Q&A</b><br/>
 * <b>Q:</b>为什么不能通过表达式的方式实现根据ID的检索，如，直接根据一个Unit的ID获取对象实例？<br/>
 * <b>A:</b>表达式查询是描述一个查询“路径”的，根据ID查询属于精确定位查询，这种查询没有路径，因此应该直接使用<code>getOrgObject( String orgObjId )</code>这一API直接获取
 * <p/>
 * <b>Q:</b>基于表达式的查询必须先获得一个“起点”，实际代码应该如何编写？<br/>
 * <b>A:</b>代码中因首先获得“起点”对象，然后通过路径检索数据。例如，希望获取某部门或小组下属的所有员工，可以这样编码：<br/>
 * <code>
 * IOrgObject fromPoint = finder.getOrgObject( "UNIT0003" );<br/>
 * List<IOrgObject> employees = finder.findOrgObjectsByPath( fromPoint, "DOWN(orgtype='employee';level=all)" );<br/>
 * </code>
 * 当明确知道查询算法，但无法定位“起点”的时候，可以使用组织结构根节点替代，例如：<Br/>
 * <code>
 * List<IOrgObject> employees = finder.findOrgObjectsByPath( finder.getRoot(), "DOWN(orgtype='employee';level=all)" );<br/>
 * </code>
 * <p/>
 * <b>Q:</b>为了提高检索性能，我是否应该对检索结果进行缓存？<br/>
 * <b>A:</b>组织结构图的核心实现已经考虑了这种因素，并在内部做了缓存优化，请不要在应用程序中尝试对检索结果进行缓存。应采用“随取随用，再用再取”的策略。
 * <p/>
 * <b>Q:</b>如果一个查询表达式的检索结果中包含相同的对象，在返回结果中是否会有重复数据？例如，某部门下属两个岗位，两个岗位中都有员工A，当我根据该部门获取下属所有员工的时候，是否会产生重复数据？<br/>
 * <b>A:</b>组织结构图会根据查询结果自动过滤重复数据，无论何时，都不会在返回结果中出现重复数据。
 * <p/>
 * <b>Q:</b>为了简化编程，提高编程效率，在应用系统使用组织结构API的时候，有什么建议？<br/>
 * <b>A:</b>为了简化编程，建议在使用组织结构图的时候，根据各业务系统实际需求情况，首先建立使用规则，然后就可以将一些常用业务的检索表达式固定下来，定义为系统常量或作为系统核心配置，直接调用即可，不用反复编写。具体的建议包括：<br/>
 * <ol>
 * <li>定义组织结构图的使用方法，如用部门表示什么业务含义，什么时候使用组，组长的业务概念是什么等；</li>
 * <li>通过合适的自定义组织类型（orgtype）和组织关系类型（relationshipType）定义业务规范，统一业务概念；</li>
 * <li>将常用的查询表达式定义为常量或配置为系统参数，如获取本组组长，获取本部门的部门领导等业务操作；</li>
 * </ol>
 * <p/>
 *
 * <AUTHOR> He
 * @version 1.0
 */
public interface IOrgFinder {

    /**
     * 获取组织结构根节点id
     * @return
     */
    String getRootObjectKey();

    /**
     * 获取组织结构根节点
     * @return
     */
    UnitDTO getRoot();


    /**
     * 根据id查询组织结构节点
     * @param orgId      节点orgId
     * @return
     */
    OrgSpiritObjectDTO getOrgObject(String orgId);

    /**
     * 通过组织结构id集合，批量查询组织结构信息
     * @param orgIds        orgId集合
     * @return
     */
    List<OrgSpiritObjectDTO> findOrgObjects(Collection<String> orgIds);

    /**
     * 通过员工id，获取员工信息
     * @param orgId     员工orgId
     * @return
     */
    EmployeeDTO getEmployeeById(String orgId);

    /**
     * 通过userId，获取员工信息
     * @param userId       员工userId
     * @return  EmployeeDTO
     */
    EmployeeDTO getEmployeeByUserId(String userId);

    /**
     * 通过员工姓名，获取员工信息
     * @param employeeName       员工userId
     * @return  EmployeeDTO
     */
    List<EmployeeDTO> findEmployeesByName(String employeeName, boolean fuzzy);

    /**
     * 通过员工姓名，获取员工信息
     * @param employeeName       员工userId
     * @return  EmployeeDTO
     */
    List<EmployeeDTO> findEmployeesByName(String employeeName);

    /**
     * 获取所有未分配的员工（包括部门,岗位,小组,角色）
     * @return  List<EmployeeDTO>
     */
    List<EmployeeDTO> findUnassignedEmployees();


    /**
     * 通过机构id，获取机构信息（包括部门,岗位,小组,角色）
     * @param orgId     机构orgi=Id
     * @return UnitDTO
     */
    UnitDTO getUnitById(String orgId);

    /**
     * 通过组织结构名，获取组织结构信息
     * @param unitType       组织结构类型
     * @param unitName       组织结构名
     * @param fuzzy          模糊匹配
     * @return  UnitDTO
     */
    List<UnitDTO> findUnits(String unitType, String unitName, boolean fuzzy);

    /**
     * 通过组织结构名，获取组织结构信息
     * @param unitType       组织结构类型
     * @param unitName       组织结构名
     * @return  UnitDTO
     */
    List<UnitDTO> findUnits(String unitType, String unitName);



    // -----------------  下面是包含层级关系的查询  ----------------------

    /**
     * 获取指定机构的父机构
     * @param unitId    机构id
     * @return  UnitDTO 返回父机构。如果unitId是root机构或者是员工，则返回null
     */
    UnitDTO getParentUnitByUnitId(String unitId);


    /**
     * 获取orgId对应节点的直接父级组织结构
     *  @param orgId         组织结构id， 可以是机构，也可以是员工
     *  @param unitType     机构类型
     */
    List<UnitDTO> findParentsByOrgId(String orgId, String unitType);

    /**
     * 获取orgId对应节点的父级组织结构
     * <p>
     * 可以通过unitTypes限制查询类型，比如查询员工所在部门、所在岗位、所在小组、所在角色
     * </p>
     * <code>
     * finder.findAllParentByOrgId(empId, UnitTypeEnum.UNIT.toString());<br/>
     * </code>
     *
     * @param orgId         组织结构id， 可以是机构，也可以是员工
     * @param unitTypes     机构类型，为 null 则不限制类型
     * @param all           向上往上找到所有的父节点。true一直找到根，false直接父节点
     * @return
     */
    List<UnitDTO> findParentsByOrgId(String orgId, String[] unitTypes, boolean all);


    /**
     * 查找下级机构节点
     *
     * @param parentId     父节点id
     * @param isAll         是否查询全部子孙后代
     * @param match         模糊匹配节点名称
     * @param unitTypes     下级机构类型
     * @param includeIds    可选节点范围
     * @return
     */
    List<UnitDTO> findAllUnits(String parentId, List<String> unitTypes, boolean isAll, String match, List<String> includeIds);

    /**
     * 获取指定节点下的所有组织结构节点
     * @param unitId        父节点id
     * @param unitTypes     机构类型，为 null 则不限制类型
     * @return
     */
    List<UnitDTO> findAllChildrenUnits(String unitId, String... unitTypes);


    /**
     * 获取指定节点下的直属对组织结构节点
     * @param unitId        父节点id
     * @param unitTypes     机构类型，为 null 则不限制类型
     * @return
     */
    List<UnitDTO> findDirectChildrenUnits(String unitId, String... unitTypes);


    /**
     * 查询指定机构下的所有员工
     *
     * @param unitId        机构id
     * @return
     */
    List<EmployeeDTO> findAllChildrenEmployee(String unitId);

    /**
     * 查询指定机构下的所有员工
     *
     * @param unitId        机构id
     * @param deep          是否查询下级机构，只有查询部门下员工时需要（true：包括子部门的员工，false：只包含当前部门所属岗位的员工）
     * @return
     */
    List<EmployeeDTO> findAllChildrenEmployee(String unitId, boolean deep);


    /**
     * 检查组织结构对象是否存在
     * @param orgId
     * @return
     */
    boolean checkOrgObjectExists(String orgId);

    /**
     * 检查组织结构关系是否存在
     * @param orgId
     * @param type
     * @return
     */
    boolean checkOrgRelationshipExists(String orgId, String type);

    /**
     * 检查组织结构关系是否存在
     * @param fromId
     * @param toId
     * @param type
     * @return
     */
    boolean checkOrgRelationshipExists(String fromId, String toId, String type);

    /**
     * 检查是否存在子孙关系
     *
     * @param fromId            关系出发点
     * @param toId              关系目标点
     * @param relationTypes     关系类型，非必填
     * @return      存在子孙关系，则返回true
     */
    boolean checkDescendantExists(String fromId, String toId, String... relationTypes);

    /**
     * 获取从指定节点出发 或者 到达指定节点 的关系
     * @param orgId
     * @return
     */
    List<RelationshipDTO> findRelationsByOrgId(String orgId);

    /**
     * 查询组织结构关系
     * @param fromObjId
     * @param toObjId
     * @param type
     * @return
     */
    List<RelationshipDTO> queryRelations(String fromObjId, String toObjId, String type);


    /**
     * 检查组织关系的左右值是否初始化
     * @return  已初始化则返回true，未初始化或者数值异常则返回false
     */
    boolean checkRelationsInit();
    
}
