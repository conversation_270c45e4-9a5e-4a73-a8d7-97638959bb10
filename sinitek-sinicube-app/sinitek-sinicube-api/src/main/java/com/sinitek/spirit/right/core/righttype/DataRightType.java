/**
 * File Name:    DataRightType.java
 *
 * File Desc:    数据操作权限，包括增删改查
 *
 * Product AB:   Spirit_1_0_0
 *
 * Product Name: Spirit
 *
 * Module Name:  权限
 *
 * Module AB:    RIGHT
 *
 * Author:       何立勇
 *
 * History:      11-4-26 created by 何立勇
 */
package com.sinitek.spirit.right.core.righttype;

import com.sinitek.base.enumsupport.AbstractEnumItem;
import com.sinitek.spirit.right.core.IRightType;

/**
 * 数据操作权限，包括增删改查
 *
 * <AUTHOR>
 * @version 1.0
 */
public final class DataRightType extends AbstractEnumItem implements IRightType
{
    /**
     * 读取，表示可以进行读操作，权限值为“READ”
     */
    public static final DataRightType READ
            = new DataRightType( "READ", 1, "读取", null );

    /**
     * 添加，表示可以进行新增操作，权限值为“ADD”
     */
    public static final DataRightType ADD
            = new DataRightType( "ADD", 2, "添加", null );

    /**
     * 更新，表示可以进行更新操作，权限值为“UPDATE”
     */
    public static final DataRightType UPDATE
            = new DataRightType( "UPDATE", 4, "更新", null );

    /**
     * 删除，表示可以进行删除操作，权限值为“DELETE”
     */
    public static final DataRightType DELETE
            = new DataRightType( "DELETE", 8, "删除", null );

    protected DataRightType( String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue )
    {
        super( enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue );
    }
}
