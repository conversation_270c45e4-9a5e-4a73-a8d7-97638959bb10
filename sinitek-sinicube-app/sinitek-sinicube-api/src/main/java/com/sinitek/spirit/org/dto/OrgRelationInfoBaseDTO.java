package com.sinitek.spirit.org.dto;

import com.sinitek.cloud.sirmapp.base.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织结构关系 - DTO
 *
 * <AUTHOR>
 * @data 2024-08-21
*/
@Data
@EqualsAndHashCode(callSuper = true)
public class OrgRelationInfoBaseDTO extends BaseDTO {

    @Schema(description = "起点节点ID")
    private String fromObjectId;

    @Schema(description = "目标节点ID")
    private String toObjectId;

    @Schema(description = "关系类型")
    private String relationType;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "high_val")
    private Integer highVal;

    @Schema(description = "sort")
    private Integer sort;

    @Schema(description = "code_val")
    private String codeVal;
}

