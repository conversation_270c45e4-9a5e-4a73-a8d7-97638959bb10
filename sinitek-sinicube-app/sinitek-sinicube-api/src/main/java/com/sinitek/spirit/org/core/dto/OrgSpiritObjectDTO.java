/**
 * File Name:    IOrgObject.java
 *
 * File Desc:    组织结构节点抽象接口
 *
 * Product AB:   Spirit_1_0_0
 *
 * Product Name: Spirit
 *
 * Module Name:  组织结构图
 *
 * Module AB:    ORG
 *
 * Author:       何立勇
 *
 * History:      2011-2-27 created by 何立勇
 */
package com.sinitek.spirit.org.core.dto;

/**
 * 组织结构节点抽象接口
 *
 * 组织结构中的节点分为组织节点和个人节点，其中个人节点必须是叶子节点。
 *
 * 所有的在组织结构图中的节点，都是本接口的实例
 *
 * <AUTHOR> He
 * @version 1.0
 */
public class OrgSpiritObjectDTO {

    /**
     * 组织结构对象ID
     */
    private String id;

    /**
     * 组织结构对象名称
     */
    private String name;

    /**
     * 组织结构对象描述
     */
    private String description;

    private boolean valid = true;

    /**
     * 用于乐观锁检查的version字段
     */
    private int versionId = 0;

    /**
     * 同步系统的用户唯一标识
     */
    private String origId;

    private String dataSrc;

    /**
     * 数据所属机构Id
     */
    private String tenantId;

    /**
     * 数据所属机构Id
     */
    private String mechanismCode;

    /**
     * 用户Id,仅组织结构为用户时有值
     */
    private String userId;

    /**
     * 获得节点ID
     *
     * 节点ID是唯一的，在不同类型的节点之间也不重复。
     *
     * @return 节点ID
     */
    public String getId()
    {
        return id;
    }

    public void setId( String id )
    {
        this.id = id;
    }

    /**
     * 获得节点名称。
     *
     * 节点名称不能为NULL
     *
     * @return 节点名称
     */
    public String getName()
    {
        return name;
    }

    public void setName( String name )
    {
        this.name = name;
    }

    /**
     * 获得节点描述信息
     *
     * @return 节点描述信息，可能为NULL。
     */
    public String getDescription()
    {
        return description;
    }

    public void setDescription( String description )
    {
        this.description = description;
    }

    /**
     * 该组织结构节点是否有效（未被逻辑删除）
     *
     * @return 节点有效性
     */
    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public int getVersionId()
    {
        return versionId;
    }

    public void setVersionId( int versionId )
    {
        this.versionId = versionId;
    }

    public String getOrigId() {
        return origId;
    }

    public void setOrigId(String origId) {
        this.origId = origId;
    }

    public String getDataSrc() {
        return dataSrc;
    }

    public void setDataSrc(String dataSrc) {
        this.dataSrc = dataSrc;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getMechanismCode() {
        return mechanismCode;
    }

    public void setMechanismCode(String mechanismCode) {
        this.mechanismCode = mechanismCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public int hashCode() {
        return id.toUpperCase().hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (obj instanceof OrgSpiritObjectDTO) {
            OrgSpiritObjectDTO orgObject = (OrgSpiritObjectDTO) obj;
            return id.equalsIgnoreCase(orgObject.getId());
        }
        return false;
    }


}
