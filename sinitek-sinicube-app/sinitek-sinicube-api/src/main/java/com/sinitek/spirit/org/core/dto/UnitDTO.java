/**
 * File Name:    IOrgUnit.java
 *
 * File Desc:    组织节点接口类
 *
 * Product AB:   Spirit_1_0_0
 *
 * Product Name: Spirit
 *
 * Module Name:  组织结构图
 *
 * Module AB:    ORG
 *
 * Author:       何立勇
 *
 * History:      2011-2-27 created by 何立勇
 */
package com.sinitek.spirit.org.core.dto;

/**
 * 组织节点接口类
 * <p/>
 * 一个组织节点可以通过其类型来制定节点的种类，类型包括但不局限于：部门,岗位,小组,角色
 *
 * <AUTHOR>
 * @version 1.0
 */
public class UnitDTO extends OrgSpiritObjectDTO
{

    /**
     * 父节点id
     */
    private String parentId;

    /**
     * 组织节点类型
     */
    private String unitType;

    public UnitDTO() {
    }

    public UnitDTO(UnitDTO unit) {
        setParentId(unit.getParentId());
        setId(unit.getId());
        setName(unit.getName());
        setDescription(unit.getDescription());
        setUnitType(unit.getUnitType());
        setValid(unit.isValid());
        setVersionId(unit.getVersionId());
    }

    /**
     * 获得组织节点类型
     * <p/>
     * 节点类型不能为NULL
     *
     * @return 表示组织节点类型的字符串
     */
    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }


    /**
     * 获得父节点id
     * <p/>
     * 根节点的父节点id为 0
     *
     * @return 表示组织节点类型的字符串
     */
    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
