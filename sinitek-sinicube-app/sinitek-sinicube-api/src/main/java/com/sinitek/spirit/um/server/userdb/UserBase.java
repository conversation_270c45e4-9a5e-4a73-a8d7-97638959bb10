package com.sinitek.spirit.um.server.userdb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 存储在Shiro Cache中的用户信息
 *
 * <AUTHOR>
 * @date 2019-12-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "在线用户基本信息")
public class UserBase implements Serializable {

    private static final long serialVersionUID = 4920691408760792884L;

    @Schema(description = "用户Id")
    private String userId;

    @Schema(description = "用户登陆名")
    private String userName;

    @Schema(description = "所属机构")
    private String tenantId;

    @Schema(description = "客户端IP")
    private String clientIp;

    @Schema(description = "用户终端类型")
    private String terminal;

    @Schema(description = "客户端操作系统")
    private String operatingSystem;

    @Schema(description = "登陆时间")
    private Date logonTime;

    @Schema(description = "最后一次访问时间")
    private Date lastAccessTime;

    @Schema(description = "判断是否过期(实现用户自动登录情况下，修改/重置密码场景也能踢出用户)")
    private boolean expireFlag = false;

    @Schema(description = "会话分组(PC端是PC端，Excel端是Excel端)")
    private String group;

    @Schema(description = "是否强制用户修改密码标识")
    private boolean userPwdForcedModification = false;

    public UserBase(String userId, String userName) {
        this.userId = userId;
        this.userName = userName;
    }

    public UserBase(String userId, String userName, String tenantId) {
        this.userId = userId;
        this.userName = userName;
        this.tenantId = tenantId;
    }

    public UserBase(String userId, String userName, String tenantId, String clientIp, String terminal, String operatingSystem) {
        this.userId = userId;
        this.userName = userName;
        this.tenantId = tenantId;
        this.clientIp = clientIp;
        this.terminal = terminal;
        this.operatingSystem = operatingSystem;
    }

    public UserBase(String userId, String userName, String tenantId, String clientIp, String terminal, String operatingSystem, String group) {
        this.userId = userId;
        this.userName = userName;
        this.tenantId = tenantId;
        this.clientIp = clientIp;
        this.terminal = terminal;
        this.operatingSystem = operatingSystem;
        this.group = group;
    }

    public UserBase(String userId, String userName, String tenantId, String clientIp, String terminal, String operatingSystem, String group, boolean userPwdForcedModification) {
        this.userId = userId;
        this.userName = userName;
        this.tenantId = tenantId;
        this.clientIp = clientIp;
        this.terminal = terminal;
        this.operatingSystem = operatingSystem;
        this.group = group;
        this.userPwdForcedModification = userPwdForcedModification;
    }
}
