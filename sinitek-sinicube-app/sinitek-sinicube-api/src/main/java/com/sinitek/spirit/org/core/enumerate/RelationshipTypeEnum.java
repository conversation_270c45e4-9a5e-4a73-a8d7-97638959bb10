package com.sinitek.spirit.org.core.enumerate;

/**
 * 关系类型
 */
public enum RelationshipTypeEnum {

    /**
     * 下属关系
     * <p/>
     * 表示一个组织结构对象属于另一个组织结构对象，主要是UNIT类型的机构之间的关系。比如某岗位是某部门的下属，某小组是某部门的下属等。
     * <p/>
     * 下属关系一般只用来表示上下层次关系，不表示其他业务含义。如业务中的管理关系等。
     * <p/>
     * 在关系树中，可能是叶子节点。其他关系肯定是叶子节点。
     */
    UNDERLINE,

    /**
     * 岗位成员关系
     * <p/>
     * 常用于表达岗位与人员之间的关系。
     */
    SUPERVISION,

    /**
     * 小组成员关系
     * <p/>
     * 常用于表达小组与人员之间的关系。
     */
    SUPERTEAM,

    /**
     * 角色成员关系
     * <p/>
     * 常用于表达角色与人员之间的关系。
     */
    SUPERROLE,

    /**
     * 行政上级关系
     * <p/>
     * 用于表达岗位与岗位之间的行政上级关系。
     */
    SUPEXECUTE,


    /**
     * 小组长关系
     * <p/>
     * 用于表达小组中的哪个员工是小组长。
     */
    TEAMER,
}
