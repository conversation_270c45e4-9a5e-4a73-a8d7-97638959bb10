package com.sinitek.spirit.org.server.service;

import com.sinitek.spirit.org.dto.OrgRelationInfoBaseDTO;

import java.util.List;

/**
 * IOrgRelationInfo 统一API
 *
 * <AUTHOR>
 * @data 2024-08-21
*/
public interface IOrgRelationInfoExtService {

    /**
     * 获取根据组织结构编号查询关系类型
     * @param orgId
     * @return List<OrgRelationInfo> 类型
     */
    List<OrgRelationInfoBaseDTO> findRelationTypeByOrgId(String orgId);


    /**
     * 查找组织结构关联
     * @param fromObjectId 关系开始编号
     * @param toObjectId 关系结束编号
     * @param type 关系类型:对应数据库列relationtype
     * @return List<OrgRelationInfo> 组织结构关系列表
     */
    public List<OrgRelationInfoBaseDTO> findOrgRelationInfo(String fromObjectId, String toObjectId, String type);
}
