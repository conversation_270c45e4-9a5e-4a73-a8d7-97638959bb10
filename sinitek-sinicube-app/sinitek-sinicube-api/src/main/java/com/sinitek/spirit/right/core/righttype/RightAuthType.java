package com.sinitek.spirit.right.core.righttype;

import com.sinitek.base.enumsupport.AbstractEnumItem;
import com.sinitek.spirit.right.core.IRightType;

/**
 * <AUTHOR>
 * @Date 2022/9/23
 * 权限节点类型
 */
public class RightAuthType extends AbstractEnumItem implements IRightType {

    public static final DataRightType SELF
            = new DataRightType( "SELF", 1, "仅自身", null );

    public static final DataRightType PARENT
            = new DataRightType( "PARENT", 2, "仅上级", null );

    public static final DataRightType MIX
            = new DataRightType( "MIX", 3, "混合", null );
    /**
     * 只允许子类使用的构造函数
     *
     * @param enumItemName         枚举项名称
     * @param enumItemValue        枚举项值
     * @param enumItemInfo         枚举项说明信息
     * @param enumItemDisplayValue 枚举项显示值
     */
    protected RightAuthType(String enumItemName, int enumItemValue, String enumItemInfo, String enumItemDisplayValue) {
        super(enumItemName, enumItemValue, enumItemInfo, enumItemDisplayValue);
    }
}
