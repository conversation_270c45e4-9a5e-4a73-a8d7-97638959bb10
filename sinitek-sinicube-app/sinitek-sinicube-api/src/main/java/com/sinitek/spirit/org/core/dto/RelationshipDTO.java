/**
 * File Name:    IOrgRelationship.java
 *
 * File Desc:    组织关系
 *
 * Product AB:   Spirit_1_0_0
 *
 * Product Name: Spirit
 *
 * Module Name:  组织结构图
 *
 * Module AB:    ORG
 *
 * Author:       何立勇
 *
 * History:      2011-2-27 created by 何立勇
 */
package com.sinitek.spirit.org.core.dto;

/**
 * 组织关系
 * <p/>
 * 组织关系表示任意两个组织结构对象之间的关联关系。一个关联关系是有方向的（矢量），它表示为从一个组织节点指向另一个组织节点。
 * 这个方向的概念，在系统中，用From和To表示。
 * <p/>
 * 通过关系的起始节点，目标节点和关系类型可以唯一确认一个关系实例。
 * <p/>
 * 一对多或多对多关系，由多个关系对象实例进行表达。例如一个岗位下属多个员工，那么这个岗位节点下存在多个关系类型为“下属”的IRelationship接口实例。
 *
 * <AUTHOR> He
 * @version 1.0
 */
public class RelationshipDTO
{
    private OrgSpiritObjectDTO fromObject;

    private OrgSpiritObjectDTO toObject;

    private String type;


    public RelationshipDTO() {

    }

    public RelationshipDTO(RelationshipDTO relationship) {
        setFromObject(relationship.getFromObject());
        setToObject(relationship.getToObject());
        setType(relationship.getType());
    }

    /**
     * 获得组织结构关系的出发节点
     *
     * @return 出发节点
     */
    public OrgSpiritObjectDTO getFromObject() {
        return fromObject;
    }

    public void setFromObject(OrgSpiritObjectDTO fromObject) {
        this.fromObject = fromObject;
    }

    /**
     * 获得组织结构关系的目标节点
     *
     * @return 目标节点
     */
    public OrgSpiritObjectDTO getToObject() {
        return toObject;
    }

    public void setToObject(OrgSpiritObjectDTO toObject) {
        this.toObject = toObject;
    }


    /**
     * 获得关系类型
     * <p/>
     * 关系类型是通过字符串方式表达的，系统支持两种关系，即下属关系和主管关系
     *
     * @return 关系类型
     */
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getKey() {
        return fromObject.getId() + "_" + toObject.getId() + "_" + type.toUpperCase();
    }


    @Override
    public int hashCode() {
        int code = 1;
        code = 31 * code + fromObject.hashCode();
        code = 31 * code + toObject.hashCode();
        code = 31 * code + type.toUpperCase().hashCode();
        return code;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (obj instanceof RelationshipDTO) {
            RelationshipDTO relationship = (RelationshipDTO) obj;
            return fromObject.equals(relationship.getFromObject()) &&
                    toObject.equals(relationship.getToObject()) &&
                    type.equalsIgnoreCase(relationship.getType());
        }
        return false;
    }

}
