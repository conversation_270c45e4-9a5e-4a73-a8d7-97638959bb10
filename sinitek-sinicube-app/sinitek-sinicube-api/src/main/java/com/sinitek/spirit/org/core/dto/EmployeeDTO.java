/**
 * File Name:    IEmployee.java
 * <p>
 * File Desc:    员工接口
 * <p>
 * Product AB:   Spirit_1_0_0
 * <p>
 * Product Name: Spirit
 * <p>
 * Module Name:  组织结构图
 * <p>
 * Module AB:    ORG
 * <p>
 * Author:       何立勇
 * <p>
 * History:      2011-2-27 created by 何立勇
 */
package com.sinitek.spirit.org.core.dto;

/**
 * 员工接口
 *
 * 该接口用于表示一个员工，员工在组织结构图中只能是叶子节点
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 */
public class EmployeeDTO extends OrgSpiritObjectDTO {
    private String userId;

    public EmployeeDTO() {
    }

    public EmployeeDTO(EmployeeDTO employee) {
        setId(employee.getId());
        setName(employee.getName());
        setDescription(employee.getDescription());
        setUserId(employee.getUserId());
        setInservice(employee.isInservice());
        setVersionId(employee.getVersionId());
        setOrigId(employee.getOrigId());
        setDataSrc(employee.getDataSrc());
    }

    /**
     * 获得用户ID
     *
     * @return 获得该员工对应的用户ID
     */
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 是否在职，true表示在职
     * @return 在职状态
     */
    public Boolean isInservice() {
        return isValid();
    }

    public void setInservice(Boolean inservice) {
        if (inservice != null) {
            setValid(inservice);
        }
    }


}
