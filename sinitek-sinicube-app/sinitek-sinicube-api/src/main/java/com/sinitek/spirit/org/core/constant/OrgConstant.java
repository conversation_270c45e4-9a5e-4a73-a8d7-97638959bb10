package com.sinitek.spirit.org.core.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.text.MessageFormat;

/**
 * 常量类
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrgConstant {
    public static final String ORG_TYPE = "orgtype";
    public static final String ORG_ID = "orgid";
    public static final String ORG_NAME = "orgname";
    public static final String UNIT_TYPE = "unittype";
    public static final String TENANT_ID = "tenantId";
    public static final String DESCRIPTION_STR = "description";
    public static final String VERSION_ID = "versionid";
    public static final String INSERVICE_KEY = "inservice";
    public static final String USER_ID = "userid";
    public static final String FUZZY_KEY = "fuzzy";
    public static final String FROM_ORGID = "fromorgid";
    public static final String EXISTS_KEY = "exists";
    public static final String RELATION_TYPE = "relationtype";

    public static final String DIRECTION_UP = "UP";
    public static final String DIRECTION_DOWN = "DOWN";

    public static final String SPRTORG_02 = "sprtorg_02";
    public static final String SPOG_0007 = "SPOG0007";
    public static final String SPOG_0025 = "SPOG0025";
    public static final String SPOG_1025 = "SPOG1025";
    public static final String SPOG_1004 = "SPOG1004";
    public static final String SPOG_0035 = "SPOG0035";

    public static final String DEBUG_MSG = "从[{0}]指向[{1}]的类型为[{2}]的关联关系已加入{3}队列";
    public static final String UPDATE_WARN_MSG = "无法识别数据行[{0}]的数据类型[{1}]";


    public static final String SPOG_0013 = "SPOG0013";
    public static final String SPOG_1001 = "SPOG1001";
    public static final String SPOG_0033 = "SPOG0033";

    public static final String EMP_IS_ADMIN_CACHE = "empIsAdminCache";
    public static final String EMP_IS_MECHANISM_ADMIN_CACHE = "empIsMechanismAdminCache";

    public static final String ROLE_ADMIN = "管理员";
    public static final String ROLE_MECHANISM_ADMIN = "机构管理员";

    public static final String ROOT_OBJECT_KEY = "99999";

    /**
     * 当orgId为0，则会认为是系统操作
     */
    public static final String SYSTEM_ORG_ID_VALUE = "0";

    /**
     * ORGID 序列的名称
     */
    public static final String SEQ_ORGID_NAME = "SEQ_ORGID";

    public static final int ORG_TYPE_DEPT = 1;
    public static final int ORG_TYPE_POST = 2;
    public static final int ORG_TYPE_TEAM = 4;
    public static final int ORG_TYPE_EMP = 8;
    public static final int ORG_TYPE_ROLE = 16;

    public static final int ORG_ID_INDEX = 0;
    public static final int ORG_NAME_INDEX = 1;
    public static final int ORG_TYPE_INDEX = 2;

    public static String getDebugMsg(String fromObjid, String toObjid, String relationshipType, String type) {
        return MessageFormat.format(DEBUG_MSG, fromObjid, toObjid, relationshipType, type);
    }

    public static String getUpdateWarnMsg(String key1, String key2) {
        return MessageFormat.format(UPDATE_WARN_MSG, key1, key2);
    }
}
