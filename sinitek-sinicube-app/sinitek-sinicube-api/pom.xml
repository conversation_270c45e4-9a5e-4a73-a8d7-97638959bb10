<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sinitek-sinicube-app</artifactId>
        <groupId>com.sinitek.sinicube</groupId>
        <version>8.0.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sinitek-sinicube-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-utils</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
           <groupId>jakarta.servlet</groupId>
           <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-remind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-desensitization</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
