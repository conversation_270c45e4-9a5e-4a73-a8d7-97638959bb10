package com.sinitek.sinicube.license.constant;

import org.apache.commons.lang3.StringUtils;

import java.io.File;

/**
 * SiniCube License 常量
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
public class LicenseVerifyConstant {

    private LicenseVerifyConstant() {
        throw new IllegalStateException("Utility class");
    }

    public static final String DEFAULT_PUBLIC_KEYS_STORE_PATH_NAME = "publicCerts.keystore";

    public static final String DEFAULT_PUBLIC_KEYS_STORE_PATH = "license";

    public static String getDefaultStorePath () {
        String[] items = {LicenseVerifyConstant.DEFAULT_PUBLIC_KEYS_STORE_PATH, LicenseVerifyConstant.DEFAULT_PUBLIC_KEYS_STORE_PATH_NAME};
        return File.separator + StringUtils.join(items, File.separator);
    }
}
