package com.sinitek.sinicube.license.holder;

import com.sinitek.sinicube.license.CustomLicenseManager;
import de.schlichtherle.license.LicenseManager;
import de.schlichtherle.license.LicenseParam;

/**
 * de.schlichtherle.license.LicenseManager的单例
 *
 * <AUTHOR>
 * @date 2022-12-02
 */
public class SiniCubeLicenseManagerHolder {

    private static LicenseManager licenseManager;

    private SiniCubeLicenseManagerHolder() {
        throw new IllegalStateException("Utility class");
    }

    public static synchronized LicenseManager getInstance(LicenseParam param){
        if(licenseManager == null){
            licenseManager = new CustomLicenseManager(param);
        }

        return licenseManager;
    }

}
