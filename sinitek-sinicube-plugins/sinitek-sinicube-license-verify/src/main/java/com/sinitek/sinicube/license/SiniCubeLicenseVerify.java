package com.sinitek.sinicube.license;

import com.sinitek.sinicube.license.constant.LicenseVerifyConstant;
import com.sinitek.sinicube.license.holder.SiniCubeLicenseManagerHolder;
import com.sinitek.sinicube.license.properties.SiniCubeLicenseProperties;
import de.schlichtherle.license.*;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.prefs.Preferences;

/**
 * License校验类
 *
 * <AUTHOR>
 * @date 2022-12-02
 */
@Slf4j
@SuppressFBWarnings(value = "PATH_TRAVERSAL_IN", justification = "该类中的文件路径是从后端配置文件而非用户输入,无须处理")
public class SiniCubeLicenseVerify {

    /**
     * 安装License证书
     *
     * @param param
     * @return
     */
    @SneakyThrows
    public synchronized LicenseContent install(SiniCubeLicenseProperties param){
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        LicenseManager licenseManager = SiniCubeLicenseManagerHolder.getInstance(initLicenseParam(param));
        licenseManager.uninstall();

        LicenseContent result = licenseManager.install(new File(param.getLicensePath()));
        log.info("证书名称: {}", result.getSubject());
        log.info(MessageFormat.format("证书安装成功，证书有效期：{0} - {1}",format.format(result.getNotBefore()),format.format(result.getNotAfter())));

        return result;
    }

    /**
     * 校验License证书
     *
     * @return
     */
    public boolean verify(){
        LicenseManager licenseManager = SiniCubeLicenseManagerHolder.getInstance(null);
        try {
            licenseManager.verify();
            return true;
        }catch (Exception e){
            log.error("证书校验失败！{}","无其他核心参数",e);
            return false;
        }
    }

    /**
     * 初始化证书生成参数
     * @param param License校验类需要的参数
     * @return de.schlichtherle.license.LicenseParam
     */
    private LicenseParam initLicenseParam(SiniCubeLicenseProperties param){
        Preferences preferences = Preferences.userNodeForPackage(SiniCubeLicenseVerify.class);

        CipherParam cipherParam = new DefaultCipherParam(param.getStorePass());


        String publicKeysStorePath = param.getPublicKeysStorePath();
        if (StringUtils.isBlank(publicKeysStorePath)) {
            publicKeysStorePath = LicenseVerifyConstant.getDefaultStorePath();
        }

        KeyStoreParam publicStoreParam = new LicenseClientCustomKeyStoreParam(SiniCubeLicenseVerify.class
                ,publicKeysStorePath
                ,param.getPublicAlias()
                ,param.getStorePass()
                ,null);

        return new DefaultLicenseParam(param.getSubject()
                ,preferences
                ,publicStoreParam
                ,cipherParam);
    }

}
