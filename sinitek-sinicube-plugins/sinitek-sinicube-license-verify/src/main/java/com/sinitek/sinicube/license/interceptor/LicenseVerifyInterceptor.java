package com.sinitek.sinicube.license.interceptor;

import com.sinitek.sinicube.license.SiniCubeLicenseVerify;
import com.sinitek.sirm.common.utils.HttpUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.nio.charset.StandardCharsets;

/**
 * 许可证拦截器检查
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Slf4j
@Component
public class LicenseVerifyInterceptor implements HandlerInterceptor  {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        SiniCubeLicenseVerify licenseVerify = new SiniCubeLicenseVerify();
        boolean verifyResult = licenseVerify.verify();
        if(verifyResult){
            return true;
        }
        
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        HttpUtils.buildErrorResponse(response, HttpStatus.FORBIDDEN, "您的证书无效，请核查服务器是否取得授权或重新申请证书！");
        return false;
    }
}