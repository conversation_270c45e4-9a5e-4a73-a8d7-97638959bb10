package com.sinitek.sinicube.license.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SiniCube License配置
 *
 * <AUTHOR>
 * @date 2022-12-02
 */
@Data
@Component
@ConfigurationProperties(prefix = "sinicube.license")
public class SiniCubeLicenseProperties {

    /**
     * 证书subject
     */
    private String subject;

    /**
     * 公钥别称
     */
    private String publicAlias = "publicCert";

    /**
     * 访问公钥库的密码
     */
    private String storePass = "sinitek-sinicube-123456";

    /**
     * 证书生成路径
     */
    private String licensePath;

    /**
     * 密钥库存储路径
     */
    private String publicKeysStorePath;
}
