package com.sinitek.sinicube.license;

import com.sinitek.sinicube.license.constant.LicenseVerifyConstant;
import de.schlichtherle.license.AbstractKeyStoreParam;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

import java.io.*;

/**
 * 自定义KeyStoreParam，用于将公私钥存储文件存放到其他磁盘位置而不是项目中
 *
 * <AUTHOR>
 * @date 2022/12/02
 */
@SuppressFBWarnings(value = "PATH_TRAVERSAL_IN", justification = "该类中的文件路径是从后端配置文件而非用户输入,无须处理")
public class LicenseClientCustomKeyStoreParam extends AbstractKeyStoreParam {

    /**
     * 公钥/私钥在磁盘上的存储路径
     */
    private String storePath;
    private String alias;
    private String storePwd;
    private String keyPwd;

    private Class<?> myClazz;

    public LicenseClientCustomKeyStoreParam(Class<?> clazz, String resource, String alias, String storePwd, String keyPwd) {
        super(clazz, resource);
        this.myClazz = clazz;
        this.storePath = resource;
        this.alias = alias;
        this.storePwd = storePwd;
        this.keyPwd = keyPwd;
    }


    @Override
    public String getAlias() {
        return alias;
    }

    @Override
    public String getStorePwd() {
        return storePwd;
    }

    @Override
    public String getKeyPwd() {
        return keyPwd;
    }

    /**
     * 复写de.schlichtherle.license.AbstractKeyStoreParam的getStream()方法<br/>
     * 用于将公私钥存储文件存放到其他磁盘位置而不是项目中
     * @param
     * @return java.io.InputStream
     */
    @Override
    public InputStream getStream() throws IOException {
        String path = LicenseVerifyConstant.getDefaultStorePath();
        if (path.equals(storePath)) {
            InputStream in = myClazz.getResourceAsStream(path);
            if (null == in) {
                throw new FileNotFoundException(path);
            }
            return in;
        }
        return new FileInputStream(new File(storePath));
    }


}
