package com.sinitek.sinicube.license.config;

import com.sinitek.sinicube.license.interceptor.LicenseVerifyInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 许可证web配置
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Configuration
public class LicenseWebConfig implements WebMvcConfigurer {

    @Autowired
    private LicenseVerifyInterceptor licenceVerifyInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(licenceVerifyInterceptor)
                .addPathPatterns("/**");
    }
}
