package com.sinitek.sinicube.license.init;

import com.sinitek.sinicube.license.SiniCubeLicenseVerify;
import com.sinitek.sinicube.license.properties.SiniCubeLicenseProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 在项目启动时安装证书
 *
 * <AUTHOR>
 * @date 2022-12-02
 */
@Slf4j
@Component
public class SiniCubeLicenseCheckListener implements ApplicationRunner {

    @Autowired
    private SiniCubeLicenseProperties siniCubeLicenseProperties;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("++++++++ 开始安装证书 ++++++++");

        SiniCubeLicenseVerify licenseVerify = new SiniCubeLicenseVerify();
        licenseVerify.install(siniCubeLicenseProperties);

        log.info("++++++++ 证书安装结束 ++++++++");
    }
}
