package com.sinitek.sinicube.desensitization.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.sinitek.sinicube.desensitization.annotation.Sensitive;
import com.sinitek.sinicube.desensitization.enumerate.SensitiveStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 序列化注解自定义实现
 * JsonSerializer<Object>：Object 类型，serialize()方法用于将修改后的数据载入
 * <AUTHOR>
 * @date 2022/12/26
 */
@Slf4j
public class SensitiveJsonSerializer extends JsonSerializer<Object> implements ContextualSerializer {

    private SensitiveStrategy strategy;
    private SensitiveStrategy[] key;
    private String[] item;
    private String regex;
    private String replacement;
    private String[] regexs;
    private String[] replacements;
    private boolean mapType = false;
    private boolean stringType = false;
    private boolean customizeType = false;

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (mapType) {
            //对Map操作
            Map<String, Object> map = new ObjectMapper().convertValue(value, Map.class);
            //开始替换内容
            gen.writeStartObject();
            for (String k : map.keySet()) {
                //遍历整个Map的key
                boolean index = true;
                for (int i = 0;i < item.length;i++) {
                    //遍历想要脱敏的字段
                    if (Objects.equals(k, item[i])) {
                        //如果匹配，则使用和字段对应的规则进行匹配替换
                        index = false;
                        //检查是否为自定义规则，如果是走自定义规则
                        if (customizeType) {
                            gen.writeStringField(k, MapUtils.getString(map,k).replaceAll(regexs[i],replacements[i]));
                        } else {
                            gen.writeStringField(k, (String) key[i].desensitizer().apply(MapUtils.getString(map,k)));
                        }

                        break;
                    }
                }
                if (index) {
                    //如果没有和脱敏字段匹配，则保留原数据
                    gen.writeStringField(k, MapUtils.getString(map,k));
                }
            }
            gen.writeEndObject();
        } else if (stringType) {
            //对String操作，按照对应规则进行匹配替换；检查是否为自定义规则，如果是走自定义规则
            if (customizeType) {
                gen.writeString(value.toString().replaceAll(regex,replacement));
            }else {
                gen.writeString((String) strategy.desensitizer().apply(value.toString()));
            }
        }
    }

    /**
     * 获取属性上的注解属性
     */
    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        Sensitive annotation = property.getAnnotation(Sensitive.class);
        if (Objects.nonNull(annotation)) {
            //检查是否为自定义规则，如果是添加自定义需要的数据
            if ((!annotation.regex().isEmpty()&&!annotation.replacement().isEmpty())) {
                //String类型自定义
                this.customizeType = true;
                this.regex = annotation.regex();
                this.replacement = annotation.replacement();
            } else if ((!annotation.regexs()[0].isEmpty()&&!annotation.replacements()[0].isEmpty())) {
                //Map类型自定义
                this.customizeType = true;
                this.regexs = annotation.regexs();
                this.replacements = annotation.replacements();
            }
            //如果类型为String则设置strategy
            if (Objects.equals(String.class, property.getType().getRawClass())) {
                this.stringType = true;
                this.strategy = annotation.strategy();
                return this;
                //如果类型为Map则设置key和item
            } else if (Objects.equals(Map.class, property.getType().getRawClass())) {
                this.mapType = true;
                this.key = annotation.key();
                this.item = annotation.item();
                return this;
            }
        }
        return prov.findValueSerializer(property.getType(), property);

    }
}
