package com.sinitek.sinicube.desensitization.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sinitek.sinicube.desensitization.config.SensitiveJsonSerializer;
import com.sinitek.sinicube.desensitization.enumerate.SensitiveStrategy;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义jackson注解，标注在属性上
 * <AUTHOR>
 * @date 2022/12/26
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@JacksonAnnotationsInside
@JsonSerialize(using = SensitiveJsonSerializer.class)
public @interface Sensitive {
    //String 脱敏策略
    SensitiveStrategy strategy() default SensitiveStrategy.DEFAULT_TYPE;
    //Map 脱敏策略
    SensitiveStrategy[] key() default SensitiveStrategy.DEFAULT_TYPE ;
    String[] item() default "";
    //String 自定义脱敏策略
    String regex() default "";
    String replacement() default "";
    //Map 自定义脱敏策略
    String[] regexs() default "";
    String[] replacements() default "";
}
