package com.sinitek.sinicube.desensitization.enumerate;
import java.util.function.Function;

/**
 * 脱敏策略，枚举类，针对不同的数据定制特定的策略
 * <AUTHOR>
 */

public enum SensitiveStrategy {
    /**
     * 默认类型
     *  -不进行脱敏
     */
    DEFAULT_TYPE(s -> s.replaceAll("(\\S)(\\S*)", "$1$2")),
    /**
     * 用户名
     *  -将第二个字符串替换为*
     *  -如 管*员、马*、上*婉儿
     */
    USERNAME(s -> s.replaceAll("(\\S)\\S(\\S*)", "$1*$2")),
    /**
     * 身份证
     *  -将身份证中间10位替换为****
     *  如 5346****364X
     */
    ID_CARD(s -> s.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1****$2")),
    /**
     * 手机号
     *  -将手机号第4位到第7位替换为****
     *  -如 133****4567
     */
    PHONE(s -> s.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2")),
    /**
     * 地址
     *  -将4-5、8、11替换为*把最后两位替换为***
     *  如 四川省****市武*区新*望大厦***
     */
    ADDRESS(s -> s.replaceAll("(\\S{3})\\S{2}(\\S{2})\\S{1}(\\S{2})\\S{1}(\\S*)\\S{2}", "$1**$2*$3*$4***"));


    private final Function<String, Object> desensitizer;

    SensitiveStrategy(Function<String, Object> desensitizer) {
        this.desensitizer = desensitizer;
    }

    public Function<String, Object> desensitizer() {
        return desensitizer;
    }
}
