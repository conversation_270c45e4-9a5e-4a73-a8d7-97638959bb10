package com.sinitek.sinicube.desensitization;

import com.sinitek.sinicube.desensitization.annotation.Sensitive;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 扫描 前端敏感数据脱敏注解 @Sensitive 的场景
 *
 * <AUTHOR>
 * @date 2022/12/26
 */
@Slf4j
@Component
@ConfigurationProperties(prefix = "sinicube.desensitization")
public class DesensitizationApplicationRunner {

    private String scanPackage = "com.sinitek";

    private static final String EXT = "class";
    private static final Integer DIVISION_NUMBER = 2;

    public void run()  {
        log.info("开始扫描包:{}前端敏感数据脱敏注解 @Sensitive 的场景",scanPackage);
        //获取项目路径
        String pkgDirName = scanPackage.replace('.', '/');
        URL url = Thread.currentThread().getContextClassLoader().getResource(pkgDirName);
        String pkgPath = url != null ? url.getFile() : null;
        //判断目录是否为空
        if (pkgPath == null){
            log.error("要扫描的目录为空: {}", pkgPath);
            return;
        }
        File fPkgDir = new File(pkgPath);
        //获取File下class文件（包括子目录）
        Collection<File> allClassFile = FileUtils.listFiles(fPkgDir, new String[]{EXT}, true);
        for (File curFile : allClassFile){
            //遍历class，创建有注解的属性列表
            List<String> fieldStr = new ArrayList<>();
            try {
                //根据File和path获取class类
                Class<?> curClass = getClassObj(curFile);
                for (Field field : curClass.getDeclaredFields()) {
                    //遍历当前class中的属性，判断属性上是否有脱敏注解 @Sensitive
                    if (field.isAnnotationPresent(Sensitive.class)){
                        //如果有注解，将属性名加入列表
                        fieldStr.add(field.getName());
                    }
                }
                //打印脱敏场景
                if (!fieldStr.isEmpty()) {
                    log.info("类:{} 脱敏属性[{}]", curClass, String.join(",", fieldStr));
                }
            } catch (ClassNotFoundException e) {
                log.error("加载{}失败:{}", curFile,e);
            }
        }
    }

    /**
     * 加载类
     * @param file
     * @return
     * @throws ClassNotFoundException
     */
    private static Class<?> getClassObj(File file) throws ClassNotFoundException{
        // 去除pkgPath的.class后缀
        String absPath = file.getAbsolutePath().substring(0, file.getAbsolutePath().length() - EXT.length() - 1);
        //将 / 替换为 .
        String className = absPath.replace(File.separatorChar, '.');
        //获取包名
        className = String.format("com.%s", className.split(".com.", DIVISION_NUMBER)[1]);
        //根据包名获取class
        return Thread.currentThread().getContextClassLoader().loadClass(className);

    }
}
