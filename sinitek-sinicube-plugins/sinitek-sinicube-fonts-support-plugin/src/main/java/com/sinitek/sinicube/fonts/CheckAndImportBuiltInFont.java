package com.sinitek.sinicube.fonts;

import com.sinitek.sirm.common.utils.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.io.IOException;
import java.io.InputStream;

/**
 * 检测和导入内置字体
 *
 * <AUTHOR>
 * date 2023-08-03
 */
@Slf4j
@Component
public class CheckAndImportBuiltInFont {

    /**
     * 但操作系统中找不到默认字体时，导入内置字体
     */
    public void init () {
        String fontName = GlobalConstant.DEFAULT_FONT;
        log.info("检测到系统中没有字体: {}", fontName);
        String builtInFont = "/template/simsun.ttc";
        boolean imported = importFont(builtInFont);
        if (!imported) {
            log.info("加载内置字体: {} 失败", builtInFont);
        }

        String builtInFontTtf = "/template/simsunb.ttf";
        boolean importedTtf = importFont(builtInFontTtf);
        if (!importedTtf) {
            log.info("加载内置字体: {} 失败", builtInFontTtf);
        }
    }

    /**
     * 判断是否有选择的字体
     * @param fontName
     * @return
     */
    @SuppressWarnings("squid:CatchCheck")
    public static boolean isFontAvailable(String fontName) {
        try {
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            String[] availableFonts = ge.getAvailableFontFamilyNames();
            for (String font : availableFonts) {
                log.info("当前字体的名称: {}", font);
                if (font.equalsIgnoreCase(fontName)) {
                    return true;
                }
            }
            return false;
        } catch (Throwable e) {
            // 异常说明服务器没有一个字体，直接false即可
            return false;
        }
    }

    /**
     * 导入内置字体
     * @param fontFileName
     * @return
     */
    public static boolean importFont(String fontFileName) {
        try {
            InputStream inputStream = CheckAndImportBuiltInFont.class.getResourceAsStream(fontFileName);
            if (null == inputStream) {
                log.info("加载内置字体: {}失败,获取为空",fontFileName);
                return false;
            }
            Font font = Font.createFont(Font.TRUETYPE_FONT, inputStream);
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            ge.registerFont(font);
            return true;
        } catch (FontFormatException | IOException e) {
            log.error("导入内置字体: {}, 发生异常", fontFileName, e);
            return false;
        }
    }
}