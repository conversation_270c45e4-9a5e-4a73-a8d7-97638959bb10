# 動作模型消息碼規則
# 12-00-0000
# [動作模型12-模塊-錯誤碼]

# 01 - 通用
# 02 - 動作模型
# 03 - 動作定義
# 04 - 動作綁定
# 05 - 動作執行


# 動作管理通用


# 動作定義
12030001=動作名稱不能為空
12030002=動作名稱過長
12030003=動作編碼不能為空
12030004=動作編碼過長
12030005=遠程動作的url過長
12030006=找不到編碼為[{0}]的動作模板
12030007=本地接口地址對應的類不存在
12030008=綁定的動作名字為空,【name】節點不存在或內容為空
12030009=綁定的動作code為空,【code】節點不存在或內容為空
12030010=綁定的動作表單數據為空,【componentData】節點不存在或內容為空

# 綁定動作
12040001=綁定動作時，對象id不能為空
12040002=綁定動作時，對象id過長
12040003=綁定動作時，對象名稱不能為空
12040004=綁定動作時，對象名稱過長
12040005=綁定動作時，找不到該動作[id={0}]
12040006=綁定動作[{0}]時，關聯了Vue組件，但是沒有傳對應的表單內容
12040007=綁定動作[{0}]時，Vue表單內容轉換出現異常
12040008=動作【{0}】已經被綁定，請不要重複綁定

# 動作執行
12050001=執行成功
12050002=執行失敗
12050003=事件
12050004=定時任務
12050005=消息發送動作的發送方式為空，跳過該消息的發送
12050006=消息發送動作的消息內容為空，跳過該消息的發送
12050007=發送消息動作轉換初始參數異常
12050008=收件人為空，跳過該消息的發送
12050009=添加日程動作轉換初始參數異常
12050010=發起流程動作轉換初始參數異常
12050011=未通過sysCode獲取到對應的流程模板，跳過該動作
12050012=發起流程動作中不存在關聯實體數據模型，跳過該動作
12050013=遠程調用動作轉換初始參數異常
12050014=遠程調用動作請求頭轉換異常，跳過該動作
12050015=遠程調用動作請求參數轉換異常，跳過該動作
12050016=發送消息動作標題最大長度為100
12050017=發送消息動作標題不允許為空
12050018=發送消息動作內容不允許為空
12050019=發送消息動作提醒對象不允許為空
12050020=發送消息動作提醒對象最大長度為100
12050021=遠程調用動作請求url不允許為空
12050022=遠程調用動作請求url最大長度為200
12050023=遠程調用動作請求頭最大長度為200
12050024=遠程調用動作請求參數最大長度為200
12050025=綁定動作失敗，表單數據異常
12050026=動作執行條件最大長度為100
12050027=提醒對象不允許為空
12050028=提醒對象數據不允許為空
12050029=動作類型有誤

# 附件消息碼規則
# 14-00-0000
# [附件13-模塊-錯誤碼]

# 01 - 通用
# 02 - 附件正文

# 通用
14010001=目標附件【{0}】不存在，請確認附件

# 附件正文
14020001=附件正文生成指紋錯誤，歸檔失敗
14020002=不能重復插入相同md5的附件內容
14020003=附件{0}的md5值不能為空
14020004=保存附件失敗，找不到附件【{0}】
14020005=附件【{0}】保存失敗