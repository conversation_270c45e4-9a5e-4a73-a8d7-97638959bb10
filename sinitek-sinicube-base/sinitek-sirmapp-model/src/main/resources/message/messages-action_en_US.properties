# 动作模型消息码规则
# 12-00-0000
# [动作模型12-模块-错误码]

# 01 - 通用
# 02 - 动作模型
# 03 - 动作定义
# 04 - 动作绑定
# 05 - 动作执行


# 动作管理通用

# Action definition
12030001=Action name cannot be empty
12030002=The action name is too long
12030003=The action code cannot be empty
12030004=The action code is too long
12030005=The URL of the remote action is too long
12030006=Cannot find the action template coded as [{0}]
12030007=The class corresponding to the local interface address does not exist
12030008=of the bound action name is empty, the [name] node does not exist or the content is empty
12030009=of the bound action code is empty, the [code] node does not exist or the content is empty
12030010=of bound action sheet data is empty, [componentData] node does not exist or content is empty

# Binding action
12040001=When binding actions, the object id cannot be empty
12040002=The object id is too long when binding actions
12040003=When binding actions, the object name cannot be empty
12040004=The object name is too long when binding actions
12040005=When binding an action, the action cannot be found [id={0}]
12040006=When binding action [{0}], the Vue component is associated, but the corresponding form content is not passed
12040007=When binding action [{0}], Vue form content conversion is abnormal
12040008=The action [{0}] has been bound, please do not bind it again

# 附件消息码规则
# 14-00-0000
# [附件13-模块-错误码]

# 01 - 通用
# 02 - 附件正文

# 通用
14010001=Target attachment[{0}] is not exist, please make it sure

# 附件正文
14020001=Build attachment fingerprint error, archive failed
14020002=Can not insert repeat md5 record
14020003=Attachment {0} property md5 can not be empty
14020004=Failed to save attachment, attachment not found【{0}】
14020005=attachment content [{0}] save failed

# Excute action
12050001=Execute success
12050002=Execute failure
12050003=Event
12050004=Schedule
12050005=The sending mode of the message sending action is empty. The sending of the message is skipped
12050006=The message content of the message sending action is empty. The sending of the message is skipped
12050007=Send message action conversion initial parameter exception
12050008=The recipient is empty and the message is skipped
12050009=Add schedule action to convert initial parameter exception
12050010=The initial parameter of initiating process action conversion is abnormal
12050011=If the corresponding process template is not obtained through sysCode, this action is skipped
12050012=The initiating process action is skipped because the associated entity data model does not exist
12050013=Remote call action conversion initial parameter exception
12050014=The remote call action request header conversion failed. The action is skipped
12050015=The remote call action request parameter conversion is abnormal. The action is skipped
12050016=The maximum length of the send message action title is 100
12050017=The send message action title cannot be empty
12050018=The send message action content cannot be empty
12050019=The send message action alert object cannot be empty
12050020=The maximum length of the send message action reminder object is 100
12050021=The remote call action request url cannot be empty
12050022=The maximum length of a remote call action request url is 200
12050023=The maximum length of the remote call action request header is 200
12050024=The remote call action request parameter has a maximum length of 200
12050025=The binding action failed, and the form data is abnormal
12050026=The maximum length of the operation condition is 100
12050027=The reminder object cannot be empty
12050028=The reminder object data cannot be empty
12050029=The action type error