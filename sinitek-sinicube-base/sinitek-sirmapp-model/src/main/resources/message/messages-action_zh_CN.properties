# 动作模型消息码规则
# 12-00-0000
# [动作模型12-模块-错误码]

# 01 - 通用
# 02 - 动作模型
# 03 - 动作定义
# 04 - 动作绑定
# 05 - 动作执行


# 动作管理通用


# 动作定义
12030001=动作名称不能为空
12030002=动作名称过长
12030003=动作编码不能为空
12030004=动作编码过长
12030005=远程动作的url过长
12030006=找不到编码为[{0}]的动作模板
12030007=本地接口地址对应的类不存在
12030008=绑定的动作名字为空,【name】节点不存在或内容为空
12030009=绑定的动作code为空,【code】节点不存在或内容为空
12030010=绑定的动作表单数据为空,【componentData】节点不存在或内容为空

# 绑定动作
12040001=绑定动作时，对象id不能为空
12040002=绑定动作时，对象id过长
12040003=绑定动作时，对象名称不能为空
12040004=绑定动作时，对象名称过长
12040005=绑定动作时，找不到该动作[id={0}]
12040006=绑定动作[{0}]时，关联了Vue组件，但是没有传对应的表单内容
12040007=绑定动作[{0}]时，Vue表单内容转换出现异常
12040008=动作【{0}】已经被绑定，请不要重复绑定

# 动作执行
12050001=执行成功
12050002=执行失败
12050003=事件
12050004=定时任务
12050005=消息发送动作的发送方式为空，跳过该消息的发送
12050006=消息发送动作的消息内容为空，跳过该消息的发送
12050007=发送消息动作转换初始参数异常
12050008=收件人为空，跳过该消息的发送
12050009=添加日程动作转换初始参数异常
12050010=发起流程动作转换初始参数异常
12050011=未通过sysCode获取到对应的流程模板，跳过该动作
12050012=发起流程动作中不存在关联实体数据模型，跳过该动作
12050013=远程调用动作转换初始参数异常
12050014=远程调用动作请求头转换异常，跳过该动作
12050015=远程调用动作请求参数转换异常，跳过该动作
12050016=发送消息动作标题最大长度为100
12050017=发送消息动作标题不允许为空
12050018=发送消息动作内容不允许为空
12050019=发送消息动作提醒对象不允许为空
12050020=发送消息动作提醒对象最大长度为100
12050021=远程调用动作请求url不允许为空
12050022=远程调用动作请求url最大长度为200
12050023=远程调用动作请求头最大长度为200
12050024=远程调用动作请求参数最大长度为200
12050025=绑定动作失败，表单数据异常
12050026=动作执行条件最大长度为100
12050027=提醒对象不允许为空
12050028=提醒对象数据不允许为空
12050029=动作类型有误

# 附件消息码规则
# 14-00-0000
# [附件13-模块-错误码]

# 01 - 通用
# 02 - 附件正文

# 通用
14010001=目标附件【{0}】不存在，请确认附件

# 附件正文
14020001=附件正文生成指纹错误，归档失败
14020002=不能重复插入相同md5的附件内容
14020003=附件{0}的md5值不能为空
14020004=保存附件失败，找不到附件【{0}】
14020005=附件【{0}】保存失败