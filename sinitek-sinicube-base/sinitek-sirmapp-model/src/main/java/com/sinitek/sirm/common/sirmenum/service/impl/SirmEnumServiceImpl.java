package com.sinitek.sirm.common.sirmenum.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.common.constant.CacheConstant;
import com.sinitek.sirm.common.sirmenum.dto.EnumListDTO;
import com.sinitek.sirm.common.sirmenum.entity.SirmEnum;
import com.sinitek.sirm.common.sirmenum.mapper.SirmEnumMapper;
import com.sinitek.sirm.common.sirmenum.message.EnumMessageCode;
import com.sinitek.sirm.common.sirmenum.service.ISirmEnumService;
import com.sinitek.sirm.common.sirmenum.support.EnumMap;
import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.sirmenum.dto.TypeCataLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: BaseDase
 * Author:      刘建
 * History:     11-5-9 created by 刘建
 */
@Slf4j
@Service
public class SirmEnumServiceImpl extends ServiceImpl<SirmEnumMapper, SirmEnum> implements ISirmEnumService {

    @Lazy
    @Autowired
    private SirmEnumServiceImpl sirmEnumService;

    /**
     * 查询某个模块某个数据类型 枚举数据
     *
     * @param cataLog 模块
     * @param type    数据类型
     * @return key 对应数据库 value  name 对应数据库中name
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    @Override
    public Map<String, String> getSirmEnumMapByCataLogAndType(String cataLog, String type) {
        List<SirmEnum> list = sirmEnumService.findSirmEnumByCatalogAndType(cataLog, type);
        if (list == null || list.size() < 1) {
            return new HashMap<>();
        }
        Map<String, String> map = new LinkedHashMap<>();
        for (int i = 0; i < list.size(); i++) {
            SirmEnum objs = list.get(i);
            String value = objs.getName();
            map.put(objs.getValue() == null ? objs.getStrvalue() : "" + objs.getValue(), value);
        }
        return new EnumMap(map);
    }

    /**
     * 根据模块和值获取枚举名称
     *
     * @param cataLog 模块
     * @param value   值
     * @return ISirmEnum
     */
    @Cacheable(value = CacheConstant.ENUM_CACHE,key = "T(String).valueOf(#cataLog).toUpperCase() + '::' + T(String).valueOf(#type).toUpperCase() + '::' + #value")
    @Override
    public SirmEnum getSirmEnum(String cataLog, String type, Integer value) {
        SirmEnum result = null;
        if (value != null){
            List<SirmEnum> resultList = this.baseMapper.findSirmEnumByCataLogAndTypeAndValue(cataLog,type,value);
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = resultList.get(0);
            }
        }
        return result;
    }

    @Override
    public SirmEnum getSirmEnum(String cataLog, String type, String strValue){
        SirmEnum result = null;
        if (StringUtils.isNotBlank(strValue)){
            List<SirmEnum> sirmEnums = findSirmEnumByCatalogAndType(cataLog, type);
            for(SirmEnum sirmEnum : sirmEnums){
                if (sirmEnum.getStrvalue() != null){
                    if (strValue.equals(sirmEnum.getStrvalue())){
                        result = sirmEnum;
                    }
                }
                else if (sirmEnum.getValue() != null){
                    if (strValue.equals(String.valueOf(sirmEnum.getValue()))){
                        result = sirmEnum;
                    }
                }
                if (result != null){
                    break;
                }
            }
        }
        return result;
    }

    @Override
    public List<SirmEnum> findSirmEnum(List<TypeCataLogDTO> typeCataLogDTOList) {
        List<SirmEnum> enumList = new ArrayList<>();
        for (TypeCataLogDTO typeCataLogDTO : typeCataLogDTOList) {
            List<SirmEnum> list = sirmEnumService.findSirmEnumByCatalogAndType(typeCataLogDTO.getCataLog(), typeCataLogDTO.getType());
            if (CollectionUtils.isNotEmpty(list)){
                for (SirmEnum sirmEnum:list) {
                    enumList.add(sirmEnum);
                }
            }
        }
        return enumList;
    }

    /**
     * 查询枚举表
     *
     * @return
     */
    @Override
    public IPage<EnumListDTO> searchSirmEnum(EnumListDTO enumListDTO) {
        List<String> orderList = Arrays.asList("t.catalog", "t.type", "t.sort");
        Page<EnumListDTO> page = enumListDTO.buildPageWithDefaultOrders(orderList, "ASC");
        IPage<EnumListDTO> sirmEnum = this.baseMapper.searchSirmEnum(page,enumListDTO);
        List<EnumListDTO> sirmEnumList = sirmEnum.getRecords();
        assembleEnumListDTO(sirmEnumList);
        return sirmEnum;
    }

    @Override
    public List<EnumListDTO> findSirmEnumList(EnumListDTO enumListDTO) {
        List<EnumListDTO> result = baseMapper.findSirmEnumList(enumListDTO);
        assembleEnumListDTO(result);
        return result;
    }

    /**
     * 保存枚举信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSirmEnum(SirmEnum sirmEnum) {
        try {
            if (IdUtil.isDataId(sirmEnum.getObjId())) {
                SirmEnum obj = null;
                obj = this.baseMapper.selectById(sirmEnum.getObjId());
                if (obj != null) {
                    sirmEnumService.evictEnumCache(obj.getCataLog(),obj.getType(),obj.getValue());
                    sirmEnumService.evictEnumCache(sirmEnum.getCataLog(),sirmEnum.getType(),sirmEnum.getValue());
                    obj.setCataLog(sirmEnum.getCataLog());
                    obj.setDescription(sirmEnum.getDescription());
                    obj.setName(sirmEnum.getName());
                    obj.setType(sirmEnum.getType());
                    obj.setValue(sirmEnum.getValue());
                    obj.setUpdateTimeStamp(new Date());
                    obj.setSort(sirmEnum.getSort());
                    obj.setStrvalue(sirmEnum.getStrvalue());
                    saveOrUpdate(obj);
                }
            } else {
                sirmEnumService.evictEnumCache(sirmEnum.getCataLog(), sirmEnum.getType(), sirmEnum.getValue());
                saveOrUpdate(sirmEnum);
            }
        } catch (Exception e) {
            log.error("枚举保存失败{}",sirmEnum,e);
            throw new BussinessException(EnumMessageCode.ENUM_SAVE_FAILED);
        }
        return sirmEnum.getObjId();
    }


    /**
     * 删除枚举信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delSirmEnumById(Long objid) {
        SirmEnum sirmEnum = baseMapper.selectById(objid);
        if (sirmEnum != null) {
            baseMapper.deleteById(objid);
            sirmEnumService.evictEnumCache(sirmEnum.getCataLog(),sirmEnum.getType(),sirmEnum.getValue());
        }
    }

    @Cacheable(value = CacheConstant.ENUM_CACHE,key = "T(String).valueOf(#cataLog).toUpperCase() + '::' + T(String).valueOf(#type).toUpperCase()")
    @Override
    public List<SirmEnum> findSirmEnumByCatalogAndType(String cataLog, String type) {
        List<SirmEnum> resultList = this.baseMapper.findSirmEnumByCataLogAndTypeAndValue(cataLog, type, null);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        return resultList;
    }

    @Override
    public SirmEnum getSirmEnumById(Long objid) {
        return this.baseMapper.selectById(objid);
    }

    @CacheEvict(value = CacheConstant.ENUM_CACHE,key = "T(String).valueOf(#cataLog).toUpperCase() + '::' + T(String).valueOf(#type).toUpperCase() + '::' + #value")
    @Override
    public void evictEnumCacheWithCataLogAndTypeAndValue(String cataLog, String type, Integer value) {
        log.debug("清除{}缓存,Key为: {}", CacheConstant.ENUM_CACHE, cataLog.concat("::").concat(type).concat("::").concat(value.toString()));
    }

    @CacheEvict(value = CacheConstant.ENUM_CACHE, key = "T(String).valueOf(#cataLog).toUpperCase() + '::' + T(String).valueOf(#type).toUpperCase()")
    @Override
    public void evictEnumCacheWithCataLogAndType(String cataLog, String type) {
        log.debug("清除{}缓存,Key为: {}", CacheConstant.ENUM_CACHE, cataLog.concat("::").concat(type));
    }

    @Override
    public void evictEnumCache(String cataLog, String type, Integer value) {
        if (StringUtils.isNotBlank(cataLog) && StringUtils.isNotBlank(type)) {
            sirmEnumService.evictEnumCacheWithCataLogAndType(cataLog, type);
            if (value != null) {
                sirmEnumService.evictEnumCacheWithCataLogAndTypeAndValue(cataLog, type, value);
            }
        }
    }

    /**
     * 组装 EnumListDTO 中特殊的字段
     * @param sirmEnumList
     */
    private void assembleEnumListDTO(List<EnumListDTO> sirmEnumList) {
        for (EnumListDTO m : sirmEnumList) {
            if (m.getValuetype() == null) {
                m.setValuetype(0);
            } else {
                m.setValuetype(1);
            }
        }
    }

}
