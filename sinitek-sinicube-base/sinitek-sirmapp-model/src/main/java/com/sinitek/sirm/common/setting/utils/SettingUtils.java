package com.sinitek.sirm.common.setting.utils;

import com.sinitek.data.mybatis.init.SpringFactoryUtil;
import com.sinitek.sirm.common.setting.entity.SirmSetting;
import com.sinitek.sirm.common.setting.service.ISettingService;
import com.sinitek.sirm.common.support.LocalSetting;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.common.utils.ObjectUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Setting 常用查询工具类
 *
 * <AUTHOR>
 * @date 2019-12-20
 */

@Slf4j
public class SettingUtils {
    /**
     * 根据参数获取参数配置表的参数属性值
     * @param module
     * @param name
     * @return
     * 若存在，返回相关内容
     * 若不存在，返回null
     */

    public static String getStringValue(String module, String name){
        SirmSetting setting = getSetting(module, name);
        return setting == null ? null : setting.getValue();
    }

    /**
     * 判断参数配置表的是否为空
     * @param module
     * @param name
     * @param dv
     * @return
     * 若不为空，返回参数表的值
     * 若为空，返回ｄｖ
     */

    public static String getStringValue(String module, String name, String dv){
        String value = getStringValue(module, name);
        return StringUtils.isBlank(value) ? dv : value;
    }

    /**
     * 判断参数配置表中的参数属性值
     * @param module
     * @param name
     * @return
     * 若存在，返回参数属性值
     * 若不存在，返回null
     */
    public static Integer getIntegerValue(String module, String name){
        SirmSetting setting = getSetting(module, name);
        return setting == null ? null : NumberTool.safeToInteger(setting.getValue(), null);
    }

    /**
     * 判断参数配置表中的参数属性值
     * @param module
     * @param name
     * @param dv
     * @return
     * 若不为空，返回参数表参数的属性值
     * 若为空，返回ｄｖ
     */

    public static Integer getIntegerValue(String module, String name, Integer dv){
        Integer value = getIntegerValue(module, name);
        return value == null ? dv : value;
    }

    /**
     * 判断参数配置表中的参数属性值
     * @param module
     * @param name
     * @return
     * 若存在，返回参数属性值
     * 若不存在，返回null
     */

    public static Double getDoubleValue(String module, String name){
        SirmSetting setting = getSetting(module, name);
        return setting == null ? null : NumberTool.safeToDouble(setting.getValue(), null);
    }

    /**
     * 判断参数配置表中的参数属性值
     * @param module
     * @param name
     * @param dv
     * @return
     * 若不为空，返回参数表参数的属性值
     * 若为空，返回ｄｖ
     */
    public static Double getDoubleValue(String module, String name, Double dv){
        Double value = getDoubleValue(module, name);
        return value == null ? dv : value;
    }

    /**
     * 判断参数配置表中的参数属性值(日期)
     * @param module
     * @param name
     * @param format
     * @return
     * 若存在，返回参数属性值
     * 若不存在，返回null
     */
    public static Date getDateValue(String module, String name, String format){
        Date r = null;
        SirmSetting setting = getSetting(module, name);
        if (setting != null){
            try{
                r = new SimpleDateFormat(format).parse(setting.getValue());
            }catch(Exception ex){
                log.error("参数配置表中{}的参数[{}]属性值[{}]转换为日期错误, {}",setting.getModule(),setting.getName(),setting.getValue(), ex.getMessage(), ex);
            }
        }
        return r;
    }

    /**
     * 判断参数配置表中的参数属性值(日期)
     * @param module
     * @param name
     * @param format
     * @param dv
     * @return
     * 若存在，返回参数属性值
     * 若不存在，返回null
     */

    public static Date getDateValue(String module, String name, String format, Date dv){
        Date r = getDateValue(module, name, format);
        return r == null ? dv : r;
    }

    /**
     * 获取某个模块下的模块配置集合
     * @param module 模块
     * @return
     */
    public static Map<String, SirmSetting> findSettingsByModule(String module){
        List<SirmSetting> settings = getSettingService().findSettingsByModule(module);
        return ObjectUtils.toMap("name",settings);
    }

    /**
     * 获取本地配置
     * @param name 配置项名称
     * @return
     * 返回本地配置
     */
    public static String getLocalSetting(String name){
        return LocalSetting.getSetting(name);
    }

    /**
     * 获取本地配置
     * @param name 配置项名称
     * @return
     * 若存在，返回本地配置
     * 若不存在，返回null
     */
    public static String getLocalSetting(String name, String dv){
        return LocalSetting.getSetting(name, dv);
    }

    /**
     * 根据模块名获取参数配置实体
     * @param module 模块
     * @param name   参数名称
     * @return
     * 若获取成功，则返回该实体
     * 若失败，返回null
     */
    public static SirmSetting getSetting(String module, String name){
        return getSettingService().getSetting(module, name);
    }

    private static ISettingService getSettingService() {
        return SpringFactoryUtil.getBean(ISettingService.class);
    }

    public static String getTempDir(){
        String str = StringUtils.trimToNull(LocalSetting.getLocalTempDir());
        if (StringUtils.isBlank(str)){
            str = CommonSettingUtil.getTempDir();
        }
        if (StringUtils.isBlank(str)) {
            log.error("临时文件目录为空，请检查application.yml配置文件中sinicube.tempdir.path参数是否配置");
            throw new BussinessException(CommonMessageCode.EMPTY_TEMP_DIR);
        }
        return str;
    }
}

