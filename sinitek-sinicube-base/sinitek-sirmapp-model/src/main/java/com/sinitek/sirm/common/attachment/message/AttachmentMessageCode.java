package com.sinitek.sirm.common.attachment.message;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
public class AttachmentMessageCode {
    public static final String TARGET_NOT_EXIST = "14010001";

    public static final String CONTENT_FINGER_PRINT_ERROR = "14020001";

    public static final String ATTACHMENT_CONTENT_MD5_CANT_REPEAT = "14020002";
    public static final String ATTACHMENT_CONTENT_MD5_CANT_EMPTY = "14020003";

    /**
     * 保存附件失败，找不到附件{0}
     */
    public static final String ATTACHMENT_CONTENT_CANNOT_FIND = "14020004";

    /**
     * 附件【{0}】保存失败
     */
    public static final String ATTACHMENT_CONTENT_SAVE_FAILED = "14020005";
}
