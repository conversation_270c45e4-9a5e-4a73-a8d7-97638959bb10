package com.sinitek.sirm.framework.service.impl;

import com.sinitek.sirm.common.attachment.dto.UploaderResultDTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentContentService;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.encryption.algorithm.asymmetric.IAsymmetricEncryption;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.utils.*;
import com.sinitek.sirm.framework.dto.ChunkUploadDTO;
import com.sinitek.sirm.framework.dto.UploaderFileDTO;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadSearchDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.service.IUploaderService;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import com.sinitek.sirm.framework.support.MessageCode;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import com.sinitek.sirm.org.service.IOrgService;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: UploaderServiceImpl
 * @author: GeChenghao
 * @date: 2019-04-28 11:22
 *
 * move from Action by ZhangJunFeng 2022/12/16
 **/
@SuppressFBWarnings(value = "PATH_TRAVERSAL_IN",justification = "类中的路径用户输入部分已通过FilenameUtils.getName防止路径遍历")
@Slf4j
@Service
public class UploaderServiceImpl implements IUploaderService {

    private static final String UPLOADDIR = "uploaddir";
    private static final String UPLOADER = "uploader";
    private static final String ENDWITH_GB = "gb";
    private static final String ENDWITH_G = "g";
    private static final String ENDWITH_MB = "mb";
    private static final String ENDWITH_M = "m";
    private static final String ENDWITH_KB = "kb";
    private static final String ENDWITH_K = "k";
    private static final String ENDWITH_B = "b";
    private static final int SIZE_K = 1024;
    private static final int SIZE_M = 1024 * 1024;
    private static final int SIZE_G = 1024 * 1024 * 1024;
    private static final String RW = "rw";


    @Autowired
    private IAsymmetricEncryption asymmetricEncryption;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IAttachmentContentService attachmentContentService;

    @Autowired
    private IOrgService orgService;

    @SuppressFBWarnings(value = "PATH_TRAVERSAL_OUT", justification = "path参数基于后端配置生成而不是用户输入")
    @Override
    public String uploadFile(MultipartFile file, String fsize, String suffixes) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(suffixes)) {
            suffixes = asymmetricEncryption.decryptByPrivateKey(suffixes);
        }

        String fileName = file.getOriginalFilename();
        String fileExt = "";
        if (org.apache.commons.lang.StringUtils.isNotBlank(fileName)) {
            fileExt = FilenameUtils.getExtension(fileName);
        }
        String name = UUID.randomUUID().toString().replace("-", "") + "." + fileExt;
        String path = getSavePath();
        File dir = new File(path);
        if (!dir.exists()) {
            return RequestResult.getMessage(CommonMessageCode.UPLOAD_TEMP_DIR_MISSING, path);
        }
        checkFileSuffix(fileName, suffixes);
        checkFileSize(file.getSize(), fsize, fileName);

        try (InputStream is = file.getInputStream();
             OutputStream os = new FileOutputStream(getSavePath() + "//" + name, true);
        ) {
            // 将InputStream里的byte拷贝到OutputStream
            IOUtils.copy(is, os);
            os.flush();
        } catch (Exception e) {
            log.error("上传错误,file:{}",file, e);
        }
        return name;
    }

    @SuppressFBWarnings(value = "WEAK_MESSAGE_DIGEST_MD5", justification = "该场景中MD5的使用仅仅只是用户文件的唯一标识,并不涉及敏感信息")
    @Override
    public UploaderResultDTO uploadChunk(ChunkUploadDTO chunkUploadDTO) {
        ChunkUploadDTO chunk = checkChunkUploadDTO(chunkUploadDTO);
        String path = getSavePath();
        File dir = new File(path);
        if (!dir.exists()) {
            throw new BussinessException(CommonMessageCode.UPLOAD_TEMP_DIR_MISSING, path);
        }

        MultipartFile file = chunk.getFile();
        String chunkFilename = Paths.get(generatePath(getSavePath(), chunk)).toString();
        File chunkFile = new File(chunkFilename);
        try (InputStream is = file.getInputStream();
             OutputStream os = new FileOutputStream(chunkFile, false)) {
            // 将InputStream里的byte拷贝到OutputStream
            IOUtils.copy(is, os);
            os.flush();

        } catch (IOException e) {
            log.error("{}文件第{}片上传出现错误:", chunk.getFilename(), chunk.getChunkNumber(), e);
        }
        try (InputStream newFile = Files.newInputStream(chunkFile.toPath())) {
            String md5 = DigestUtils.md5Hex(newFile);
            if (org.apache.commons.lang.StringUtils.isNotBlank(md5) && !md5.equals(chunk.getChunkMd5())) {
                Files.delete(chunkFile.toPath());
                log.error(RequestResult.getMessage(CommonMessageCode.FILE_COPY_ERROR, chunk.getFilename(), chunk.getChunkNumber()));
                throw new BussinessException(CommonMessageCode.FILE_COPY_ERROR, chunk.getFilename(), chunk.getChunkNumber());
            }
        } catch (IOException e) {
            log.error("{}文件第{}片计算MD5出现错误:", chunk.getFilename(), chunk.getChunkNumber(), e);
            throw new BussinessException(CommonMessageCode.UPLOAD_ERROR, chunk.getFilename(), e);
        }
        return this.merge(chunk);
    }

    @SneakyThrows
    @Override
    public UploaderResultDTO merge(ChunkUploadDTO chunk) {
        UploaderResultDTO uploaderResultDTO = new UploaderResultDTO();
        String filename = chunk.getFilename();
        String newFilePath = Paths.get(getSavePath(), chunk.getIdentifier().replace("-", "") + "." +  FilenameUtils.getExtension(filename)).toString();
        String folder = Paths.get(getSavePath(), chunk.getIdentifier()).toString();
        File newFile = new File(newFilePath);
        //不存在的话，进行合并
        File fileFolder = new File(folder);
        File[] files = fileFolder.listFiles();

        if (files != null && files.length == chunk.getTotalChunks()) {
            try (RandomAccessFile rafWrite = new RandomAccessFile(newFile, RW)) {
                // 指针指向文件顶端
                rafWrite.seek(0);
                // 转为集合
                List<File> fileList = new ArrayList<>(Arrays.asList(files));
                // 合并需要从小到大排序
                fileList.sort((o1, o2) -> {
                    if (Integer.parseInt(o1.getName()) < Integer.parseInt(o2.getName())) {
                        return -1;
                    }
                    return 1;
                });
                // 合并文件
                for (File chunkFile : fileList) {
                    doMergeFiles(chunkFile, rafWrite);
                }
            } catch (IOException e) {
                log.error("对合并后的文件{}进行读写时出现错误:", newFile.getName(), e);
            }
            uploaderResultDTO.setUniqueName(newFile.getName());
            return uploaderResultDTO;
        }
        uploaderResultDTO.setUniqueName(chunk.getChunkNumber().toString());
        return uploaderResultDTO;
    }

    @Override
    public UploaderResultDTO checkChunk(ChunkUploadDTO chunkUploadDTO) {
        ChunkUploadDTO chunk = checkChunkUploadDTO(chunkUploadDTO);
        String folderPath = Paths.get(getSavePath(), chunk.getIdentifier()).toString();

        //判断当前哪些文件块已经上传过了，把结果告诉前端，跳过这些文件块的上传，实现断点续传
        File folder = new File(folderPath);
        File[] files = folder.listFiles();
        List<Integer> list = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                list.add(Integer.valueOf(file.getName()));
            }
        }

        if (list.size() == chunkUploadDTO.getTotalChunks()) {
            return this.merge(chunk);
        }
        UploaderResultDTO uploaderResultDTO = new UploaderResultDTO();
        uploaderResultDTO.setChunkFileNumList(list);
        return uploaderResultDTO;
    }

    @Override
    public List<UploaderFileDTO> listFiles(UploadDTO uploadto, HttpServletRequest request) {
        String accesstoken = StringUtil.safeToString(request.getHeader("accesstoken"), "");
        if (uploadto.getSourceId() == null || uploadto.getSourceEntity() == null) {
            return new ArrayList<>();
        }
        List<Attachment> list = null;
        if(uploadto.getType() != null) {
            list = attachmentService.findAttachmentList(uploadto.getSourceEntity(), uploadto.getSourceId(), uploadto.getType());
        } else {
            list = attachmentService.findAttachmentList(uploadto.getSourceEntity(), uploadto.getSourceId());
        }

        List<String> ownerIdList = list.stream().map(Attachment::getOwnerId).collect(Collectors.toList());
        Map<String, String> orgNameMap = orgService.getOrgNameMapByOrgIdList(ownerIdList);

        List<UploaderFileDTO> attachData = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (Attachment attachment : list) {
                // 当没有传type时返回所有
                if(uploadto.getType() != null && !NumberTool.safeToInteger(attachment.getType(), 0).equals(uploadto.getType())){
                    continue;
                }

                UploaderFileDTO uploaderFileDTO = this.toUploaderFileDTO(attachment);
                uploaderFileDTO.setAccesstoken(accesstoken);

                String ownerId = attachment.getOwnerId();
                if (StringUtils.isNotBlank(ownerId)) {
                    String ownerName = orgNameMap.get(ownerId);
                    uploaderFileDTO.setOwnerName(ownerName);
                }

                attachData.add(uploaderFileDTO);
            }
        }
        return attachData;
    }

    @Override
    public String getAvatarBase64(UploadDTO uploadto) {
        if (uploadto.getSourceId() == null || uploadto.getSourceEntity() == null) {
            return null;
        }
        String imgData = "";
        List<Attachment> attachments = attachmentService.findAttachmentList(uploadto.getSourceEntity(), uploadto.getSourceId(), uploadto.getType());
        if (CollectionUtils.isNotEmpty(attachments)) {
            Attachment attachment = attachments.get(0);
            try (InputStream fis = AttachmentUtils.getAttachStream(attachment);
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                if (fis != null) {
                    IOUtils.copy(fis,byteArrayOutputStream);
                    // 加密
                    imgData = Base64.encodeBase64String(byteArrayOutputStream.toByteArray());
                }
            } catch (IOException e) {
                log.error("获取用户图象信息错误，attachment:{}",attachment, e);
                return null;
            }
        }
        if (StringUtils.isNotBlank(imgData)) {
            return "data:image/png;base64," + imgData;
        } else {
            return imgData;
        }
    }

    public void checkFileSuffix(String filename, String suffixes) {
        List<String> systemUploadFileMimeTypes = CommonSettingUtil.getUploadFileMimeTypes();
        String suffix = FilenameUtils.getExtension(filename);

        if (StringUtils.isBlank(filename)) {
            throw new BussinessException(MessageCode.ATTACH_UNSUPPORT_SUFFIX, suffix);
        }

        // 1. 如果文件名为jpg且没有后缀，那么就会报错
        // 2. 如果suffixList不包含该文件的后缀，报错
        // 判断前端是否支持该类型
        if (filename.equals(suffix)
                || (org.apache.commons.lang.StringUtils.isNotBlank(suffixes)
                && (!(Arrays.asList(suffixes.split(",")).contains(suffix.toLowerCase()))))) {
            throw new BussinessException(MessageCode.ATTACH_FRONTEND_UNSUPPORT_SUFFIX, suffix);
        }

        // 判断后端端是否支持该类型
        // 如果系统配置不包含该文件的后缀，报错
        if (CollectionUtils.isEmpty(systemUploadFileMimeTypes)) {
            throw new BussinessException(MessageCode.ATTACH_SYS_UNSUPPORT_EMPTY);
        } else if (!systemUploadFileMimeTypes.contains(suffix.toLowerCase())){
            throw new BussinessException(MessageCode.ATTACH_BACKEND_UNSUPPORT_SUFFIX, suffix);
        }
    }

    public void checkFileSize(long size, String fileSize, String filename) {
        long maxSize = CommonSettingUtil.getAttachMaxSize();
        long aLong = str2long(fileSize);

        if (aLong > 0 && size > aLong) {
            throw new BussinessException(MessageCode.ATTACH_OUT_MAXSIZE, filename, formatSize(String.valueOf(aLong)));
        } else if (aLong == 0 && maxSize > 0 && size > maxSize) {
            throw new BussinessException(MessageCode.ATTACH_OUT_MAXSIZE, filename, formatSize(String.valueOf(maxSize)));
        }
    }

    @SuppressFBWarnings(value = "PATH_TRAVERSAL_IN", justification = "方法中的文件路径来自于后端而非用户输入")
    @Override
    public String getTempSaveDir() {
        return Paths.get(SettingUtils.getTempDir(), UPLOADER, UPLOADDIR).toString();
    }

    @Override
    public ChunkUploadDTO checkChunkUploadDTO(ChunkUploadDTO chunk) {
        String suffixes = chunk.getSuffixes();
        if (org.apache.commons.lang.StringUtils.isNotBlank(suffixes)) {
            suffixes = asymmetricEncryption.decryptByPrivateKey(suffixes);
            chunk.setSuffixes(suffixes);
        }

        String filename = chunk.getFilename();
        checkFileSuffix(filename, suffixes);
        checkFileSize(NumberTool.safeToLong(chunk.getTotalSize(), 0L), chunk.getSize(), filename);

        return chunk;
    }

    @SneakyThrows
    @Override
    public String countMd5(InputStream inputStream) {
        return EncryptUtil.getFileMD5(inputStream);
    }

    @Override
    public String checkUploadFile(MultipartFile file, String fileSize, String suffixes) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(suffixes)) {
            suffixes = asymmetricEncryption.decryptByPrivateKey(suffixes);
        }

        String fileName = file.getOriginalFilename();
        String fileExt = "";
        if (org.apache.commons.lang.StringUtils.isNotBlank(fileName)) {
            fileExt = FilenameUtils.getExtension(fileName);
        }
        String name = UUID.randomUUID().toString().replace("-", "") + "." + fileExt;
        checkFileSuffix(fileName, suffixes);
        checkFileSize(file.getSize(), fileSize, fileName);
        return name;
    }

    @Override
    public List<UploaderFileDTO> listFilesByMultiSource(List<UploadSearchDTO> searchList) {
        if (CollectionUtils.isEmpty(searchList)) {
            return new ArrayList<>();
        }
        List<Attachment> attachmentList = attachmentService.findAttachmentListByMultiSource(searchList);
        if (CollectionUtils.isEmpty(attachmentList)) {
            return new ArrayList<>();
        }
        List<UploaderFileDTO> resultList = new ArrayList<>();
        List<Long> contentIdList = attachmentList.stream().map(Attachment::getContentId).collect(Collectors.toList());
        Map<Long, String> attachmentContentMd5Map = attachmentContentService.getAttachmentContentMd5(contentIdList);

        List<String> ownerIdList = attachmentList.stream().map(Attachment::getOwnerId).collect(Collectors.toList());
        Map<String, String> orgNameMap = orgService.getOrgNameMapByOrgIdList(ownerIdList);

        for (Attachment attachment : attachmentList) {
            Long contentId = attachment.getContentId();
            String md5 = MapUtils.getString(attachmentContentMd5Map, contentId);

            UploaderFileDTO uploaderFileDTO = this.toUploaderFileDTO(attachment);
            uploaderFileDTO.setMd5(md5);

            String ownerId = attachment.getOwnerId();
            if (StringUtils.isNotBlank(ownerId)) {
                String ownerName = orgNameMap.get(ownerId);
                uploaderFileDTO.setOwnerName(ownerName);
            }

            resultList.add(uploaderFileDTO);
        }

        return resultList;
    }

    /**
     * attachment 转换为 UploaderFileDTO
     * @param attachment
     * @return
     */
    private UploaderFileDTO toUploaderFileDTO(Attachment attachment) {
        String filename = StringUtils.isBlank(attachment.getFileType()) ? attachment.getName() : attachment.getName() + "." + attachment.getFileType();
        UploaderFileDTO uploaderFileDTO = new UploaderFileDTO();
        uploaderFileDTO.setId(IdEncryptUtil.encrypt(attachment.getObjId() + ""));
        uploaderFileDTO.setName(filename);
        uploaderFileDTO.setType(attachment.getType());
        uploaderFileDTO.setSize(attachment.getContentSize());

        uploaderFileDTO.setOwnerId(attachment.getOwnerId());
        uploaderFileDTO.setCreateTime(attachment.getCreateTimeStamp());

        return uploaderFileDTO;
    }

    private long str2long(String maxSizeStr){
        long maxSize = 0;
        if(org.apache.commons.lang.StringUtils.isNotBlank(maxSizeStr)) {
            maxSizeStr = maxSizeStr.trim().toLowerCase();
            try {
                if (maxSizeStr.endsWith(ENDWITH_GB) || maxSizeStr.endsWith(ENDWITH_G)) {
                    maxSize = NumberTool.stringToBigDecimal(maxSizeStr.replace("gb", "").replace("g", "")).multiply(new BigDecimal(SIZE_K * SIZE_K * SIZE_K)).longValue();
                } else if (maxSizeStr.endsWith(ENDWITH_MB) || maxSizeStr.endsWith(ENDWITH_M)) {
                    maxSize = NumberTool.stringToBigDecimal(maxSizeStr.replace("mb", "").replace("m", "")).multiply(new BigDecimal(SIZE_K * SIZE_K)).longValue();
                } else if (maxSizeStr.endsWith(ENDWITH_KB) || maxSizeStr.endsWith(ENDWITH_K)) {
                    maxSize = NumberTool.stringToBigDecimal(maxSizeStr.replace("kb", "").replace("k", "")).multiply(new BigDecimal(SIZE_K)).longValue();
                } else {
                    maxSize = NumberTool.stringToBigDecimal(maxSizeStr.replace("b", "")).longValue();
                }
            } catch (Exception e) {
                log.error("附件大小参数转换失败,maxSizeStr:{}",maxSizeStr, e);
            }
        }
        return maxSize;
    }

    /**
     * 获得临时文件路径
     * @return  文件路径
     */
    private String getSavePath() {
        // 将相对路径转换成绝对路径
        String tempDir = getTempSaveDir();
        TempDirUtils.createTempDir(tempDir);
        return tempDir;
    }

    private String formatSize(String size) {
        String strSize = "";
        if (org.apache.commons.lang.StringUtils.isNotBlank(size)) {
            size = size.trim().toLowerCase();
            if (size.contains(ENDWITH_B) || size.contains(ENDWITH_K) || size.contains(ENDWITH_M) || size.contains(ENDWITH_G)) {
                strSize = size;
            } else {
                long longSize = Long.parseLong(size);
                if (longSize >= SIZE_G) {
                    strSize = longSize / SIZE_G + "GB";
                } else if (longSize >= SIZE_M) {
                    strSize = longSize / SIZE_M + "MB";
                } else if (longSize >= SIZE_K) {
                    strSize = longSize / SIZE_K + "KB";
                } else if (longSize >= 0) {
                    strSize = longSize + "B";
                }
            }
        }

        return strSize;
    }

    /**
     * 获得分片文件临时路径
     * @param uploadFolder 分片文件所在目录
     * @param chunk 分片信息
     * @return 返回分片文件临时路径
     */
    private String generatePath(String uploadFolder, ChunkUploadDTO chunk) {
        String identifier = chunk.getIdentifier();
        String safeIdentifier = FileUtil.getSafeFileName(identifier);
        String folderPath = Paths.get(uploadFolder, safeIdentifier).toString();
        // 判断目录是否存在，不存在则创建
        File folder = new File(folderPath);
        if (!folder.exists()) {
            folder.mkdir();
        }

        String chunkNumber = chunk.getChunkNumber().toString();
        String safeFileName = FileUtil.getSafeFileName(chunkNumber);
        return Paths.get(folderPath, safeFileName).toString();
    }

    /**
     * 合并文件
     * @param chunkFile 分片文件
     * @param rafWrite 写入流
     * @throws IOException 合并异常
     */
    private void doMergeFiles (File chunkFile, RandomAccessFile rafWrite) throws IOException {
        // 缓冲区
        byte[] b = new byte[SIZE_M];
        try (RandomAccessFile rafRead = new RandomAccessFile(chunkFile, RW)) {
            int len;
            while ((len = rafRead.read(b)) != -1) {
                rafWrite.write(b, 0, len);
            }
        } catch (IOException e) {
            log.error("文件合并时对第{}块文件读写出现错误:", chunkFile.getName(), e);
            throw e;
        }
    }
}
