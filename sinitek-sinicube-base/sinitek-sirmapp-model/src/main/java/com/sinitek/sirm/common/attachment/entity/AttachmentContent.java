package com.sinitek.sirm.common.attachment.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.data.mybatis.init.SpringFactoryUtil;
import com.sinitek.data.mybatis.typehandlers.SiniCubeInputStreamTypeHandler;
import com.sinitek.sirm.common.attachment.support.AttachmentSupport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "附件内容")
@TableName(value = "SIRM_ATTACHMENT_CONTENT")
@EqualsAndHashCode(callSuper = true)
public class AttachmentContent extends BaseEntity {

    public static final String ENTITY_NAME = "ATTACHMENT_CONTENT";

    @Schema(description = "附件类型")
    @TableField(value = "type")
    private Integer type;

    @Schema(description = "附件名称")
    @TableField(value = "name")
    private String name;

    @Schema(description = "附件大小")
    @TableField(value = "contentsize")
    private Long contentSize;

    @Schema(description = "存储方式")
    @TableField(value = "storetype")
    private Integer storeType;

    @Schema(description = "文档存储路径")
    @TableField(value = "storepath")
    private String storePath;

    @Schema(description = "文件类型")
    @TableField(value = "filetype")
    private String fileType;

    @Schema(description = "附件内容")
    @TableField(typeHandler = SiniCubeInputStreamTypeHandler.class)
    private InputStream content;

    @Schema(description = "加密秘钥")
    @TableField(value = "enckey")
    private String encKey;

    @Schema(description = "加密所用的算法")
    @TableField(value = "enc_algorithm")
    private String encAlgorithm;

    @Schema(description = "依赖次数")
    @TableField(value = "ref_count")
    private Integer refCount;

    @Schema(description = "验证标识")
    @TableField(value = "md5")
    private String MD5;

    public InputStream getContent() {
        if (content != null) {
            return content;
        }
        if (StringUtils.isEmpty(this.getId())) {
            return content;
        }
        AttachmentSupport attachmentSupport = SpringFactoryUtil.getBean(AttachmentSupport.class);
        if (attachmentSupport != null) {
            content = attachmentSupport.getContent(this);
            return content;
        }
        return content;
    }

    public void closeContentStream() {
        try {
            if (this.content != null){
                this.content.close();
                this.setContent(null);
            }
        } catch (IOException e) {
            log.error("关闭附件流失败,name:{}",this.name, e);
        }
    }

}
