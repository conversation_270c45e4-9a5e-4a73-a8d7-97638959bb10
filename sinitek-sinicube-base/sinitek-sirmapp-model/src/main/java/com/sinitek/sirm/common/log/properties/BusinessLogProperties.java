package com.sinitek.sirm.common.log.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/28
 */
@Data
@Component
@Schema(description = "业务日志相关参数配置")
@ConfigurationProperties(prefix = "sinicube.business-log")
public class BusinessLogProperties {

    private String logFileRootPath = "" ;
}
