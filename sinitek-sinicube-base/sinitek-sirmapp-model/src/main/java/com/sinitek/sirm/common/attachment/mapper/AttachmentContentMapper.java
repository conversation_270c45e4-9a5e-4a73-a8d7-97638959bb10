package com.sinitek.sirm.common.attachment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import org.apache.ibatis.annotations.Param;

/**
 * (SirmAttachmentContent)表数据库访问层
 *
 * <AUTHOR>
 * @date 2022-09-01 10:23:11
 */
public interface AttachmentContentMapper extends BaseMapper<AttachmentContent> {

    /**
     * 根据id获取附件内容流
     *  - 每次都将取最新,不受一级、二级缓存影响
     * @param id
     * @return
     */
    AttachmentContent getAttachmentContentAsInputStreamById(@Param("id") Long id);

}
