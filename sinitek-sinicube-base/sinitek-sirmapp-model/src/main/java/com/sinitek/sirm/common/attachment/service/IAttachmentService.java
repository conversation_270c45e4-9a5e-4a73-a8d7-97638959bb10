package com.sinitek.sirm.common.attachment.service;

import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;
import com.sinitek.sirm.framework.frontend.dto.UploadSearchDTO;

import java.io.InputStream;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * (SirmAttachment)表Service
 *
 * <AUTHOR>
 * @date 2019-12-17 14:16:09
 */
public interface IAttachmentService {

    /**
     * 保存附件
     * @param attachment
     */
    public void saveAttachment(Attachment attachment);

    /**
     * 保存附件
     * @param attachment
     */
    public void saveAttachment(Attachment attachment,InputStream inputStream);

    /**
     * 批量保存附件
     * @param sourceId  关联业务编号
     * @param sourceEntity 所属实体名称
     * @param attachmentList 批量附件
     */
    public void saveAttachmentList(Long sourceId, String sourceEntity, List<Attachment> attachmentList);

    /**
     * 查询附件
     * @param sourceEntity 实体名称
     * @param sourceId     关联业务编号
     * @return
     * 返回附件列表
     */
    public List<Attachment> findAttachmentList(String sourceEntity, Long sourceId);

    /**
     * 查询附件
     *
     * @param sourceEntity 实体名称
     * @param sourceId  关联业务编号
     * @param type 附件类型
     * @deprecated 该方法只能取到一个对象，而不是一个list，请替换为{@link #getAttachment(String sourceEntity, Long sourceId, int type)}
     * @return
     * 返回附件列表
     *
     */
    public List<Attachment> findAttachmentList(String sourceEntity, Long sourceId, int type);

    /**
     * 查询附件
     *
     * @param sourceEntity 实体名称
     * @param sourceId  关联业务编号
     * @param type 附件类型
     * @return
     * 返回附件列表
     *
     */
    public Attachment getAttachment(String sourceEntity, Long sourceId, int type);

    /**
     * 查询附件内容（大数据）
     * @param id
     * @return
     * 若成功获取到，则返回附件内容
     * 若没有获取成功，抛出异常
     */
    public InputStream getAttachmentAsInputStreamById(Long id) throws SQLException;

    /**
     *
     * @param id
     * @return
     */
    public InputStream getAttachmentAsInputStreamByIdInDb(Long id);

    /**
     * 根据id获取附件
     * @param id
     * @return
     * 若存在，返回附件内容
     * 若不存在，返回null
     */
    public Attachment getAttachmentById(Long id);

    /**
     * 根据idList获取附件列表
     * @param idList
     * @return
     */
    public List<Attachment> findAttachmentList(List<Long> idList);

    /**
     * 删除附件信息
     * @param id  objid
     */
    public void removeAttachment(Long id);

    /**
     * 删除附件信息
     * @param attachment
     */
    public void removeAttachment(Attachment attachment);

     /**
     * 批量删除附件
     * @param ids  Attachment 附件的objid 字符集合，以逗号分割
     * @param sourceEntity 实体
     * @param sourceId     关联业务id
     * @return
     * 返回剩下附件个数
     */
    public int removeAttachmentList(String ids, String sourceEntity, Long sourceId);

     /**
     * 批量删除附件
     * @param ids Attachment 附件的objid 字符集合
     */
    public void removeAttachmentList(String[] ids);

    /**
     *
     * @param idList
     */
    public void removeAttachmentList(List<Long> idList);

     /**
     * 查询实体业务对应附件的个数
     * @param sourceEntity 实体名称
     * @param sourceId     关联业务编号
     * @return
     * 若存在，返回附件对应的个数
     * 若不存在，返回０
     */
    public int getAttachmentCount(String sourceEntity, Long sourceId);

    /**
     * 根据实体名称，实体id和文件类型删除附件信息
     * @param sourceEntity 实体名称
     * @param sourceId　实体ＩＤ
     * @param fileType　文件类型
     */
    public void removeAttachment(String sourceEntity, Long sourceId, String fileType);

    /**
     * 根据实体名称，实体id和删除附件信息
     * @param sourceEntity 实体名称
     * @param sourceId　实体ＩＤ
     */
    public void removeAttachment(String sourceEntity, Long sourceId);

    /**
     * 根据实体名称,实体id和文件类型得到附件信息
     * @param sourceEntity 实体名称
     * @param sourceId 实体ID
     * @param fileType 文件类型
     * @return
     * 若存在，返回相应的附件
     * 若不存在，返回null
     */
    public Attachment getAttachment(String sourceEntity, Long sourceId, String fileType);

    /**
     * 根据实体名称、实体Id、实体type删除附件信息
     * @param sourceEntity
     * @param sourceId
     * @param type
     */
    public void removeAttachmentList(String sourceEntity, Long sourceId, int type);

    /**
     * 更新 attachment
     * @param attachment
     */
    public void updateAttachment(Attachment attachment);

    /**
     * @param sourceIdList 实体id集合
     * @param sourceEntity 实体名称
     * @return key：sourceId(实体id)，value：count(附件数量)
     */
    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    Map<Long, Integer> getAttachmentCountBySourceIdListAndSourceEntity(List<Long> sourceIdList, String sourceEntity);

    /**
     *
     * @param sourceEntity  实体名称
     * @param type          类型,-1:数据库为空
     * @param sourceIdList  实体id集合
     * @return              附件
     */
    List<Attachment> findAttachments(String sourceEntity, Integer type, List<Long> sourceIdList);

    /**
     * 快速拷贝
     * 将源附件的信息直接和新业务id关联
     * @param origId    源附件id
     * @param targetSourceName 新业务名
     * @param targetSourceId    新业务id
     * @return 保存新附件的id
     */
    public Long copyAttachment(Long origId,String targetSourceName, Long targetSourceId);

    /**
     * 批量快速拷贝
     * 将源附件的信息直接和新业务id关联
     * @param origIds    批量源附件id
     * @param targetSourceName 新业务名
     * @param targetSourceId    新业务id
     * @return 保存新附件的id
     */
    public List<Long> copyAttachment(List<Long> origIds,String targetSourceName, Long targetSourceId);

    /**
     * 根据 id、sourceEntity、sourceId删除附件
     *
     * @param id
     * @param sourceEntity
     * @param sourceId
     */
    void removeAttachment(Long id,String sourceEntity, Long sourceId);


    /**
     * 检查文件是否存在，不取附件正文
     * @param objId
     * @return
     */
    boolean checkAttachmentExist(Long objId);

    /**
     * 根据searchList获取附件列表
     * @param searchList
     * @return
     */
    List<Attachment> findAttachmentListByMultiSource(List<UploadSearchDTO> searchList);
}
