package com.sinitek.sirm.common.setting.enumerate;

/**
 * 权限防扩大枚举类
 *
 * <AUTHOR>
 * date 2023/8/2
 */
public enum PreventPermissionExpansionTypeEnum {

    /**
     * 开启状态
     */
    OPEN("0", "开启状态"),

    /**
     * 关闭状态
     */
    CLOSE("1","关闭状态");

    private String code;

    private String desc;

    PreventPermissionExpansionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
