package com.sinitek.sirm.common.setting.service;

import com.sinitek.sirm.common.setting.entity.SirmGroupsetting;

import java.util.List;

/**
 * (SirmGroupsetting)表服务接口
 *
 * <AUTHOR>
 * @date 2019-12-02 13:38:48
 */
public interface ISirmGroupsettingService {
    /**
     * 根据参数编码查询参数组
     * @param catalogCode
     * @return
     * 返回对应的参数组
     */
    public List<SirmGroupsetting> findGroupSettingByCode(String catalogCode);

    /**
     * 查询所有参数组信息
     * @return
     * 返回所有的参数组信息
     */
    public List<SirmGroupsetting> findGroupSettingList();

    /**
     * 查询所有权限对象表
     * @return
     * 根据顺序升序排列显示权限对象表
     */
    public List<SirmGroupsetting> findGroupSettings();

    /**
     * 更新权限报表的排序
     * @param groupSettings
     */
    public void updateGroupSettings(List<SirmGroupsetting> groupSettings);

    /**
     * 参数组插入
     * @param groupSetting　参数组配置信息
     */
    public void insertGroupSetting(SirmGroupsetting groupSetting);

    /**
     * 根据id获取 GroupSetting
     * @param id
     */
    SirmGroupsetting getGroupSettingById(Long id);

}
