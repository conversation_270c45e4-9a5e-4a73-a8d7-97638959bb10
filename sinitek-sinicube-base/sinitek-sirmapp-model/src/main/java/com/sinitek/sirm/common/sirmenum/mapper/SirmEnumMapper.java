package com.sinitek.sirm.common.sirmenum.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.common.sirmenum.dto.EnumListDTO;
import com.sinitek.sirm.common.sirmenum.dto.SirmEnumSearchDTO;
import com.sinitek.sirm.common.sirmenum.entity.SirmEnum;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * (SirmEnum)表数据库访问层
 *
 * <AUTHOR>
 * @date 2019-12-11 19:04:14
 */
public interface SirmEnumMapper extends BaseMapper<SirmEnum> {

    /**
     * 查询枚举
     *
     * @return
     */
    public IPage<EnumListDTO> searchSirmEnum(Page<EnumListDTO> page, @Param("param") EnumListDTO enumListDTO);

    /**
     * 查询枚举列表
     * @param enumListDTO
     * @return
     */
    public List<EnumListDTO> findSirmEnumList(@Param("param") EnumListDTO enumListDTO);

    /**
     * 通过SirmEnumSearchDTO集合查找SirmEnum
     * @param queryList
     * @return
     */
    public List<SirmEnum> findSirmEnumByList(List<SirmEnumSearchDTO> queryList);

    /**
     * 通过cataLog、type、value查找SirmEnum
     * @param cataLog
     * @param type
     * @param value
     * @return
     */
    public List<SirmEnum> findSirmEnumByCataLogAndTypeAndValue(@Param("cataLog")String cataLog,@Param("type") String type,@Param("value") Integer value);
}
