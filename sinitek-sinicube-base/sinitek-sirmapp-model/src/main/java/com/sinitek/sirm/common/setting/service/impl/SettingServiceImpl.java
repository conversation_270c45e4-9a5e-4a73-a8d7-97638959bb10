package com.sinitek.sirm.common.setting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.common.constant.CacheConstant;
import com.sinitek.sirm.common.encryption.algorithm.symmetry.ISymmetryEncryption;
import com.sinitek.sirm.common.setting.constant.CommonSettingConstant;
import com.sinitek.sirm.common.setting.entity.SirmSetting;
import com.sinitek.sirm.common.setting.mapper.SirmSettingMapper;
import com.sinitek.sirm.common.setting.message.SettingMessageCode;
import com.sinitek.sirm.common.setting.service.ISettingService;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 作者: terry
 * 日期: 11-4-25
 * 时间: 下午4:59
 * 描述:
 */
@Slf4j
@Service
public class SettingServiceImpl
        extends ServiceImpl<SirmSettingMapper, SirmSetting>
        implements ISettingService {

    @Autowired
    private SirmSettingMapper sirmSettingMapper;

    @Autowired
    private ISymmetryEncryption symmetryEncryption;

    @Autowired
    private ISettingService self;

    //密码加密
    private static final String ENCRY_STRINNG = ";PASSWORD;MAIL_SMTPPWD;";


    /**
     * 根据模块名获取参数配置实体
     *
     * @param module 模块
     * @param name   参数名称
     * @return
     */
    @Cacheable(value = CacheConstant.SETTING_CACHE,key = "#module + '::' + #name")
    @Override
    public SirmSetting getSetting(String module, String name) {
        List<SirmSetting> settings = sirmSettingMapper.selectList(new QueryWrapper<SirmSetting>()
                .lambda()
                .eq(SirmSetting::getModule, module)
                .eq(SirmSetting::getName, name));

        SirmSetting setting = null;
        if(!settings.isEmpty()){
            setting = settings.get(0);
            // 解密
            if(setting != null && setting.getEncryptionFlag() != null && setting.getEncryptionFlag() == 1){
                setting.setValue(symmetryEncryption.decrypt(setting.getValue()));
            }
        }
        return setting;
    }

    @Override
    public List<SirmSetting> findAllSettings() {
        List<SirmSetting> settings = this.baseMapper.selectList(new QueryWrapper<>());
        for(SirmSetting setting : settings){
            // 解密
            if(setting.getEncryptionFlag() != null && setting.getEncryptionFlag() == 1){
                setting.setValue(symmetryEncryption.decrypt(setting.getValue()));
            }
        }
        return settings;
    }

    @Override
    public List<SirmSetting> findSettingsByModule(String module) {
        List<SirmSetting> settings = sirmSettingMapper.selectList(new QueryWrapper<SirmSetting>()
                .lambda()
                .eq(SirmSetting::getModule, module));

        for(SirmSetting setting : settings){
            // 解密
            if(setting.getEncryptionFlag() != null && setting.getEncryptionFlag() == 1){
                setting.setValue(symmetryEncryption.decrypt(setting.getValue()));
            }
        }
        return settings;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveSetting(SirmSetting setting) {
        return saveSeting(setting);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateBatchSettingList(List<SirmSetting> sirmSettingList) {
        if (sirmSettingList == null || sirmSettingList.isEmpty()) {
            return;
        }

        sirmSettingList.forEach(obj -> {
            if(ENCRY_STRINNG.contains(CommonSettingConstant.SEMICOLON + obj.getName() + CommonSettingConstant.SEMICOLON)){
                obj.setValue(symmetryEncryption.encrypt(obj.getValue()));
                obj.setEncryptionFlag(1);
            }
        });
        saveOrUpdateBatch(sirmSettingList);
        for (SirmSetting sirmSetting : sirmSettingList) {
            self.evictSetting(sirmSetting.getModule(),sirmSetting.getName());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteSetting(SirmSetting setting) {
        removeById(setting.getObjId());
        self.evictSetting(setting.getModule(),setting.getName());
    }

    /**
     * 保存配置参数
     *
     * @param setting
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveSeting(SirmSetting setting) {
        if(StringUtils.isBlank(setting.getModule()) || StringUtils.isBlank(setting.getName())){
            throw new BussinessException(SettingMessageCode.MODULE_AND_NAME_CAN_NOT_NULL);
        }
        LambdaQueryWrapper<SirmSetting> queryWrapper = new LambdaQueryWrapper<>();
        if (setting.getObjId() != null && 0 != setting.getObjId()) {
            queryWrapper.ne(SirmSetting::getObjId, setting.getObjId());
        }
        queryWrapper.eq(StringUtils.isNotBlank(setting.getModule()), SirmSetting::getModule, setting.getModule());
        queryWrapper.eq(StringUtils.isNotBlank(setting.getName()), SirmSetting::getName, setting.getName());
        if (sirmSettingMapper.selectOne(queryWrapper) != null) {
            throw new BussinessException(SettingMessageCode.PARMS_CAN_NOT_REPEATED);
        }
        if (setting.getObjId() != null && 0 != setting.getObjId()) {
            SirmSetting obj = null;
            try {
                obj = sirmSettingMapper.selectOne(new QueryWrapper<SirmSetting>()
                        .lambda()
                        .eq(SirmSetting::getObjId, setting.getObjId()));
                if (obj != null) {
                    self.evictSetting(obj.getModule(),obj.getName());

                    String value = setting.getValue();
                    int encryFlag = 0;
                    if(ENCRY_STRINNG.contains(CommonSettingConstant.SEMICOLON + obj.getName() + CommonSettingConstant.SEMICOLON)){
                        value = symmetryEncryption.encrypt(setting.getValue());
                        encryFlag = 1;
                    }
                    obj.setValue(value);
                    obj.setEncryptionFlag(encryFlag);
                    obj.setModule(setting.getModule());
                    obj.setName(setting.getName());
                    obj.setBrief(setting.getBrief());
                    obj.setUpdateTimeStamp(new Date());
                    saveOrUpdate(obj);
                }
            } catch (Exception e) {
                log.error("保存配置参数异常,SirmSetting:{}",setting,e);
                throw new BussinessException(CommonMessageCode.SAVE_FAIL);
            }
        } else {
            self.evictSetting(setting.getModule(),setting.getName());
            saveOrUpdate(setting);
        }
        return setting.getObjId();
    }

    /**
     * 删除配置参数
     *
     * @param objid
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delSettingByObjid(String objid) {
        SirmSetting obj = sirmSettingMapper.selectOne(new QueryWrapper<SirmSetting>()
                .lambda()
                .eq(SirmSetting::getObjId, NumberTool.safeToLong(objid,0L)));
        if (obj != null) {
            deleteSetting(obj);
        }
    }

    /**
     * 查询参数设置表的模块
     *
     * @return
     */
    @Override
    public List<String> findSettingModule() {
        return sirmSettingMapper.findSettingModule();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveSetting(String module, String name, String value) {
        SirmSetting setting = getSetting(module, name);
        if (setting != null) {
            setting.setValue(value);
        } else {
            setting = new SirmSetting();
            setting.setModule(module);
            setting.setName(name);
            setting.setValue(value);
        }
        saveSeting(setting);

        return setting.getObjId();
    }

    @Override
    public SirmSetting getSettingById(Long objid) {
        SirmSetting obj = sirmSettingMapper.selectOne(new QueryWrapper<SirmSetting>()
                .lambda()
                .eq(SirmSetting::getObjId, objid));
        // 解密
        if(obj != null && obj.getEncryptionFlag() != null && obj.getEncryptionFlag() == 1){
            obj.setValue(symmetryEncryption.decrypt(obj.getValue()));
        }
        return obj;
    }

    @CacheEvict(value = CacheConstant.SETTING_CACHE,key = "#module + '::' + #name")
    @Override
    public void evictSetting(String module, String name) {
        log.debug("清除{}缓存,Key为: {}", CacheConstant.SETTING_CACHE, module.concat("::").concat(name));
    }
}
