<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.common.attachment.mapper.AttachmentContentMapper">

    <select id="getAttachmentContentAsInputStreamById"
            resultType="com.sinitek.sirm.common.attachment.entity.AttachmentContent" flushCache="true">
        select sa.content from sirm_attachment_content sa where sa.id = #{id}
    </select>
</mapper>
