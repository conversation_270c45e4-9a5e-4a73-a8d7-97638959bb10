package com.sinitek.sirm.common.attachment.listener;

import com.sinitek.sirm.common.attachment.annotation.AttachmentStore;
import com.sinitek.sirm.common.attachment.dto.AttachmentStoreInfoDTO;
import com.sinitek.sirm.common.attachment.support.IAttachmentCustomStore;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 附件信息监听
 *
 * <AUTHOR>
 * @date 2022/01/17
 */
@Slf4j
@Component
public class AttachmentListener {

    private Map<String, AttachmentStoreInfoDTO> attachmentStoreInfoMap = new HashMap<>();

    private Map<String, IAttachmentCustomStore> attachmentCustomStoreMap = new HashMap<>();

    private Map<Integer, String> storeTypeMap = new HashMap<>();

    /**
     * 启动时 初始化附件注解信息
     * @param event         服务启动事件
     */
    @Order(value = Ordered.HIGHEST_PRECEDENCE)
    @EventListener
    public void onSpringReady(ApplicationReadyEvent event){
        if (event.getApplicationContext().getParent() == null
                || Objects.requireNonNull(event.getApplicationContext().getParent()).getParent() == null) {
            Map<String, IAttachmentCustomStore> customStoreMap = event.getApplicationContext().getBeansOfType(IAttachmentCustomStore.class);

            for (String name : customStoreMap.keySet()) {
                IAttachmentCustomStore attachmentCustomStore = customStoreMap.get(name);

                AttachmentStore attachmentStore = attachmentCustomStore.getClass().getAnnotation(AttachmentStore.class);
                if (attachmentStore == null) {
                    continue;
                }
                String storeName = attachmentStore.storeName();
                String storeCode = attachmentStore.storeCode();
                int storeType = attachmentStore.storeType();
                int sort = attachmentStore.sort();

                AttachmentStoreInfoDTO attachmentStoreInfoDTO = new AttachmentStoreInfoDTO();
                attachmentStoreInfoDTO.setStoreName(storeName);
                attachmentStoreInfoDTO.setStoreCode(storeCode);
                attachmentStoreInfoDTO.setStoreType(storeType);
                attachmentStoreInfoDTO.setSort(sort);
                attachmentStoreInfoMap.put(storeCode, attachmentStoreInfoDTO);
                attachmentCustomStoreMap.put(storeCode, attachmentCustomStore);
                storeTypeMap.put(storeType, storeCode);
            }
        }
    }

    /**
     * 获取 AttachmentStoreInfoDTO List
     * @return
     */
    public List<AttachmentStoreInfoDTO> findAttachmentStoreInfoList() {
        List<AttachmentStoreInfoDTO> resultList = new ArrayList<>();
        for (String storeCode : attachmentStoreInfoMap.keySet()) {
            resultList.add(attachmentStoreInfoMap.get(storeCode));
        }
        return resultList.stream().sorted(Comparator.comparing(AttachmentStoreInfoDTO::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 根据 storeCode 获取 IAttachmentCustomStore
     * @param storeCode
     * @return
     */
    public IAttachmentCustomStore getAttachmentCustomStore(String storeCode) {
        return attachmentCustomStoreMap.get(storeCode);
    }

    /**
     * 根据 storeType 获取 StoreCode
     * @param storeType
     * @return
     */
    public String getStoreCode(int storeType) {
        return storeTypeMap.get(storeType);
    }

    /**
     * 根据 StoreCode 获取 IntStoreType
     * @param storeCode
     * @return
     */
    public Integer getIntStoreType(String storeCode) {
        for (Integer storeType : storeTypeMap.keySet()) {
            String storeCodeValue = storeTypeMap.get(storeType);
            if (StringUtils.isNotBlank(storeCodeValue) && storeCodeValue.equals(storeCode)) {
                return storeType;
            }
        }
        return null;
    }

    /**
     * 获取系统当前模式的 IAttachmentCustomStore
     * @return
     */
    public IAttachmentCustomStore getCurrentAttachmentCustomStore() {
        String attachStoreType = CommonSettingUtil.getAttachStoreType();
        IAttachmentCustomStore attachmentCustomStore = this.getAttachmentCustomStore(attachStoreType);
        if (attachmentCustomStore == null) {
            throw new BussinessException(CommonMessageCode.UPLOAD_MODE_NONRECOGNITION, attachStoreType);
        }
        return attachmentCustomStore;
    }
}
