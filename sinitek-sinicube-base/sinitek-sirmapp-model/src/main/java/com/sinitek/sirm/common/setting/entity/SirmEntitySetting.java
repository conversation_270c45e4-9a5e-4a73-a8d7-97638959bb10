package com.sinitek.sirm.common.setting.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.MetadbBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;


/**
 * (SirmEntitysetting)表实体类
 *
 * <AUTHOR>
 * @date 2019-12-10 18:09:37
 */
@Schema(description = "实体配置")
@EqualsAndHashCode(callSuper = true)
@TableName("SIRM_ENTITYSETTING")
public class SirmEntitySetting extends MetadbBaseEntity {

    public static final String ENTITY_NAME = "SIRM_ENTITYSETTING";


    @TableField(value = "sourceentity")
    @Schema(description = "实体名字")
    private String sourceEntity;

    @TableField("sourceid")
    @Schema(description = "实体名称")
    private Long sourceId;

    @TableField("name")
    @Schema(description = "实体对应的记录Id")
    private String name;

    @TableField("VALUE")
    @Schema(description = "配置值")
    private String value;

    @TableField("BRIEF")
    @Schema(description = "配置简介")
    private String brief;


    public String getSourceEntity() {
        return sourceEntity;
    }

    public void setSourceEntity(String sourceEntity) {
        this.sourceEntity = sourceEntity;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getBrief() {
        return brief;
    }

    public void setBrief(String brief) {
        this.brief = brief;
    }

    public static String getEntityNameName() {
        return ENTITY_NAME;
    }

}
