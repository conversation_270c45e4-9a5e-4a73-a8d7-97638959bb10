package com.sinitek.sirm.common.sirmenum.message;

/**
 * 枚举系列的Message Code 常量
 *
 * <AUTHOR>
 * @date 2020-08-19
 */
public class EnumMessageCode {

    /**
     * objId输入错误
     */
    public final static String OBJ_ID_ERROR = "0604001";

    /**
     * objId为[{0}]的枚举不存在
     */
    public final static String OBJ_ID_DATA_NOT_EXIST = "0604002";

    /**
     * objIds参数不能为空
     */
    public final static String OBJ_IDS_CAN_NOT_NULL = "0604003";

    /**
     * 模块名称不能为空!
     */
    public final static String MODULE_NAME_CAN_NOT_NULL = "0604004";

    /**
     * 枚举类型不能为空!
     */
    public final static String TYPE_CAN_NOT_NULL = "0604005";

    /**
     * 枚举名称不能为空!
     */
    public final static String NAME_CAN_NOT_NULL = "0604006";

    /**
     * 枚举值不能为空!
     */
    public final static String VALUE_CAN_NOT_NULL = "0604007";

    /**
     * 模块名称不能超过66个字符
     */
    public final static String MODULE_NAME_LENGTH_TOO_BIG = "0604008";

    /**
     * 枚举类型不能超过66个字符
     */
    public final static String TYPE_LENGTH_TOO_BIG = "0604009";

    /**
     * 枚举名称不能超过200个字符
     */
    public final static String NAME_LENGTH_TOO_BIG = "0604010";

    /**
     * 枚举值不能超过200个字符
     */
    public final static String VALUE_LENGTH_TOO_BIG = "0604011";

    /**
     * 描述不能超过166个字符
     */
    public final static String DESCRIPTION_LENGTH_TOO_BIG = "0604012";

    /**
     * 序号请输入非负整数
     */
    public final static String SORT_CAN_NOT_NEGATIVE_NUMBER = "0604013";

    /**
     * 序号最大支持8位整数
     */
    public final static String SORT_LENGTH_TOO_BIG = "0604014";

    /**
     * 枚举保存失败
     */
    public final static String ENUM_SAVE_FAILED = "0604015";

    /**
     * 整型枚举值不能超过9个字符
     */
    public final static String INT_VALUE_LENGTH_TOO_BIG = "0604016";
}
