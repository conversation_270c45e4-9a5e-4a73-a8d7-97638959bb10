package com.sinitek.sirm.common.attachment.service;


import com.sinitek.sirm.common.attachment.entity.AttachmentContent;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * (SirmAttachmentContent)表Service
 *
 * <AUTHOR>
 * @date 2022-09-05 10:26:23
 */
public interface IAttachmentContentService {

    /**
     * 保存附件
     * @param attachmentContent
     */
    public void saveAttachmentContent(AttachmentContent attachmentContent);

    /**
     * 根据MD5获取附件正文
     * @param md5 md5字符串
     * @return 附件正文对象
     */
    public AttachmentContent getAttachmentContentByMD5 (String md5);

    /**
     * 查询附件内容（大数据）
     * @param id
     * @return
     * 若成功获取到，则返回附件内容
     * 若没有获取成功，抛出异常
     */
    public InputStream getAttachmentContentAsInputStreamById(Long id);

    /**
     * 根据id获取附件
     * @param id
     * @return
     * 若存在，返回附件内容
     * 若不存在，返回null
     */
    public AttachmentContent getAttachmentContentById(Long id);

    /**
     * 根据idList获取附件列表
     * @param idList
     * @return
     */
    public List<AttachmentContent> findAttachmentContentList(List<Long> idList);

    /**
     * 删除附件信息
     * @param id  objid
     */
    public void removeAttachmentContent(Long id);

    /**
     * 删除附件信息
     * @param AttachmentContent
     */
    public void removeAttachmentContent(AttachmentContent AttachmentContent);

     /**
     * 批量删除附件
     * @param ids AttachmentContent 附件的objid 字符集合
     */
    public void removeAttachmentContentList(String[] ids);

    /**
     *
     * @param idList
     */
    public void removeAttachmentContentList(List<Long> idList);

    /**
     * 更新 AttachmentContent
     * @param attachmentContent
     */
    public void updateAttachmentContent(AttachmentContent attachmentContent);

    /**
     * 根据存储类型 逻辑删除attachmentContent内容
     * @param attachmentContent
     */
    void delete(AttachmentContent attachmentContent);


    /**
     * 检查文件是否存在，不取附件正文
     * @param id    附件归档id
     * @return      是否存在附件
     */
    boolean checkAttachmentExist(Long id);

    /**
     * 根据附件Id列表获取md5
     * @param idList
     * @return
     */
    @SuppressWarnings("squid:ReturnMapCheck")
    Map<Long, String> getAttachmentContentMd5(List<Long> idList);
}
