<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.common.attachment.mapper.AttachmentMapper">

    <select id="findAttachmentCountBySourceIdListAndSourceEntity" resultType="com.sinitek.sirm.common.attachment.dto.AttachmentCountDTO">
        select sourceid sourceId,count(*) count
        from sirm_attachment
        where sourceentity = #{sourceEntity} and sourceid in
            <foreach collection="sourceIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        group by sourceid
    </select>

    <select id="getAttachmentContentById" resultType="com.sinitek.sirm.common.attachment.entity.Attachment">
        select sa.content from sirm_attachment sa where sa.objid = #{id}
    </select>

</mapper>
