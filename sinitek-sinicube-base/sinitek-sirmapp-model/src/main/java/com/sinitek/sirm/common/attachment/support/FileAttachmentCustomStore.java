package com.sinitek.sirm.common.attachment.support;

import com.google.common.base.Preconditions;
import com.sinitek.sirm.common.attachment.annotation.AttachmentStore;
import com.sinitek.sirm.common.attachment.constant.AttachmentConstant;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.attachment.vo.AttachmentStoreVO;
import com.sinitek.sirm.common.exception.SirmAppException;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.common.utils.FileUtil;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.TimeUtil;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

/**
 * File模式 存储下的 save、remove、getContent
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
@SuppressFBWarnings(value = "PATH_TRAVERSAL_IN", justification = "该类中的文件路径是从后端配置文件或数据库读取而非用户输入,无须处理")
@Slf4j
@Service(value = "fileAttachmentCustomStore")
@AttachmentStore(storeName = "文件系统",storeCode = AttachmentConstant.FILE_STORE_CODE,
        storeType = AttachmentConstant.FILE_STORE_TYPE,sort = 10)
public class FileAttachmentCustomStore extends AbstractAttachmentCustomStore {
    private static final String ROOT_PATH = "/";
    private static final String DOUBLE_BACK_SLASH = "\\";
    private static final String FILE_PATH_SUFFIX = "file:";
    private static final String ROOT_PATH_WITH_SUFFIX_COLON = ":/";


    @Override
    public void setStoreContent(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO) {
        String storeHome = attachmentStoreVO.getStoreHome();
        // 错误在这里抛出,因为这个方法是文件方式存储
        if (storeHome == null) {
            throw new SirmAppException("[COMMON,ATTACH_STOREHOME] is not configed", SirmAppException.IO);
        }
        // 获取是否附件加密的参数
        boolean encflag = CommonSettingUtil.isAttachEnc();

        File storeFile = getStoreFile(true,attachmentContent,attachmentStoreVO);
        InputStream inputStream = attachmentStoreVO.getInputStream();
        Preconditions.checkNotNull(storeFile,"storeFile不能为空");

        try {
            if (null == inputStream) {
                UploadFileDTO uploadFileDTO = new UploadFileDTO();
                uploadFileDTO.setResponseId(attachmentStoreVO.getTempFileStorePath());
                inputStream = getTempUploadFile(uploadFileDTO);
            }

            if (inputStream.available() > 0) {
                if (encflag) {
                    File tempFile = this.setStoreContentEncryption(inputStream, attachmentContent);
                    try (InputStream fin = Files.newInputStream(tempFile.toPath());) {
                        storeFile = IOUtil.copyFile(storeFile.getAbsolutePath(), fin);
                    }
                } else {
                    storeFile = IOUtil.copyFile(storeFile.getAbsolutePath(), inputStream);
                    attachmentContent.setEncKey(null);
                }
                attachmentContent.setStorePath(storeFile.toURI().toString());
                attachmentContent.setStoreType(1);
                attachmentContent.closeContentStream();
            }
        } catch (IOException ex) {
            log.error("save attachment to [ATTACH_STOREHOME] failed.[{}]",storeFile.getAbsolutePath(),ex);
            throw new SirmAppException("save attachment to [ATTACH_STOREHOME] failed.[" + storeFile.getAbsolutePath() + "]", SirmAppException.IO);
        }

    }

    @Override
    public InputStream getStoreContent(Attachment attachment, AttachmentStoreVO attachmentStoreVO) {
        String encKey = attachment.getEncKey();

        String storePath = StringUtils.replace(attachment.getStorePath(), DOUBLE_BACK_SLASH, ROOT_PATH);
        File file;
        InputStream result = null;
        if (StringUtils.isNotBlank(storePath)) {
            file = getFile(storePath, attachmentStoreVO);
            if (file != null && file.exists()) {
                // 如果有随机数种子，则对附件进行解密
                if(StringUtils.isNotBlank(encKey)) {
                    try(InputStream fin = Files.newInputStream(file.toPath());) {
                        result = this.getStoreContentDecryption(attachment, encKey, fin);
                    }  catch (IOException e) {
                        log.error("找不到附件,file:{}",file, e);
                    }
                } else {
                    try {
                        result = new FileInputStream(file);
                    } catch (FileNotFoundException e) {
                        log.error("找不到附件,file:{}",file, e);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public void removeStoreContent(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO) {
        File storeFile = getStoreFile(false, attachmentContent,attachmentStoreVO);
        if (storeFile != null) {
            if(!storeFile.delete()){
                log.info("删除失败");
            }
        }
    }

    @Override
    public boolean checkFileExist(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO) {
        String storePath = StringUtils.replace(attachmentContent.getStorePath(), DOUBLE_BACK_SLASH, ROOT_PATH);
        if (StringUtils.isNotBlank(storePath)) {
            File file = getFile(storePath, attachmentStoreVO);
            return file != null && file.exists();
        }
        return false;
    }

    /**
     * File存储模式,生成存储file对象，用于附件写操作
     * @param create
     * @return
     */
    private File getStoreFile(boolean create, AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO){
        String storePath = attachmentContent.getStorePath();
        String fileType = attachmentContent.getFileType();
        String storeHome = attachmentStoreVO.getStoreHome();

        File result = null;
        try{
            if (StringUtils.isNotBlank(storePath)) {
                if (storePath.indexOf(ROOT_PATH_WITH_SUFFIX_COLON) >= 1) {
                    result = new File(new URI(storePath));
                } else {
                    String storepath = IOUtil.combinePath(new String[]{storeHome, storePath});
                    result = new File(storepath);
                }
            } else if (create){
                String date = new SimpleDateFormat("yyyyMM").format(new Date());
                LocalDate currentDate = LocalDate.now();
                String day = currentDate.getDayOfMonth() + "";

                String filename = TimeUtil.formatDate(new Date(), "yyyyMMddHHmmss");
                if (StringUtils.isNotBlank(fileType)){
                    filename += ("." + fileType);
                }
                else{
                    filename += FileUtil.TEMP_FILE_COMMON_SUFFIX;
                }
                String storepath = IOUtil.combinePath(new String[]{storeHome, date, day, filename});
                result = new File(storepath);

                if(!result.getParentFile().exists()){
                    result.getParentFile().mkdirs();
                }
            }
        }
        catch (Exception ex){
            log.error("getstorefile failed,attachmentContent:{}",attachmentContent, ex);
        }
        return result;
    }

    private File getFile(String storePath, AttachmentStoreVO attachmentStoreVO) {
        String storeHome = attachmentStoreVO.getStoreHome();
        File file = null;
        if (StringUtils.isNotBlank(storePath)) {
            if (storePath.startsWith(ROOT_PATH)) {
                if (DOUBLE_BACK_SLASH.equals(File.separator)) {
                    // windows
                    String path = IOUtil.combinePath(new String[]{StringUtils.replace(storeHome, DOUBLE_BACK_SLASH, ROOT_PATH), storePath});
                    path = StringUtils.replace(path, DOUBLE_BACK_SLASH, ROOT_PATH);
                    file = new File(path);
                } else {
                    // linux下绝对路径
                    file = new File(storePath);
                }
            } else if (storePath.startsWith(FILE_PATH_SUFFIX)) {
                // 说明是uri格式
                try {
                    URI uri = new URI(storePath.replace(" ", "%20"));
                    file = new File(uri);
                } catch (URISyntaxException e) {
                    log.error("附件路径不正确,path:{}", storePath, e);
                }
            } else {
                String path = null;
                if (DOUBLE_BACK_SLASH.equals(File.separator)) {
                    if (!storePath.contains(ROOT_PATH_WITH_SUFFIX_COLON)) {
                        // windows下相对路径
                        path = IOUtil.combinePath(new String[]{StringUtils.replace(storeHome, DOUBLE_BACK_SLASH, ROOT_PATH), storePath});
                        path = StringUtils.replace(path, DOUBLE_BACK_SLASH, ROOT_PATH);
                    } else {
                        // windows下绝对路径
                        path = storePath;
                    }
                } else {
                    // Linux下相对路径
                    path = IOUtil.combinePath(new String[]{StringUtils.replace(storeHome, DOUBLE_BACK_SLASH, ROOT_PATH), storePath});
                }
                file = new File(path);
            }
        }
        return file;
    }
}

