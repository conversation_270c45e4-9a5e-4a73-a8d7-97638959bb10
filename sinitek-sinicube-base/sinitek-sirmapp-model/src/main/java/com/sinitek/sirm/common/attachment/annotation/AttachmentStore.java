package com.sinitek.sirm.common.attachment.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 消息对象注解
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface AttachmentStore {

    /**
     * 存储模式的名称
     */
    String storeName();

    /**
     * 存储的唯一标识
     * @return
     */
    String storeCode();

    /**
     * attachment表中存储的标识
     * @return
     */
    int storeType();

    /**
     * 排序，默认int的最大值
     */
    int sort() default Integer.MAX_VALUE;
}
