package com.sinitek.sirm.common.setting.properties;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "sinicube.setting")
public class SettingProperties {

    private String tableName = "";

    private String tableNamePrefix = "";

    private String fixedTableName = "SETTING";

    private List<String> systemUsageConfigList;

    /**
     * 获取全部系统使用配置
     * @return
     */
    public List<String> findAllSystemUsageConfigList () {
        List<String> allSystemUsageConfigList = findDefaultSystemUsageConfigList();
        if (CollectionUtils.isNotEmpty(systemUsageConfigList)) {
            allSystemUsageConfigList.addAll(systemUsageConfigList);
        }
        return allSystemUsageConfigList;
    }

    /**
     * 获取默认加载的系统使用配置
     * @return
     */
    public List<String> findDefaultSystemUsageConfigList() {
        List<String> defaultSystemUsageConfigList = new ArrayList<>();
        defaultSystemUsageConfigList.add("ATTACHMENT_MAXSIZE");
        defaultSystemUsageConfigList.add("DOCUMENTMIMETYPES");
        defaultSystemUsageConfigList.add("ROUTE_PARAM_CHECK");
        defaultSystemUsageConfigList.add("MENU_TAB_LIMIT");
        defaultSystemUsageConfigList.add("MENU_MODE");
        defaultSystemUsageConfigList.add("ORGAUTHDEFAULTVIEW");
        defaultSystemUsageConfigList.add("SIRM_TITLE");
        defaultSystemUsageConfigList.add("HOST_ADDRESS");
        defaultSystemUsageConfigList.add("ORGRESIGNDFAULTSWITCH");

        return defaultSystemUsageConfigList;
    }
}
