package com.sinitek.sirm.common.attachment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.attachment.mapper.AttachmentContentMapper;
import com.sinitek.sirm.common.attachment.message.AttachmentMessageCode;
import com.sinitek.sirm.common.attachment.service.IAttachmentContentService;
import com.sinitek.sirm.common.setting.enumerate.AttachStoreTypeEnum;
import com.sinitek.sirm.common.utils.EncryptUtil;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/7
 */
@Slf4j
@Service
public class AttachmentContentServiceImpl implements IAttachmentContentService {

    private static final String FOR_UPDATE_SQL = " for update";

    @Autowired
    private AttachmentContentMapper attachmentContentMapper;

    @Autowired
    private AttachmentContentServiceImpl self;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAttachmentContent(AttachmentContent attachmentContent) {
        if (attachmentContent != null && attachmentContent.getContent() != null ) {

            StopWatch sw = new StopWatch("附件MD5指纹生成");
            sw.start("开始生成指纹");
            String md5 = EncryptUtil.getFileMD5(attachmentContent.getContent());
            sw.stop();

            if (StringUtils.isBlank(md5)) {
                throw new BussinessException(AttachmentMessageCode.CONTENT_FINGER_PRINT_ERROR);
            }
            log.debug(sw.prettyPrint());
            attachmentContent.setMD5(md5);
            self.saveAttachmentContentWithoutMd5Check(attachmentContent, false);
        } else {
            throw new BussinessException(AttachmentMessageCode.CONTENT_FINGER_PRINT_ERROR);
        }
    }

    /**
     * 保存附件,不生成指纹
     * @param attachmentContent 附件正文存储对象
     * @param updateFlag        是否更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAttachmentContentWithoutMd5Check(AttachmentContent attachmentContent, boolean updateFlag) {
        if (attachmentContent == null) {
            return;
        }
        String md5 = attachmentContent.getMD5();
        if (StringUtils.isNotBlank(md5)) {
            AttachmentContent ac = getAttachmentContentByMD5(md5);

            // 如果没有存在的引用内容，或者内容相同但是附件名字不相同，则保存一条新的归档记录
            if (ac == null) {
                // 因为是新增，直接引用次数+1，不用再update一次，
                // 如果后续业务调整，这里要着重检查
                try {
                    attachmentContent.setId(null);
                    attachmentContent.setRefCount(1);
                    // 如果存放模式不是db，则清空附件内容
                    if (ObjectUtils.compare(attachmentContent.getStoreType(), AttachStoreTypeEnum.DB.getStoreTypeInt())  != 0 ){
                        attachmentContent.closeContentStream();
                    }
                    attachmentContentMapper.insert(attachmentContent);
                } catch (Exception e) {
                    log.error("附件【{}】归档失败", attachmentContent.getName(), e);
                    throw new BussinessException(AttachmentMessageCode.ATTACHMENT_CONTENT_SAVE_FAILED, attachmentContent.getName());
                }
            } else if(updateFlag){
                // 如果是新增，或者切换了存储方式，或者存储服务上找不到文件，则需要将归档的正文信息更新
                attachmentContent.setId(ac.getId());
                BeanUtils.copyProperties(attachmentContent, ac, "id", "MD5", "refCount");
                ac.setRefCount(ac.getRefCount() + 1);
                self.updateAttachmentContent(ac);
            } else {
                self.updateAttachmentContentRefCount(ac,1);
                attachmentContent.setId(ac.getId());
            }
        } else {
            throw new BussinessException(AttachmentMessageCode.ATTACHMENT_CONTENT_MD5_CANT_EMPTY, attachmentContent.getName());
        }
    }


    @Override
    public AttachmentContent getAttachmentContentByMD5 (String md5) {
        if (StringUtils.isNotBlank(md5)) {
            return attachmentContentMapper.selectOne(new QueryWrapper<AttachmentContent>()
                    .lambda()
                    .eq(AttachmentContent::getMD5, md5)
            );
        }
        return null;
    }

    @Override
    public InputStream getAttachmentContentAsInputStreamById(Long id) {
        AttachmentContent attachmentContent = attachmentContentMapper.getAttachmentContentAsInputStreamById(id);
        if (attachmentContent != null) {
            return attachmentContent.getContent();
        }
        return null;
    }


    @Override
    public AttachmentContent getAttachmentContentById(Long id) {
        return attachmentContentMapper.selectOne(new QueryWrapper<AttachmentContent>()
                .lambda()
                .eq(AttachmentContent::getId,id)
        );
    }

    @Override
    public List<AttachmentContent> findAttachmentContentList(List<Long> idList) {
        List<AttachmentContent> attachmentContents = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(idList)) {
            attachmentContents = attachmentContentMapper.selectList(new QueryWrapper<AttachmentContent>()
                    .lambda()
                    .in(AttachmentContent::getId, idList)
            );
        }
        return attachmentContents;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAttachmentContent(Long id) {
        AttachmentContent attachmentContent = getAttachmentContentById(id);
        if(attachmentContent!=null) {
            // 减去附件归档引用次数，如果引用次数为0，则删除归档
            if (attachmentContent.getRefCount() > 0) {
                self.updateAttachmentContentRefCount(attachmentContent, -1);
            } else {
                delete(attachmentContent);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAttachmentContent(AttachmentContent attachmentContent) {
        if (attachmentContent != null) {
            removeAttachmentContent(attachmentContent.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAttachmentContentList(String[] ids) {
        if (ids != null && ids.length > 0) {
            for (String s : ids) {
                Long id = NumberTool.safeToLong(s, 0L);
                removeAttachmentContent(id);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAttachmentContentList(List<Long> idList) {
        if (idList != null && !idList.isEmpty()) {
            for (Long id : idList) {
                removeAttachmentContent(id);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAttachmentContent(AttachmentContent attachmentContent) {
        if (attachmentContent != null) {
            LambdaUpdateWrapper<AttachmentContent> wrapper = new UpdateWrapper<AttachmentContent>()
                .lambda()
                .set(AttachmentContent::getUpdateTimeStamp, new Date())
                .set(AttachmentContent::getRefCount, NumberTool.safeToInteger(attachmentContent.getRefCount(), 1))
                .set(AttachmentContent::getFileType, attachmentContent.getFileType())
                .set(AttachmentContent::getStorePath, attachmentContent.getStorePath())
                .set(AttachmentContent::getStoreType, attachmentContent.getStoreType())
                .set(AttachmentContent::getEncAlgorithm, attachmentContent.getEncAlgorithm())
                .set(AttachmentContent::getEncKey, attachmentContent.getEncKey())
                .eq(AttachmentContent::getId, attachmentContent.getId());
            // 如果存放模式不是db，则清空附件内容，单独set的原因是防止spring反射调用getContent，导致数据库content始终有值
            if (ObjectUtils.compare(attachmentContent.getStoreType(), AttachStoreTypeEnum.DB.getStoreTypeInt())  != 0 ){
                wrapper.set(AttachmentContent::getContent, null);
            } else {
                wrapper.set(AttachmentContent::getContent, attachmentContent.getContent());
            }
            attachmentContentMapper.update(null, wrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(AttachmentContent attachmentContent) {
        // Do nothing because of logic delete
    }

    @Override
    public boolean checkAttachmentExist(Long id) {
        LambdaQueryWrapper<AttachmentContent> lambda = new QueryWrapper<AttachmentContent>().lambda();
        lambda.select(AttachmentContent::getName);
        lambda.eq(AttachmentContent::getId, id);
        lambda.isNull(AttachmentContent::getContent);
        List<AttachmentContent> names = attachmentContentMapper.selectList(lambda);
        return CollectionUtils.isEmpty(names);
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<Long, String> getAttachmentContentMd5(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<AttachmentContent> lambda = new QueryWrapper<AttachmentContent>().lambda();
        lambda.select(AttachmentContent::getId, AttachmentContent::getMD5);
        lambda.in(AttachmentContent::getId, idList);
        List<AttachmentContent> attachmentContentList = attachmentContentMapper.selectList(lambda);

        Map<Long, String> resultMap = new HashMap<>();
        for (AttachmentContent attachmentContent : attachmentContentList) {
            Long id = attachmentContent.getId();
            String md5 = attachmentContent.getMD5();
            resultMap.put(id, md5);
        }
        return resultMap;
    }

    /**
     * 附件目前没有更新概念，这种更新方法私有化
     * @param ac    附件内容对象
     * @param count 增加或者减少的数量
     */

    @Transactional(rollbackFor = Exception.class)
    public void updateAttachmentContentRefCount(AttachmentContent ac, int count) {
        if (ac != null) {
            ac.closeContentStream();
            attachmentContentMapper.update(null, new UpdateWrapper<AttachmentContent>()
                    .lambda()
                    .set(AttachmentContent::getRefCount, NumberTool.safeToInteger(ac.getRefCount(), 1) + count)
                    .eq(AttachmentContent::getId, ac.getId()));
        }
    }
}
