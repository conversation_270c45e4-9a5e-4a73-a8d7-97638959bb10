package com.sinitek.sirm.common.sirmenum.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.MetadbBaseEntity;

/**
 * (SirmEnum)表实体类
 *
 * <AUTHOR>
 * @date 2019-12-11 19:04:13
 */
@TableName(value = "SIRM_ENUM")
public class SirmEnum extends MetadbBaseEntity {

    public static final String ENTITY_NAME = "SIRMENUM";

    @TableField("catalog")
    private String cataLog;

    @TableField("type")
    private String type;

    @TableField("name")
    private String name;

    @TableField("value")
    private Integer value;

    @TableField("description")
    private String description;

    @TableField("sort")
    private Integer sort;

    @TableField("strvalue")
    private String strvalue;


    public String getCataLog() {
        return cataLog;
    }

    public void setCataLog(String cataLog) {
        this.cataLog = cataLog;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getStrvalue() {
        return strvalue;
    }

    public void setStrvalue(String strvalue) {
        this.strvalue = strvalue;
    }

    public static String getEntityNameName() {
        return ENTITY_NAME;
    }

}
