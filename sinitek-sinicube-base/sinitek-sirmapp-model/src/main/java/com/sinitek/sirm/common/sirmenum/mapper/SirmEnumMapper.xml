<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.common.sirmenum.mapper.SirmEnumMapper">

  <sql id="list">
    select objid,upper(catalog) as catalog, catalog originalCatalog,description,name,sort,value,strvalue,type,
    value as valuetype
    from sirm_enum  t
    <where>
      <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(param.catalog)">
        <bind name="catalog_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(param.catalog)"/>
        and upper(t.catalog) like upper(#{catalog_like}) escape '/'
      </if>
      <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(param.name)">
        <bind name="name_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(param.name)"/>
        and upper(t.name) like upper(#{name_like}) escape '/'
      </if>
      <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(param.type)">
        <bind name="type_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(param.type)"/>
        and upper(t.type) like upper(#{type_like}) escape '/'
      </if>
    </where>
  </sql>

  <select id="searchSirmEnum" resultType="com.sinitek.sirm.common.sirmenum.dto.EnumListDTO">
    <include refid="list" />
  </select>

  <select id="findSirmEnumByList" resultType="com.sinitek.sirm.common.sirmenum.entity.SirmEnum">
    select OBJID,CATALOG,CREATETIMESTAMP,description,sort,TYPE,version,UPDATETIMESTAMP,strvalue,ENTITYNAME,NAME,VALUE from sirm_enum
    <where>
      <foreach collection="list" item="item" separator=" or ">
        (upper(CATALOG) = #{item.catalog} and TYPE = #{item.type})
      </foreach>
    </where>
    order by SORT, TYPE, VALUE
  </select>

  <select id="findSirmEnumList" resultType="com.sinitek.sirm.common.sirmenum.dto.EnumListDTO">
    <include refid="list" />
  </select>

  <select id="findSirmEnumByCataLogAndTypeAndValue" resultType="com.sinitek.sirm.common.sirmenum.entity.SirmEnum">
    select OBJID,CATALOG,CREATETIMESTAMP,description,sort,TYPE,version,UPDATETIMESTAMP,strvalue,ENTITYNAME,NAME,VALUE from sirm_enum t
    <where>
      <if test="cataLog != null and cataLog !=''">
        and upper(t.cataLog) = upper(#{cataLog})
      </if>
      <if test="type != null and type != ''">
        and upper(t.type) = upper(#{type})
      </if>
      <if test="value != null">
        and t.value = #{value}
      </if>
    </where>
    order by SORT, TYPE, VALUE ASC
  </select>

</mapper>
