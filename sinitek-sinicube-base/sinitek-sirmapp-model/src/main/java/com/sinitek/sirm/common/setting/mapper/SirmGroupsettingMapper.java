package com.sinitek.sirm.common.setting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.common.setting.entity.SirmGroupsetting;

import java.util.List;

/**
 * (SirmGroupsetting)表数据库访问层
 *
 * <AUTHOR>
 * @date 2019-12-02 13:38:48
 */
public interface SirmGroupsettingMapper extends BaseMapper<SirmGroupsetting> {
    List<SirmGroupsetting> findGroupSettings();

    void updateGroupSettings(SirmGroupsetting groupSetting);
}
