package com.sinitek.sirm.common.setting.utils;

import com.sinitek.sirm.common.attachment.properties.AttachmentProperties;
import com.sinitek.sirm.common.log.properties.BusinessLogProperties;
import com.sinitek.sirm.common.setting.constant.CommonSettingConstant;
import com.sinitek.sirm.common.setting.enumerate.MenuModeEnum;
import com.sinitek.sirm.common.setting.enumerate.OrgAuthDefaultViewEnum;
import com.sinitek.sirm.common.setting.enumerate.PreventPermissionExpansionTypeEnum;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.tempdir.properties.TempDirProperties;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.user.properties.SiniCubeUserProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 公共参数配置，参数获取工具类
 */
@Slf4j
@Component
public class CommonSettingUtil {

    private static final int BYTE_SIZE = 1024;
    private static final int WATERMARK_WIDTH= 300;
    private static final int WATERMARK_HIGH= 70;
    private static final int TIMEINTERVAL= 10;
    private static final int WATERMARK_DEGREE= -20;
    private static final int ALPHA = 30;
    private static final int FONT_SIZE = 14;
    private static final int FONT_R = 0;
    private static final int FONT_G = 0;
    private static final int FONT_B = 0;
    public static final int FONT_R_INDEX = 0;
    public static final int FONT_G_INDEX = 1;
    public static final int FONT_B_INDEX = 2;

    /**
     * 获取密码最长使用周期
     * @return
     */
    public static int getMaximumPwdUsagePeriod() {
        return NumberTool.safeToInteger(SettingUtils.getStringValue("ORG", "ORGSIRM001"), 0);
    }

    /**
     * 密码失效前提前提醒天数
     * @return
     */
    public static int getPwdExpirationReminderDays () {
        return NumberTool.safeToInteger(SettingUtils.getStringValue("ORG", "ORGSIRM002"), 0);
    }

    /**
     * 发送系统消息用的管理员id
     * @return
     */
    public static String getSendMessageUserId() {
        return getCommonSetting(CommonSettingConstant.SEND_MESSAGE_ADMINUSER);
    }

    /**
     * 是否对附件进行加密
     * @return
     */
    public static boolean isAttachEnc() {
        String commonSetting = getCommonSetting(CommonSettingConstant.ATTACHMENT_ENC_FLAG);
        return NumberTool.safeToInteger(commonSetting, 0) == 1;
    }

    /**
     * 附件大小限制 单位为字节（b）
     * @return
     */
    public static long getAttachMaxSize() {
        String commonSetting = getCommonSetting(CommonSettingConstant.ATTACHMENT_MAX_SIZE);
        return strSizeToLongSize(commonSetting);
    }

    /**
     * 附件上传数量限制
     * @return
     */
    public static Integer getAttachUploadMaxSize() {
        String commonSetting = getCommonSetting(CommonSettingConstant.ATTACHMENT_UPLOAD_MAXSIZE);
        return NumberTool.safeToInteger(commonSetting, 0);
    }

    /**
     * 附件存放地址
     * @return
     */
    public static String getAttachStoreHome() {
        return SpringFactory.getBean(AttachmentProperties.class).getStoreHome();
    }

    /**
     * 附件存放方式, 默认为db
     * @return
     */
    public static String getAttachStoreType() {
        return SpringFactory.getBean(AttachmentProperties.class).getStoreType();
    }

    /**
     * 系统可上传文件类型
     *  - 不在系统允许范围内的文件类型将被排除
     * @return
     */
    public static List<String> getUploadFileMimeTypes() {
        List<String> allSupportFileTypeList = SpringFactory.getBean(AttachmentProperties.class).findAllSupportFileTypeList();
        String mimeTypes = getCommonSetting(CommonSettingConstant.DOCUMENT_MIME_TYPES);
        List<String> mimeTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(mimeTypes)) {
            String[] split = mimeTypes.split(",");
            for (String type : split) {
                if (allSupportFileTypeList.contains(type)) {
                    mimeTypeList.add(type);
                } else {
                    log.warn("系统允许的文件类型中没有: {}。但该文件类型被配置在[附件可上传文件类型]参数中。该文件类型已被忽略将不生效,请确保配置正确", type);
                }
            }
        }
        return mimeTypeList;
    }

    /**
     * 主机域名地址
     * @return
     */
    public static String getHostAddr() {
        return getCommonSetting(CommonSettingConstant.HOST_ADDRESS);
    }

    /**
     * 踢人模式
     * @return
     */
    public static boolean isKickMode() {
        String commonSetting = getCommonSetting(CommonSettingConstant.KICK_MODE);
        return NumberTool.safeToInteger(commonSetting, 0) == 1;
    }

    /**
     * 日志文件根路径
     * @return
     */
    public static String getLogFileRootPath() {
        return SpringFactory.getBean(BusinessLogProperties.class).getLogFileRootPath();
    }

    /**
     * 默认日志输出格式
     * @return
     */
    public static String getLogPattern() {
        return getCommonSetting(CommonSettingConstant.LOG_PATTERN);
    }

    /**
     * 定时任务执行异常提醒的邮箱
     * @return
     */
    public static List<String> getQrtzErrorRemindEmails() {
        String commonSetting = getCommonSetting(CommonSettingConstant.QRTZ_JOB_REMIND_EMAIL);
        List<String> remindEmails = new ArrayList<>();
        if (StringUtils.isNotBlank(commonSetting)) {
            commonSetting = commonSetting.replace("；", ";");
            String[] split = commonSetting.split(";");
            for (String type : split) {
                remindEmails.add(type);
            }
        }
        return remindEmails;
    }

    /**
     * 发送消息时间间隔
     * @return
     */
    public static int getMessageTimeinterval() {
        String commonSetting = getCommonSetting(CommonSettingConstant.MESSAGE_TIMEINTERVAL);
        return NumberTool.safeToInteger(commonSetting, TIMEINTERVAL);
    }

    /**
     * 菜单模式
     * @return
     */
    public static MenuModeEnum getMenuMode() {
        String commonSetting = getCommonSetting(CommonSettingConstant.MENU_MODE);
        return MenuModeEnum.getModeEnumByValue(commonSetting);
    }

    /**
     * 默认授权视图
     * @return
     */
    public static OrgAuthDefaultViewEnum getOrgAuthDefaultView() {
        String commonSetting = getCommonSetting(CommonSettingConstant.ORG_AUTH_DEFAULT_VIEW);
        return OrgAuthDefaultViewEnum.getViewEnumByValue(commonSetting);
    }

    /**
     * 自动登录有效时间(天)
     * @return
     */
    public static int getAutoLoginValidDay() {
        String commonSetting = getCommonSetting(CommonSettingConstant.AUTO_LOGIN_VALID_DAY);
        return commonSetting == null ? 0 : NumberTool.safeToInteger(commonSetting, 0);
    }

    /**
     * 自动登录有效次数(次)
     * @return
     */
    public static int getAutoLoginValidTimes() {
        String commonSetting = getCommonSetting(CommonSettingConstant.AUTO_LOGIN_VALID_TIMES);
        return commonSetting == null ? 0 : NumberTool.safeToInteger(commonSetting, 0);
    }

    /**
     * 是否支持短信发送
     * @return
     */
    public static boolean isSmsSupport() {
        String commonSetting = getCommonSetting(CommonSettingConstant.SMS_SUPPORT_FLAG);
        return NumberTool.safeToInteger(commonSetting, 0) == 1;
    }

    /**
     * 临时路径,临时文件存放的路径。当application.properties中没有配置setting.tempdir时就取该值，上传的文件和生成的excel文件会暂时存放在该路径下面
     * @return
     */
    public static String getTempDir() {
        return SpringFactory.getBean(TempDirProperties.class).getPath();
    }

    /**
     * 水印内容
     * @return
     */
    public static String getWatermarkContent() {
        return getCommonSetting(CommonSettingConstant.WATERMARK_CONTENT);
    }

    /**
     * 水印的倾斜度
     * @return
     */
    public static int getWatermarkDegree() {
        String commonSetting = getCommonSetting(CommonSettingConstant.WATERMARK_DEGREE);
        return NumberTool.safeToInteger(commonSetting, WATERMARK_DEGREE);
    }

    /**
     * 水印字体
     * @return {大小，透明度}
     */
    public static int[] getWatermarkFont() {
        String commonSetting = getCommonSetting(CommonSettingConstant.WATERMARK_FONT);
        String[] values = commonSetting.split(":");
        int [] arr = {FONT_SIZE,ALPHA};
        if (values.length >= CommonSettingConstant.WATERMARK_FONT_ITEM_COUNT) {
            arr[0] = NumberTool.safeToInteger(values[0], FONT_SIZE);
            arr[1] = NumberTool.safeToInteger(values[1], ALPHA);
        }
        return arr;
    }
    /**
     * 水印字体颜色
     * @return {r,g,b}
     */
    public static int[] getWatermarkFontColor() {
        String commonSetting = getCommonSetting(CommonSettingConstant.WATERMARK_FONT_COLOR);
        int [] arr = {FONT_R,FONT_G,FONT_B};
        if (StringUtils.isBlank(commonSetting)){
            return arr;
        }
        String[] values = commonSetting.split(",");
        if (values.length >= CommonSettingConstant.WATERMARK_FONT_COLOR_ITEM_COUNT) {
            arr[FONT_R_INDEX] = NumberTool.safeToInteger(values[FONT_R_INDEX], FONT_R);
            arr[FONT_G_INDEX] = NumberTool.safeToInteger(values[FONT_G_INDEX], FONT_G);
            arr[FONT_B_INDEX] = NumberTool.safeToInteger(values[FONT_B_INDEX], FONT_B);
        }
        return arr;
    }
    /**
     * 是否展示水印
     * @return
     */
    public static boolean isWatermarkShow() {
        final String commonSetting = getCommonSetting(CommonSettingConstant.WATERMARK_SHOW);
        return NumberTool.safeToInteger(commonSetting, 0) == 1;
    }

    /**
     * 水印密度(宽高)
     * @return {宽,高}
     */
    public static int[] getWatermarkWidthAndHeight() {
        String commonSetting = getCommonSetting(CommonSettingConstant.WATERMARK_WIDTH_HEIGHT);
        String[] values = commonSetting.split(":");
        int[] arr = {WATERMARK_WIDTH,WATERMARK_HIGH};
        if (values.length >= CommonSettingConstant.WATERMARK_SIZE_ITEM_COUNT) {
            arr[0] = NumberTool.safeToInteger(values[0], WATERMARK_WIDTH);
            arr[1] = NumberTool.safeToInteger(values[1], WATERMARK_HIGH);
        }
        return arr;
    }

    /**
     * 是否远程调用Windows工具
     * @return
     */
    public static boolean isRpcWindows() {
        String commonSetting = getCommonSetting(CommonSettingConstant.RPC_WINDOWS_FLAG);
        return NumberTool.safeToInteger(commonSetting, 0) == 1;
    }

    /**
     * 远程Windows服务器地址
     * @return
     */
    public static String getRpcWebService() {
        String commonSetting = getCommonSetting(CommonSettingConstant.RPC_WEB_SERVICE);
        return commonSetting == null ? "" : commonSetting;
    }

    /**
     * 获取Des密钥
     * @return
     */
    public static String getDesPassword(){
        return SettingUtils.getLocalSetting("despassword", "SIRM2012");
    }

    public static String getCommonSetting(String name){
        return getCommonSetting(CommonSettingConstant.COMMON_MODULE_NAME, name);
    }

    public static String getOrgSetting(String name, String devValue){
        return SettingUtils.getStringValue(CommonSettingConstant.ORG_MODULE_NAME, name, devValue);
    }

    public static String getOrgSetting(String name){
        return getCommonSetting(CommonSettingConstant.ORG_MODULE_NAME, name);
    }

    /**
     * 获取用户默认数据源名称
     * @return
     */
    public static String getUserDefaultDataSrc() {
        return SpringFactory.getBean(SiniCubeUserProperties.class).getUserDefaultDataSrc();
    }


    private static String getCommonSetting(String module, String name){
        return SettingUtils
                .getStringValue(module, name);
    }

    /**
     * 将设置带单位的文件大小转化为单位为b的值
     * @param maxSizeStr 1gb, 1mb, 1kb, 1b
     * @return 1024 * 1024 * 1024, 1024 * 1024, 1024, 1
     */
    private static long strSizeToLongSize(String maxSizeStr){
        long maxSize = 0;
        if(StringUtils.isNotBlank(maxSizeStr)) {
            maxSizeStr = maxSizeStr.trim().toLowerCase();
            try {
                if (maxSizeStr.endsWith(CommonSettingConstant.GB_NAME) || maxSizeStr.endsWith(CommonSettingConstant.G_NAME)) {
                    maxSize = NumberTool
                        .stringToBigDecimal(maxSizeStr.replace(CommonSettingConstant.GB_NAME, "").replace(CommonSettingConstant.G_NAME, ""))
                            .multiply(new BigDecimal(BYTE_SIZE * BYTE_SIZE * BYTE_SIZE)).longValue();
                } else if (maxSizeStr.endsWith(CommonSettingConstant.MB_NAME) || maxSizeStr.endsWith(CommonSettingConstant.M_NAME)) {
                    maxSize = NumberTool
                        .stringToBigDecimal(maxSizeStr.replace(CommonSettingConstant.MB_NAME, "").replace(CommonSettingConstant.M_NAME, ""))
                            .multiply(new BigDecimal(BYTE_SIZE * BYTE_SIZE)).longValue();
                } else if (maxSizeStr.endsWith(CommonSettingConstant.KB_NAME) || maxSizeStr.endsWith(CommonSettingConstant.K_NAME)) {
                    maxSize = NumberTool
                        .stringToBigDecimal(maxSizeStr.replace(CommonSettingConstant.KB_NAME, "")
                                .replace(CommonSettingConstant.K_NAME, "")).multiply(new BigDecimal(BYTE_SIZE)).longValue();
                } else {
                    maxSize = NumberTool.stringToBigDecimal(maxSizeStr.replace(CommonSettingConstant.B_NAME, "")).longValue();
                }
            } catch (Exception e){
                log.error("附件大小参数转换失败, 转换字符串为：{}",maxSize, e);
            }
        }
        return maxSize;
    }

    /**
     * 密码复杂度不满足是否强制修改密码
     * @return
     */
    public static boolean isPwComplexityChangePw () {
        int pwComplexityChangePw = NumberTool.safeToInteger(
                SettingUtils.getStringValue(CommonSettingConstant.ORG_MODULE_NAME,
                        CommonSettingConstant.PW_COMPLEXITY_CHANGE_PW), 0);
        return pwComplexityChangePw != 0;
    }

    /**
     * 判断是否进行权限防扩大化
     * @return
     */
    public static boolean isPreventPermissionExpansion () {
        String expansion= getCommonSetting(CommonSettingConstant.PREVENT_PERMISSION_EXPANSION);
        if (StringUtils.isBlank(expansion)) {
            return true;
        }
        if (PreventPermissionExpansionTypeEnum.CLOSE.getCode().equals(expansion)) {
            return false;
        }
        return true;
    }

    /**
     * 获取组织结构管理中离职人员配置
     * @return
     */
    public static Integer getOrgResignDfaultSwitch() {
        String commonSetting = getCommonSetting(CommonSettingConstant.ORG_RESIGN_DFAULT_SWITCH);
        return NumberTool.safeToInteger(commonSetting, 0);
    }

    /**
     * 菜单授权委派开关
     */
    public static boolean isMenuAuthDelegate() {
        String delegateFlag= getCommonSetting(CommonSettingConstant.MENU_AUTH_DELEGATE_FLAG);
        return CommonBooleanEnum.isTrue(NumberTool.safeToInteger(delegateFlag, 0));
    }
}
