package com.sinitek.sirm.common.attachment.support;

import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.attachment.listener.AttachmentListener;
import com.sinitek.sirm.common.attachment.message.AttachmentMessageCode;
import com.sinitek.sirm.common.attachment.service.impl.AttachmentContentServiceImpl;
import com.sinitek.sirm.common.attachment.vo.AttachmentStoreVO;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.common.utils.EncryptUtil;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.ObjectUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.FileNameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Objects;

/**
 * 附件常用操作逻辑,基于不同存储 save、remove、getContent
 * 主要用于附件和附件归档的实体转换以及业务转换
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
@Slf4j
@Component
public class AttachmentSupport {

    @Autowired
    private AttachmentListener attachmentListener;

    @Autowired
    private AttachmentContentServiceImpl attachmentContentService;


    /**
     * 根据存储类型 获取attachment内容
     * @param attachment
     * @return
     */
    public InputStream getContent(Attachment attachment) {
        // 如果有附件归档，则直接从附件归档附件正文
        if (attachment.getContentId() != null) {
            AttachmentContent attachmentContent = attachmentContentService.getAttachmentContentById(attachment.getContentId());
            if (attachmentContent != null) {
                return getContent(attachmentContent);
            } else {
                log.error("获取附件正文失败");
            }
        } else {
            AttachmentStoreVO attachmentStoreVO = loadAttachmentStoreVO(attachment);
            String storeType = attachmentStoreVO.getStoreType();
            IAttachmentCustomStore attachmentCustomStore = attachmentListener.getAttachmentCustomStore(storeType);

            if (attachmentCustomStore != null) {
                return attachmentCustomStore.getStoreContent(attachment, attachmentStoreVO);
            }
        }
        return null;
    }

    /**
     * 根据存储类型 获取attachment内容
     * @param attachmentContent
     * @return
     */
    public InputStream getContent(AttachmentContent attachmentContent) {
        Attachment attachment = new Attachment();
        BeanUtils.copyProperties(attachmentContent, attachment, "content");
        Integer storeTypeInt = attachment.getStoreType();
        Long attachmentContentId = attachmentContent.getId();
        attachment.setContentId(attachmentContentId);

        AttachmentStoreVO attachmentStoreVO = loadAttachmentStoreVO(attachment);
        String storeType = attachmentStoreVO.getStoreType();
        IAttachmentCustomStore attachmentCustomStore = attachmentListener.getAttachmentCustomStore(storeType);
        if (attachmentCustomStore != null) {
            return attachmentCustomStore.getStoreContent(attachment, attachmentStoreVO);
        }
        log.info("当前AttachmentContentId: {}, 数据库中的storeTypeInt: {}, 获取实现类的storeType: {}，对应的IAttachmentCustomStore为空", attachmentContentId, storeTypeInt, storeType);
        return null;
    }

    /**
     * 保存附件内容归档
     * @param attachment
     */
    public void save(Attachment attachment,InputStream stream) {
        String name = attachment.getName();
        if (StringUtils.isBlank(attachment.getFileType()) && name.contains(".")){
            attachment.setName(FileNameUtils.getBaseName(name));
            attachment.setFileType(FileNameUtils.getExtension(name));
        }
        AttachmentContent attachmentContent = new AttachmentContent();
        AttachmentStoreVO attachmentStoreVO = loadAttachmentStoreVO(attachment);
        String storeType = attachmentStoreVO.getStoreType();
        attachmentStoreVO.setTempFileStorePath(attachment.getTempFileStorePath());

        ObjectUtils.copyObject(attachment, attachmentContent);
        Long contentId = attachment.getContentId();
        attachmentContent.setId(contentId);

        String md5 = attachment.getTempFileMd5();
        if (stream != null && StringUtils.isBlank(md5)) {
            File tempFile = IOUtil.copyToTempDir(stream);
            StopWatch sw = new StopWatch("附件MD5指纹生成");
            sw.start("开始生成指纹");
            try {
                md5 = EncryptUtil.getFileMD5(new FileInputStream(tempFile));
                sw.stop();
                log.debug(sw.prettyPrint());
                if (StringUtils.isBlank(md5)) {
                    throw new BussinessException(AttachmentMessageCode.CONTENT_FINGER_PRINT_ERROR);
                }
                attachmentStoreVO.setInputStream(new FileInputStream(tempFile));
            } catch (FileNotFoundException e) {
                log.error("生成附件{}的md5指纹错误, 文件流存为临时文件后读取失败", name, e);
                throw new BussinessException(AttachmentMessageCode.CONTENT_FINGER_PRINT_ERROR);
            }
        }

        // 当附件引用ContentId为空 并且 新增操作 并且 md5为空，则拒绝保存
        if (null == contentId && IdUtil.isNotDataId(attachment.getObjId()) && StringUtils.isBlank(md5)) {
            log.error("附件找不到文件流，Attachment: {}", attachment);
            throw new BussinessException(AttachmentMessageCode.ATTACHMENT_CONTENT_CANNOT_FIND, name);
        }

        if (StringUtils.isNotBlank(md5)) {
            attachmentContent.setMD5(md5);
            AttachmentContent ac = attachmentContentService.getAttachmentContentByMD5(md5);
            IAttachmentCustomStore attachmentCustomStore = attachmentListener.getAttachmentCustomStore(storeType);
            boolean updateFlag = false;
            // 如果是新增，或者切换了存储方式，或者存储服务上找不到文件，则需要将归档的正文信息重新保存到附件服务器上
            if (ac == null
                    || !Objects.equals(ac.getStoreType(), attachmentContent.getStoreType())
                    || !attachmentCustomStore.checkFileExist(ac, attachmentStoreVO)) {
                attachmentCustomStore.setStoreContent(attachmentContent, attachmentStoreVO);
                updateFlag = true;
            }
            attachmentContentService.saveAttachmentContentWithoutMd5Check(attachmentContent, updateFlag);
            attachment.setContentId(attachmentContent.getId());
        }
        attachment.closeContentStream();
    }

    /**
     * 根据存储类型 逻辑删除attachment内容
     * @param attachment
     */
    public void delete(Attachment attachment) {
        // 如果有附件内容归档，先减去引用次数,在删掉引用内容id
        if (attachment.getContentId() != null) {
            attachmentContentService.removeAttachmentContent(attachment.getContentId());
            attachment.setContentId(null);
        }
        // Do nothing because of logic delete
    }

    /**
     * 加载附件对应的 AttachmentStoreVO
     * @param attachment
     */
    public AttachmentStoreVO loadAttachmentStoreVO(Attachment attachment) {
        AttachmentStoreVO attachmentStoreVO = new AttachmentStoreVO();
        String storeType = "";
        Integer storeTypeInt = attachment.getStoreType();

        if (storeTypeInt == null){
            storeType = CommonSettingUtil.getAttachStoreType();
            Integer intStoreType = attachmentListener.getIntStoreType(storeType);
            if (intStoreType != null) {
                attachment.setStoreType(intStoreType);
            }
        } else {
            storeType = attachmentListener.getStoreCode(storeTypeInt);
        }

        String storeHome = CommonSettingUtil.getAttachStoreHome();
        if (storeHome == null) {
            log.error("[COMMON,ATTACH_STOREHOME] is not configed");
        }
        attachmentStoreVO.setStoreType(storeType);
        attachmentStoreVO.setStoreHome(storeHome);
        return attachmentStoreVO;
    }

}
