package com.sinitek.sirm.common.setting.message;

/**
 * 功能配置系列的Message Code 常量
 *
 * <AUTHOR>
 * @date 2020-08-19
 */
public class SettingMessageCode {

    /**
     * 模块名不能为空
     */
    public final static String MODULE_NAME_CAN_NOT_NULL = "0603001";

    /**
     * 日志名不能为空
     */
    public final static String LOGGER_NAME_CAN_NOT_NULL = "0603002";

    /**
     * 创建路径失败
     */
    public final static String CREATE_PATH_FAIL = "0603004";

    /**
     * 业务日志Index模式为空
     */
    public final static String LOG_ES_INDEX_IS_NULL = "0603005";

    /**
     * 参数名不能为空
     */
    public final static String NAME_CAN_NOT_NULL = "0603006";

    /**
     * 不能重复保存参数
     */
    public final static String PARMS_CAN_NOT_REPEATED= "0603007";

    /**
     * 模块名和参数名不能为空
     */
    public final static String MODULE_AND_NAME_CAN_NOT_NULL = "0603008";

    /**
     * 模块名称由英文字母、数字、英文字符等组成
     */
    public final static String MODULE_VALIDATE = "0603009";

    /**
     * 参数名称由英文字母、数字、英文字符等组成
     */
    public final static String NAME_VALIDATE = "0603010";

    /**
     * 模块名称不能超过30个字符
     */
    public final static String MODULE_NAME_LENGTH_TOO_BIG = "0603011";

    /**
     * 参数名称不能超过30个字符
     */
    public final static String NAME_LENGTH_TOO_BIG = "0603012";

    /**
     * 参数值不能超过600个字符
     */
    public final static String VALUE_LENGTH_TOO_BIG = "0603013";

    /**
     * 参数说明不能超过200个字符
     */
    public final static String DESC_LENGTH_TOO_BIG = "0603014";
}
