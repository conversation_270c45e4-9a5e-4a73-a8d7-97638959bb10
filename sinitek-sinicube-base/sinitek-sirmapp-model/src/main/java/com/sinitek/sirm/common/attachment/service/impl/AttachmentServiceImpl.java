package com.sinitek.sirm.common.attachment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.sinitek.sirm.common.attachment.dto.AttachmentCountDTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.attachment.mapper.AttachmentMapper;
import com.sinitek.sirm.common.attachment.message.AttachmentMessageCode;
import com.sinitek.sirm.common.attachment.service.IAttachmentContentService;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.attachment.support.AttachmentSupport;
import com.sinitek.sirm.common.attachment.support.IAttachmentCustomExt;
import com.sinitek.sirm.common.attachment.support.IAttachmentFormat;
import com.sinitek.sirm.common.sonar.IgnoreSonarCheck;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadSearchDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (SirmAttachment)表Service实现
 *
 * <AUTHOR>
 * @date 2019-12-17 14:16:09
 */
@Slf4j
@Service
public class AttachmentServiceImpl extends ServiceImpl<AttachmentMapper, Attachment> implements IAttachmentService {

    @Autowired
    private AttachmentMapper attachmentMapper;
    @Autowired
    private IAttachmentContentService attachmentContentService;

    @Autowired
    private AttachmentSupport attachmentSupport;

    @Lazy
    @Autowired
    private AttachmentServiceImpl attachmentService;

    private static final String CONTENT_NAME = "content";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAttachment(Attachment attachment) {
        saveAttachment(attachment,attachment.getContent());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAttachment(Attachment attachment,InputStream inputStream) {
        attachmentSupport.save(attachment, inputStream);
        // 清空附件内容
        attachment.closeContentStream();
        if (attachment.getObjId() == null) {
            save(attachment);
        } else {
            updateAttachment(attachment);
        }
        IAttachmentCustomExt attachmentCustomExt = SpringFactory.getBean(IAttachmentCustomExt.class);
        if (null != attachmentCustomExt) {
            attachmentCustomExt.afterSave(attachment);
        }
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error("run saveAttachment close InputStream fail,attachment:{}",attachment, e);
            }
        }
    }

    /**
     * 批量保存附件
     *
     * @param attachmentList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAttachmentList(Long sourceId, String entityName, List<Attachment> attachmentList) {
        if (attachmentList != null && !attachmentList.isEmpty()) {
            List<Attachment> saveList = attachmentList.stream().filter(a -> a.getObjId() == null).collect(Collectors.toList());
            List<Attachment> updateList = attachmentList.stream().filter(a -> a.getObjId() != null).collect(Collectors.toList());
            // 新增
            if (CollectionUtils.isNotEmpty(saveList)) {
                for (Attachment attachment : saveList) {
                    attachment.setSourceId(sourceId);
                    attachment.setSourceEntity(entityName);
                    attachmentSupport.save(attachment, attachment.getContent());
                    // 清空附件内容
                    attachment.closeContentStream();
                }
                saveOrUpdateBatch(saveList);
            }
            // 更新
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (Attachment attachment : updateList) {
                    attachment.setSourceId(sourceId);
                    attachment.setSourceEntity(entityName);
                    attachmentSupport.save(attachment, attachment.getContent());
                    // 清空附件内容
                    attachment.closeContentStream();
                    updateAttachment(attachment);
                }
            }
        }
    }

    /**
     * 查询附件 (不建议使用)
     *
     * @param sourceEntity 实体名称
     * @param sourceId     关联业务编号
     * @return
     */
    @Override
    public List<Attachment> findAttachmentList(String sourceEntity, Long sourceId) {
        return attachmentMapper.selectList(new QueryWrapper<Attachment>()
                .lambda()
                .select(Attachment.class, i ->  !CONTENT_NAME.equals(i.getColumn()))
                .eq(Attachment::getSourceEntity,sourceEntity)
                .eq(Attachment::getSourceId,sourceId)
        );
    }


    @Override
    @Deprecated
    public List<Attachment> findAttachmentList(String sourceEntity, Long sourceId, int type) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        lambda.eq(Attachment::getSourceEntity, sourceEntity);
        lambda.eq(Attachment::getSourceId, sourceId);
        lambda.select(Attachment.class, i ->  !CONTENT_NAME.equals(i.getColumn()));
        lambda.eq(Attachment::getType, type);
        return attachmentMapper.selectList(lambda);
    }

    @Override
    public Attachment getAttachment(String sourceEntity, Long sourceId, int type) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        lambda.eq(Attachment::getSourceEntity, sourceEntity);
        lambda.eq(Attachment::getSourceId, sourceId);
        lambda.select(Attachment.class, i ->  !CONTENT_NAME.equals(i.getColumn()));
        lambda.eq(Attachment::getType, type);
        return attachmentMapper.selectOne(lambda);
    }

    /**
     * 只查询附件内容(大数据)
     *
     * @param id
     * @return
     */
    @Override
    public InputStream getAttachmentAsInputStreamById(Long id) throws SQLException {
        Attachment attachment = getAttachmentById(id);
        InputStream result = null;
        if (attachment != null) {
            result = attachmentSupport.getContent(attachment);
        }
        return result;
    }

    @Override
    public InputStream getAttachmentAsInputStreamByIdInDb(Long id) {
        Attachment attachment = attachmentMapper.getAttachmentContentById(id);
        if (attachment != null) {
            InputStream in = attachment.getContent();
            if (attachment.getContentId() != null) {
                AttachmentContent attachmentContent = attachmentContentService.getAttachmentContentById(attachment.getContentId());
                in = attachmentContent.getContent();
            }
            try {
                if(in.markSupported()){
                    if (in.available() == 0) {
                        in.reset();
                    } else {
                        in.mark(0);
                    }
                } else {
                    log.info("附件流不支持重置，流的实现为：{}", in.getClass().getName());
                }
            } catch (IOException e) {
                log.error("附件流重置失败，流的实现为：{}, msg：{}", in.getClass().getName(), e);
                // 兼容oracle（available方法返回数据与InputStream定义的不一样），直接mark
                if(in.markSupported()) {
                    in.mark(0);
                }
            }
            return in;
        }
        return null;
    }

    @Override
    public Attachment getAttachmentById(Long id) {
        return attachmentMapper.selectOne(new QueryWrapper<Attachment>()
                .lambda()
                .select(Attachment.class,i ->  !CONTENT_NAME.equals(i.getColumn()))
                .eq(Attachment::getObjId, id)
        );
    }

    @Override
    public List<Attachment> findAttachmentList(List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        lambda.select(Attachment.class, i ->  !CONTENT_NAME.equals(i.getColumn()));
        lambda.in(Attachment::getObjId, idList);
        return attachmentMapper.selectList(lambda);
    }

    /**
     * 查询实体业务对应附件的个数
     *
     * @param sourceEntity 实体名称
     * @param sourceId     关联业务编号
     * @return
     */
    @Override
    public int getAttachmentCount(String sourceEntity, Long sourceId) {
        return attachmentMapper.selectCount(
                new QueryWrapper<Attachment>().lambda()
                .eq(Attachment::getSourceEntity, sourceEntity)
                .eq(Attachment::getSourceId, sourceId)
        ).intValue();
    }

    /**
     * 删除附件信息
     *
     * @param id 根据objid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAttachment(Long id) {
        Attachment attachment = getAttachmentById(id);
        if (attachment != null) {
            removeAttachment(attachment);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttachment(Attachment attachment) {
        attachmentService.removeUnionAttachmentContent(attachment);
        Preconditions.checkNotNull(attachment,"removeAttachment中: attachment 对象不能为空");
        removeById(attachment.getObjId());
        attachmentSupport.delete(attachment);
    }

    /**
     * 批量删除附件
     *
     * @param ids          Attachment 附件的objid 字符集合，以逗号分割
     * @param sourceEntity 实体
     * @param sourceId     关联业务id
     * @return 返回剩下附件个数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int removeAttachmentList(String ids, String sourceEntity, Long sourceId) {
        if (ids != null && ids.trim().length() > 0) {
            removeAttachmentList(ids.split(","));
        }
        return getAttachmentCount(sourceEntity, sourceId);
    }

    /**
     * 批量删除附件
     *
     * @param ids Attachment 附件的objid 字符集合
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttachmentList(String[] ids) {
        if (ids != null && ids.length > 0) {
            for (String s : ids) {
                Long id = NumberTool.safeToLong(s, 0L);
                removeAttachment(id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttachmentList(List<Long> idList) {
        if (idList != null && !idList.isEmpty()) {
            for (Long id : idList) {
                removeAttachment(id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttachment(String sourceEntity, Long sourceId, String fileType) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>()
                .lambda()
                .select(Attachment::getContentId)
                .eq(Attachment::getSourceEntity, sourceEntity)
                .eq(Attachment::getSourceId, sourceId)
                .eq(Attachment::getFileType, fileType);
        // 先处理文件归档
        List<Attachment> attachments = attachmentMapper.selectList(lambda);
        attachmentService.removeUnionAttachmentContent(attachments);

        attachmentMapper.delete(lambda);
    }

    @Override
    public void removeAttachment(String sourceEntity, Long sourceId) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>()
                .lambda()
                .select(Attachment::getContentId)
                .eq(Attachment::getSourceEntity, sourceEntity)
                .eq(Attachment::getSourceId, sourceId);
        // 先处理文件归档
        List<Attachment> attachments = attachmentMapper.selectList(lambda);
        attachmentService.removeUnionAttachmentContent(attachments);

        attachmentMapper.delete(lambda);
    }

    @Override
    public Attachment getAttachment(String sourceEntity, Long sourceId, String fileType) {
        return attachmentMapper.selectOne(new QueryWrapper<Attachment>()
                .lambda()
                .select(Attachment.class, i ->  !CONTENT_NAME.equals(i.getColumn()))
                .eq(Attachment::getSourceEntity, sourceEntity)
                .eq(Attachment::getSourceId, sourceId)
                .eq(Attachment::getFileType, fileType)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttachmentList(String sourceEntity, Long sourceId, int type) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        lambda.select(Attachment::getContentId)
                .eq(Attachment::getSourceEntity, sourceEntity)
                .eq(Attachment::getSourceId, sourceId);
        if (type == -1) {
            lambda.isNull(Attachment::getType);
        } else {
            lambda.eq(Attachment::getType, type);
        }
        // 先处理文件归档
        List<Attachment> attachments = attachmentMapper.selectList(lambda);
        attachmentService.removeUnionAttachmentContent(attachments);
        attachmentMapper.delete(lambda);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAttachment(Attachment attachment) {
        update(null, new UpdateWrapper<Attachment>()
                .lambda()
                .set(Attachment::getSourceId, attachment.getSourceId())
                .set(Attachment::getSourceEntity, attachment.getSourceEntity())
                .set(Attachment::getName, attachment.getName())
                .set(Attachment::getType, attachment.getType())
                .set(Attachment::getBrief, attachment.getBrief())
                .set(Attachment::getFileType, attachment.getFileType())
                .set(Attachment::getOwnerId, attachment.getOwnerId())
                .set(Attachment::getConvertId, attachment.getConvertId())
                .set(Attachment::getConvertFlag, attachment.getConvertFlag())
                .set(Attachment::getDigest, attachment.getDigest())
                .set(Attachment::getContentId, attachment.getContentId())
                .eq(Attachment::getObjId, attachment.getObjId())
        );
    }

    @IgnoreSonarCheck(types = Map.class, ignoreReturn = true)
    @Override
    public Map<Long, Integer> getAttachmentCountBySourceIdListAndSourceEntity(List<Long> sourceIdList, String sourceEntity) {
        Map<Long, Integer> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sourceIdList)) {
            List<AttachmentCountDTO> attachmentCountDTOList = attachmentMapper.findAttachmentCountBySourceIdListAndSourceEntity(sourceIdList, sourceEntity);
            result = attachmentCountDTOList.stream().collect(Collectors.toMap(AttachmentCountDTO::getSourceId, AttachmentCountDTO::getCount));
        }
        return result;
    }

    @Override
    public List<Attachment> findAttachments(String sourceEntity, Integer type, List<Long> sourceIdList) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        lambda.eq(StringUtils.isNotBlank(sourceEntity), Attachment::getSourceEntity, sourceEntity)
                .in(CollectionUtils.isNotEmpty(sourceIdList), Attachment::getSourceId, sourceIdList);
        if (type != null) {
            if (type == -1) {
                lambda.isNull(Attachment::getType);
            } else {
                lambda.eq(Attachment::getType, type);
            }
        }
        return attachmentMapper.selectList(lambda);
    }

    @Override
    public Long copyAttachment(Long origId,String targetSourceName, Long targetSourceId) {
        if (origId == null) {
            throw new BussinessException(AttachmentMessageCode.TARGET_NOT_EXIST);
        }
        Attachment attachment = getAttachmentById(origId);
        if (attachment != null && StringUtils.isNotBlank(targetSourceName) && targetSourceId != null) {
            Attachment newAttachment = new Attachment();
            BeanUtils.copyProperties(attachment, newAttachment, "content", "objId");
            newAttachment.setSourceEntity(targetSourceName);
            newAttachment.setSourceId(targetSourceId);
            try {
                IAttachmentFormat af = SpringFactory.getBean(IAttachmentFormat.class);
                if(af != null) {
                    newAttachment = af.reSetAttachment(newAttachment);
                }
            } catch (RuntimeException e) {
                log.error("附件拷贝未进行自定义参数值,IAttachmentFormat#reSetAttachment未实现,{}","无其他核心参数",e);
            }
            // 兼容从Attachment表中获取content的老数据，如果是老数据，则需要设置content的输入流，保存到attachment_content表中
            if (attachment.getContentId() == null) {
                newAttachment.setContent(attachment.getContent());
                attachmentService.saveAttachment(newAttachment);
                // 除了将copy赋值的附件归档，还要将原来的附件归档
                attachment.setContentId(newAttachment.getContentId());
                attachment.closeContentStream();
                saveOrUpdate(attachment);
            } else {
                attachmentService.saveAttachment(newAttachment);
            }
            return newAttachment.getObjId();
        } else {
            throw new BussinessException(AttachmentMessageCode.TARGET_NOT_EXIST, origId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> copyAttachment(List<Long> origIds,String targetSourceName, Long targetSourceId) {
        List<Long> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(origIds)) {
            origIds = origIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            origIds.forEach(id -> {
                Long newId = attachmentService.copyAttachment(id, targetSourceName, targetSourceId);
                if (newId != null) {
                    result.add(newId);
                }
            });
            return result;
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeAttachment(Long id, String sourceEntity, Long sourceId) {
        LambdaUpdateWrapper<Attachment> lambda = new UpdateWrapper<Attachment>()
                .lambda()
                .eq(Attachment::getSourceEntity, sourceEntity)
                .eq(Attachment::getSourceId, sourceId)
                .eq(Attachment::getObjId, id);
        // 先处理文件归档
        List<Attachment> attachments = attachmentMapper.selectList(lambda);
        attachmentService.removeUnionAttachmentContent(attachments);

        attachmentMapper.delete(lambda);
    }

    @Override
    public boolean checkAttachmentExist(Long objId) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        lambda.select(Attachment::getName);
        lambda.eq(Attachment::getObjId, objId);
        lambda.isNull(Attachment::getContent);
        List<Attachment> names = attachmentMapper.selectList(lambda);
        return CollectionUtils.isEmpty(names);
    }

    @Override
    public List<Attachment> findAttachmentListByMultiSource(List<UploadSearchDTO> searchList) {
        LambdaQueryWrapper<Attachment> lambda = new QueryWrapper<Attachment>().lambda();
        int i = 0;
        for (UploadSearchDTO uploadSearchDTO : searchList) {
            String sourceId = uploadSearchDTO.getSourceId();
            String sourceEntity = uploadSearchDTO.getSourceEntity();
            List<Integer> typeList = uploadSearchDTO.getTypeList();

            lambda.eq(Attachment::getSourceId, sourceId);
            lambda.eq(Attachment::getSourceEntity, sourceEntity);
            if (CollectionUtils.isNotEmpty(typeList)) {
                lambda.in(Attachment::getType, typeList);
            }
            boolean lastFlag = i == searchList.size() -1;
            if (!lastFlag) {
                lambda.or();
            }
        }
        return list(lambda);
    }


    @Transactional(rollbackFor = Exception.class)
    public void removeUnionAttachmentContent(List<Attachment> attachments) {
        if (CollectionUtils.isNotEmpty(attachments)) {
            List<Long> contentIds = attachments.stream().map(Attachment::getContentId).filter(Objects::nonNull).collect(Collectors.toList());
            attachmentContentService.removeAttachmentContentList(contentIds);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void removeUnionAttachmentContent(Attachment attachment) {
        if (attachment != null && attachment.getContentId() != null) {
            attachmentContentService.removeAttachmentContent(attachment.getContentId());
        }
    }

}
