package com.sinitek.sirm.framework.service;

import com.sinitek.sirm.common.attachment.dto.UploaderResultDTO;
import com.sinitek.sirm.framework.dto.ChunkUploadDTO;
import com.sinitek.sirm.framework.dto.UploaderFileDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadSearchDTO;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

public interface IUploaderService {

    /**
     * 上传文件到临时目录下
     * @param file 文件
     * @param fsize 文件大小
     * @param suffixes 文件扩展名
     * @return 文件名
     */
    String uploadFile(MultipartFile file, String fsize, String suffixes);

    /**
     * 上传分片
     * @param chunk 分片
     * @return 错误信息
     */
    UploaderResultDTO uploadChunk(ChunkUploadDTO chunk);

    /**
     * 文件合并
     *
     * @param chunk 文件块对象
     */
    UploaderResultDTO merge(ChunkUploadDTO chunk);

    /**
     * 检查分片
     * @param chunk 分片
     * @return 分片数
     */
    UploaderResultDTO checkChunk(ChunkUploadDTO chunk);

    /**
     * 初始化附件上传控件列表
     * @param uploadto 附件参数
     * @param request 请求
     * @return 上传的文件
     */
    List<UploaderFileDTO> listFiles(UploadDTO uploadto, HttpServletRequest request);

    /**
     * 获取头像base64编码
     * @param dto 上传文件对象
     * @return base64格式的头像
     */
    String getAvatarBase64(UploadDTO dto);

    /**
     * 检查文件后缀
     * @param filename
     * @param suffixes
     */
    void checkFileSuffix(String filename, String suffixes);

    /**
     * 检查文件大小
     * @param size
     * @param fileSize
     * @param filename
     */
    void checkFileSize(long size, String fileSize, String filename);

    /**
     * 获取临时文件默认保存文件夹路径
     * @return
     */
    String getTempSaveDir();

    /**
     * 检查ChunkUploadDTO
     * @param chunk
     * @return
     */
    ChunkUploadDTO checkChunkUploadDTO(ChunkUploadDTO chunk);

    /**
     * 计算上传文件的md5
     * @param inputStream
     * @return
     */
    String countMd5(InputStream inputStream);

    /**
     * 检查普通上传文件参数
     * @param file
     * @param fileSize
     * @param suffixes
     * @return
     */
    String checkUploadFile(MultipartFile file, String fileSize, String suffixes);

    /**
     * 初始化多来源-附件上传控件列表
     * @param searchList
     * @return
     */
    List<UploaderFileDTO> listFilesByMultiSource(List<UploadSearchDTO> searchList);
}
