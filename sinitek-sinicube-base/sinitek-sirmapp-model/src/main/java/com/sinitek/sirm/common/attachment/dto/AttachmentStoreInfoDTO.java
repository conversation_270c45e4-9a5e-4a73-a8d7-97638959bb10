package com.sinitek.sirm.common.attachment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 附件存储信息DTO
 *
 * <AUTHOR>
 * @date 2021/01/18
 */
@Data
@Schema(description = "附件存储信息DTO")
public class AttachmentStoreInfoDTO {

    @Schema(description = "存储模式的名称")
    private String storeName;

    @Schema(description = "存储的唯一标识")
    private String storeCode;

    @Schema(description = "attachment表中存储的标识")
    private Integer storeType;

    @Schema(description = "排序值,从小到大")
    private Integer sort;

}
