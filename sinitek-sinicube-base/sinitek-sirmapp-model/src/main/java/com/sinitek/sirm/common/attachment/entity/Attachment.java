package com.sinitek.sirm.common.attachment.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sinitek.data.mybatis.base.MetadbBaseEntity;
import com.sinitek.data.mybatis.init.SpringFactoryUtil;
import com.sinitek.data.mybatis.typehandlers.SiniCubeInputStreamTypeHandler;
import com.sinitek.sirm.common.attachment.message.AttachmentMessageCode;
import com.sinitek.sirm.common.attachment.support.AttachmentSupport;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;

/**
 * (SirmAttachment)表实体类
 *
 * <AUTHOR>
 * @date 2019-12-17 14:16:09
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "附件信息")
@TableName(value = "SIRM_ATTACHMENT")
@JsonIgnoreProperties({"content"})
@EqualsAndHashCode(callSuper = true)
public class Attachment extends MetadbBaseEntity {

    public static final String ENTITY_NAME = "ATTACHMENT";

    @Schema(description = "所属实体名称")
    @TableField(value = "sourceentity")
    private String sourceEntity;

    @Schema(description = "附件类型")
    private Integer type;

    @Schema(description = "所属业务实例ID")
    @TableField(value = "sourceid")
    private Long sourceId;

    @Schema(description = "附件名称")
    private String name;

    @Schema(description = "附件大小")
    @TableField(value = "contentsize")
    private Long contentSize;

    @Schema(description = "页数")
    @TableField(value = "pagecount")
    private Long pageCount;

    @Schema(description = "转换标识")
    @TableField(value = "convertstatus")
    private Integer convertStatus;

    @Schema(description = "附件摘要")
    @TableField(value = "digest")
    private String digest;

    @Schema(description = "存储方式")
    @TableField(value = "storetype")
    private Integer storeType;

    @Schema(description = "文档存储路径")
    @TableField(value = "storepath")
    private String storePath;

    @Schema(description = "文件类型")
    @TableField(value = "filetype")
    private String fileType;


    @Schema(description = "附件内容")
    @TableField(typeHandler = SiniCubeInputStreamTypeHandler.class)
    private InputStream content;

    @Schema(description = "转换结果")
    @TableField(value = "convertresult")
    private Integer convertResult;

    @Schema(description = "转换标识")
    @TableField(value = "sendflag")
    private Integer sendFlag;

    @Schema(description = "转换标识")
    @TableField(value = "convertflag")
    private Integer convertFlag;

    @Schema(description = "转换附件Id")
    @TableField(value = "convertid")
    private Integer convertId;

    @Schema(description = "附件上传人id")
    @TableField(value = "ownerid")
    private String ownerId;

    @Schema(description = "备注")
    @TableField(value = "brief")
    private String brief;

    @Schema(description = "")
    @TableField(value = "storekey")
    private String storeKey;

    @Schema(description = "加密秘钥")
    @TableField(value = "enckey")
    private String encKey;

    @Schema(description = "加密所用的算法")
    @TableField(value = "enc_algorithm")
    private String encAlgorithm;

    @Schema(description = "附件内容id")
    @TableField(value = "content_id")
    private Long contentId;

    @TableLogic
    @TableField("deleted_flag")
    private Integer deletedFlag;

    @Schema(description = "临时文件的md5")
    @TableField(exist = false)
    private String tempFileMd5;

    @Schema(description = "临时文件的存储路径")
    @TableField(exist = false)
    private String tempFileStorePath;

    /**
     * 基于当前对象,构建出一个新的Attachment
     * @return
     */
    public Attachment cloneNewAttachment() {
        Attachment attachment = new Attachment();
        BeanUtils.copyProperties(this, attachment, "content","objId");
        AttachmentSupport attachmentSupport = SpringFactoryUtil.getBean(AttachmentSupport.class);
        InputStream conent = attachmentSupport.getContent(this);
        attachment.setContent(conent);
        return attachment;
    }

    /**
     * 重写 getConent的逻辑
     * @return
     */
    public InputStream getContent() {
        if (content != null) {
            return content;
        }
        if (StringUtils.isEmpty(this.getObjId())) {
            return content;
        }
        AttachmentSupport attachmentSupport = SpringFactoryUtil.getBean(AttachmentSupport.class);
        if (attachmentSupport != null) {
            content = attachmentSupport.getContent(this);
            if (content == null) {
                throw new BussinessException(AttachmentMessageCode.TARGET_NOT_EXIST, this.name);
            }
            return content;
        }
        return content;
    }

    @Override
    public String toString() {
        return "Attachment{" +
                "sourceEntity='" + sourceEntity + '\'' +
                ", type=" + type +
                ", sourceId=" + sourceId +
                ", name='" + name + '\'' +
                ", contentSize=" + contentSize +
                ", pageCount=" + pageCount +
                ", convertStatus=" + convertStatus +
                ", digest='" + digest + '\'' +
                ", storeType=" + storeType +
                ", storePath='" + storePath + '\'' +
                ", fileType='" + fileType + '\'' +
                ", convertResult=" + convertResult +
                ", sendFlag=" + sendFlag +
                ", convertFlag=" + convertFlag +
                ", convertId=" + convertId +
                ", ownerId='" + ownerId + '\'' +
                ", brief='" + brief + '\'' +
                ", storeKey='" + storeKey + '\'' +
                ", encKey='" + encKey + '\'' +
                '}';
    }

    public void closeContentStream() {
        try {
            if (this.getContent() != null){
                this.getContent().close();
                this.setContent(null);
            }
        } catch (IOException e) {
            log.error("关闭附件流失败,name:{}",this.name, e);
        }
    }
}
