package com.sinitek.sirm.common.attachment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.common.attachment.dto.AttachmentCountDTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;

import java.util.List;

/**
 * (SirmAttachment)表数据库访问层
 *
 * <AUTHOR>
 * @date 2019-12-17 14:16:09
 */
public interface AttachmentMapper extends BaseMapper<Attachment> {

    List<AttachmentCountDTO>  findAttachmentCountBySourceIdListAndSourceEntity(List<Long> sourceIdList, String sourceEntity);

    /**
     * 根据id获取附件流
     * @param id
     * @return
     */
    Attachment getAttachmentContentById(Long id);
}
