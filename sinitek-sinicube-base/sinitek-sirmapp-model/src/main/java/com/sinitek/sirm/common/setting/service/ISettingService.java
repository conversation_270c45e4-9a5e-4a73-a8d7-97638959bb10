package com.sinitek.sirm.common.setting.service;

import com.sinitek.sirm.common.setting.entity.SirmSetting;

import java.util.List;

/**
 * 作者: 王志华
 * 日期: 11-4-25
 * 时间: 下午4:44
 * 描述:
 */
public interface ISettingService {
    /**
     * 根据模块名获取参数配置实体
     * @param module 模块
     * @param name   参数名称
     * @return
     * 若存在，返回相应的参数配置信息
     * 若不存在，返回null
     */
    public SirmSetting getSetting(String module, String name);

    /**
     * 获取所有的配置信息
     * @return
     * 所有的参数配置信息
     */
    public List<SirmSetting> findAllSettings();

    /**
     * 根据模块名获取配置集合
     * @param module 模块名
     * @return
     * 返回相应的参数配置信息
     */
    public List<SirmSetting> findSettingsByModule(String module);

    /**
     * 保存配置
     * @param setting　参数配置表
     */
    public Long saveSetting(SirmSetting setting);

    /**
     * 批量添加或修改 sirmSettingList
     * @param sirmSettingList
     */
    public void saveOrUpdateBatchSettingList(List<SirmSetting> sirmSettingList);

    /**
     * 删除配置
     * @param setting　参数配置表
     */
    public void deleteSetting(SirmSetting setting);


    /**
     * 保存配置参数
     * @param seting
     * @return
     * 参数配置信息的id号
     */
    public Long saveSeting(SirmSetting seting);

    /**
     * 删除配置参数
     * @param objid
     */
    public void delSettingByObjid(String objid);

    /**
     * 查询参数设置表的模块
     * @return
     * 返回对应的模块
     */
    public List<String> findSettingModule();

    /**
     * 保存设置信息
     * @param module
     * @param name
     * @param value
     */
    public Long saveSetting(String module, String name, String value);

    /**
     * 根据objid查询参数集合
     * @param objid
     * @return
     * 返回相应的参数集合
     */
    public SirmSetting getSettingById(Long objid);

    /**
     * 清除参数配置缓存
     *
     * @param module 模块
     * @param name   参数名称
     */
    public void evictSetting(String module, String name);
}
