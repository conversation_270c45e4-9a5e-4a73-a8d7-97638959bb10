package com.sinitek.sirm.common.setting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.common.setting.entity.SirmGroupsetting;
import com.sinitek.sirm.common.setting.mapper.SirmGroupsettingMapper;
import com.sinitek.sirm.common.setting.service.ISirmGroupsettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * (SirmGroupsetting)表服务实现类
 *
 * <AUTHOR>
 * @date 2019-12-02 13:38:48
 */
@Service
public class SirmGroupsettingServiceImpl extends ServiceImpl<SirmGroupsettingMapper, SirmGroupsetting> implements
    ISirmGroupsettingService {

    @Autowired
    SirmGroupsettingMapper sirmGroupsettingMapper;

    @Override
    public List<SirmGroupsetting> findGroupSettingByCode(String catalogCode) {
        LambdaQueryWrapper<SirmGroupsetting> lambda = new QueryWrapper<SirmGroupsetting>().lambda();
        if (catalogCode != null && !"".equals(catalogCode)) {
            lambda.eq(SirmGroupsetting::getCatalogCode, catalogCode);
        }
        lambda.orderByAsc(SirmGroupsetting::getSort);
        List<SirmGroupsetting> groupSettings = sirmGroupsettingMapper.selectList(lambda);
        return groupSettings;
    }

    @Override
    public List<SirmGroupsetting> findGroupSettingList() {
        List<SirmGroupsetting> groupSettings = sirmGroupsettingMapper.selectList(new QueryWrapper<SirmGroupsetting>()
                .lambda()
                .orderByAsc(SirmGroupsetting::getSort));

        return groupSettings;
    }

    @Override
    public List<SirmGroupsetting> findGroupSettings() {
        return sirmGroupsettingMapper.findGroupSettings();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroupSettings(List<SirmGroupsetting> groupSettings) {
        if(groupSettings == null || groupSettings.size() <= 0){
            return ;
        }
        for(SirmGroupsetting groupSetting: groupSettings){
            sirmGroupsettingMapper.updateById(groupSetting);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertGroupSetting(SirmGroupsetting groupSetting) {
        if (groupSetting != null) {
            saveOrUpdate(groupSetting);
        }
    }

    @Override
    public SirmGroupsetting getGroupSettingById(Long id) {
       return getById(id);
    }
}
