package com.sinitek.sirm.common.sirmenum.utils;

import com.sinitek.sirm.common.sirmenum.entity.SirmEnum;
import com.sinitek.sirm.common.sirmenum.service.ISirmEnumService;
import com.sinitek.sirm.common.sirmenum.support.EnumMap;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.sirmenum.dto.TypeCataLogDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Enum 常用查询工具类
 *
 * <AUTHOR>
 * @date 2019-12-20
 */
public class EnumUtils {

    /**
     * 根据输入的模块和数据类型查询枚举数据
     * @param cataLog 模块
     * @param type    数据类型
     * @return
     * 返回相应的数据
     */
    public static Map<String, String> getSirmEnumByCataLogAndType(String cataLog, String type){
        Map<String, String> map = getSirmEnumService().getSirmEnumMapByCataLogAndType(cataLog, type);
        return new EnumMap(map);
    }

    /**
     * 获取枚举名称
     * @param cataLog 模块
     * @param type    数据类型
     * @param value   值
     * @return
     * 返回相应的名称
     */
    public static SirmEnum getSirmEnumName(String cataLog, String type, Integer value){
        return getSirmEnumService().getSirmEnum(cataLog, type, value);
    }
    /**
     * 获取枚举值
     * @param cataLog 模块
     * @param type    数据类型
     * @param name   枚举名称
     * @return
     * 返回相应的名称
     */
    public static SirmEnum getSirmEnumValue(String cataLog, String type, String name){
        SirmEnum result = null;
        if (StringUtils.isNotBlank(name)){
            List<SirmEnum> sirmEnums = getSirmEnumService().findSirmEnumByCatalogAndType(cataLog, type);
            for (SirmEnum sirmEnum : sirmEnums) {
                if (StringUtils.isNotBlank(sirmEnum.getName())){
                    if (name.equals(sirmEnum.getName())){
                        result = sirmEnum;
                        break;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取枚举名称
     * @param cataLog　模块
     * @param type　数据类型
     * @param strValue　值
     * @return
     * 返回相应的名称
     */
    public static SirmEnum getSirmEnumName(String cataLog, String type, String strValue){
        SirmEnum result = null;
        if (StringUtils.isNotBlank(strValue)){
            List<SirmEnum> sirmEnums = getSirmEnumService().findSirmEnumByCatalogAndType(cataLog, type);
            for(SirmEnum sirmEnum : sirmEnums){
                if (sirmEnum.getStrvalue() != null){
                    if (strValue.equals(sirmEnum.getStrvalue())){
                        result = sirmEnum;
                    }
                }
                else if (sirmEnum.getValue() != null){
                    if (strValue.equals(String.valueOf(sirmEnum.getValue()))){
                        result = sirmEnum;
                    }
                }
                if (result != null){
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 根据类型获取枚举名称
     * @param typeCataLogDTOList 枚举类型、枚举分类实体对象
     * @return ISirmEnum
     */
    public static List<SirmEnum> findSirmEnum(List<TypeCataLogDTO> typeCataLogDTOList){
        return getSirmEnumService().findSirmEnum(typeCataLogDTOList);
    }

    /**
     * 根据模块和值获取枚举名称
     * @param cataLog 模块
     * @param type    类型
     * @param value   值
     * @return
     * 若存在，返回相应名称
     * 若不存在，返回null
     */
    public static String getSirmEnumNameAsString(String cataLog, String type, Integer value) {
        SirmEnum e = getSirmEnumName(cataLog.toUpperCase(), type, value);
        return e == null ? null : e.getName();
    }

    private static ISirmEnumService getSirmEnumService() {
        return SpringFactory.getBean(ISirmEnumService.class);
    }

}
