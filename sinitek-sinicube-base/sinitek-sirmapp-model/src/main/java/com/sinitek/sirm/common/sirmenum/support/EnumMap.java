package com.sinitek.sirm.common.sirmenum.support;

import com.sinitek.sirm.common.utils.StringUtil;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Created by HF.fay on 14-9-26.
 */
public class EnumMap implements Map<String, String> {

    private Map<String, String> dataMap = null;

    public EnumMap(Map<String, String> map){
        dataMap = map;
    }

    @Override
    public int size() {
        return dataMap.size();
    }

    @Override
    public boolean isEmpty() {
        return dataMap.isEmpty();
    }

    @Override
    public boolean containsKey(Object key) {
        return dataMap.containsKey(StringUtil.safeToString(key, ""));
    }

    @Override
    public boolean containsValue(Object value) {
        return dataMap.containsValue(value);
    }

    @Override
    public String get(Object key) {
        return dataMap.get(StringUtil.safeToString(key, ""));
    }

    @Override
    public String put(String key, String value) {
        return dataMap.put(StringUtil.safeToString(key, ""), StringUtil.safeToString(value, ""));
    }

    @Override
    public String remove(Object key) {
        return dataMap.remove(StringUtil.safeToString(key, ""));
    }

    @Override
    public void putAll(Map m) {
        dataMap.putAll(m);
    }

    @Override
    public void clear() {
        dataMap.clear();
    }

    @Override
    public Set<String> keySet() {
        return dataMap.keySet();
    }

    @Override
    public Collection<String> values() {
        return dataMap.values();
    }

    @Override
    public Set<Entry<String, String>> entrySet() {
        return dataMap.entrySet();
    }

    @Override
    public boolean equals(Object o) {
        return dataMap.equals(o);
    }

    @Override
    public int hashCode() {
        return dataMap.hashCode();
    }


}
