package com.sinitek.sirm.common.setting.enumerate;

/**
 * 系统公共配置枚举
 * <AUTHOR>
 * @date 2024/9/25
 */
public enum SettingConfigEnum {

    LOG_PATTERN("LOG_PATTERN", "默认日志输出格式"),

    ATTACHMENT_ENC_FLAG("ATTACHMENT_ENC_FLAG", "是否对附件进行加密"),

    ATTACHMENT_MAX_SIZE("ATTACHMENT_MAXSIZE", "附件大小限制"),

    ATTACHMENT_UPLOAD_MAXSIZE("ATTACHMENT_UPLOAD_MAXSIZE", "附件上传数量限制"),

    ORG_AUTH_DEFAULT_VIEW("OR<PERSON>UTHDEFAULTVIEW", "默认授权视图"),

    DOCUMENT_MIME_TYPES("DOCUMENTMIMETYPES", "附件可上传文件类型"),

    MENU_MODE("MENU_MODE", "菜单模式"),

    MENU_TAB_LIMIT("MENU_TAB_LIMIT", "菜单限制数"),

    MENU_AUTH_DELEGATE_FLAG("MENU_AUTH_DELEGATE_FLAG", "菜单授权委派开关"),

    WATERMARK_SHOW("WATERMARK_SHOW", "是否展示水印"),

    WATERMARK_WIDTH_HEIGHT("WATERMARK_WIDTH_HEIGHT", "水印密度"),

    WATERMARK_DEGREE("WATERMARK_DEGREE", "水印的倾斜度"),

    WATERMARK_CONTENT("WATERMARK_CONTENT", "水印内容"),

    WATERMARK_FONT_COLOR("WATERMARK_FONT_COLOR", "水印字体颜色"),

    ORG_RESIGN_DFAULT_SWITCH("ORGRESIGNDFAULTSWITCH", "组织结构中离职人员操作开关"),

    CALENDAR_EVENT_REMIND_TYPE("CALENDAR_EVENT_REMIND_TYPE", "日程提醒方式"),

    CALENDAR_EVENT_SYNC_TYPE("CALENDAR_EVENT_SYNC_TYPE", "日程同步"),

    PREVENT_PERMISSION_EXPANSION("PREVENT_PERMISSION_EXPANSION", "权限防扩大化"),

    WATERMARK_FONT("WATERMARK_FONT", "水印字体"),


    //身份认证配置
    USER_CHECK_MODE("USER_CHECK_MODE", "认证模式"),

    KICK_MODE("KICKMODE", "踢人模式"),

    USER_CHECK_LDAP_HOST("USER_CHECK_LDAP_HOST","服务器地址"),

    USER_CHECK_LDAP_PORT("USER_CHECK_LDAP_PORT","服务器端口"),

    USER_CHECK_LDAP_SSL("USER_CHECK_LDAP_SSL","使用ssl"),

    USER_CHECK_LDAP_MEMBERDN("USER_CHECK_LDAP_MEMBERDN","账号格式"),

    USER_CHECK_LDAP_BASEDN("USER_CHECK_LDAP_BASEDN","查询根目录"),

    USER_CHECK_LDAP_FILTER("USER_CHECK_LDAP_FILTER", "用户过滤表达式"),

    USER_CHECK_LDAP_DISPLAYNAME("USER_CHECK_LDAP_DISPLAYNAME","用户姓名的属性名"),

    USER_CHECK_LDAP_PRINCIPAL("USER_CHECK_LDAP_PRINCIPAL","查询账号"),

    USER_CHECK_LDAP_CREDENTIALS("USER_CHECK_LDAP_CREDENTIALS","查询账号密码"),

    USER_CHECK_LDAP_ORGDEFAULTPOSITION("ORGDEFAULTPOSITION","默认岗位"),

    USER_CHECK_CLASSNAME("USER_CHECK_CLASSNAME","自定义实现类"),

    //密码安全策略
    PD_STR("PASSWORD","用户初始密码（用户重置密码后的默认初始密码）"),

    ORGSIRM001("ORGSIRM001", "密码最长使用周期 "),

    ORGSIRM002_STR("ORGSIRM002", "密码失效前提前提醒天数 "),

    ORGSIRM003_STR("ORGSIRM003", "密码重复次数 "),

    ORGSIRM004_STR("ORGSIRM004", "密码长度(设置密码的长度不得小于该值)"),

    ORGSIRM005_STR("ORGSIRM005", "密码复杂度-包含数字"),

    ORGSIRM006_STR("ORGSIRM006", "密码复杂度-包含小写字母"),

    ORGSIRM007_STR("ORGSIRM007", "密码复杂度-包含大写字母"),

    ORGSIRM008_STR("ORGSIRM008", "密码复杂度-包含特殊字符"),

    ORGSIRM009_STR("ORGSIRM009", "密码是否允许包含用户名"),

    ORGSIRM010_STR("ORGSIRM010", "密码输入错误次数"),

    ORGSIRM011_STR("ORGSIRM011", "锁定周期"),

    ORGSIRM012_STR("ORGSIRM012", "账户密码输入错误次数复位时间"),

    ORGSIRM013_STR("ORGSIRM013", "首次登录是否强制修改密码"),

    ORGSIRM014_STR("ORGSIRM014", "重置密码后是否强制修改密码"),

    PW_COMPLEXITY_CHANGE_PW("PW_COMPLEXITY_CHANGE_PW","密码复杂度不满足是否强制修改密码"),

    ORGSIRM015_STR("ORGSIRM015", "密码安全策略开关"),

    ORGSIRM018_STR("ORGSIRM018", "密码复杂度必须满足N项 (无必选项)"),

    RESET_PWD_MODE_STR("RESET_PWD_MODE", "重置密码模式"),

    //消息参数配置
    MAIL_SMTP_SERVER("MAIL_SMTPSERVER","smtp服务器地址"),

    MAIL_SMTP_PORT("MAIL_SMTPPORT", "smtp服务器发送端口"),

    MAIL_SMTP_USER("MAIL_SMTPUSER", "smtp服务器登录账号"),

    MAIL_SMTP_PD("MAIL_SMTPPWD","smtp服务器密码"),

    MAIL_ENCRYPTION_MODE("MAIL_ENCRYPTION_MODE","加密模式"),

    MAIL_SSL_FACTORY("MAIL_SSLFACTORY","SSL socket工厂类"),

    MAIL_SEND_ADDR("MAIL_FROMSYS","系统发送邮件地址"),

    SEND_MESSAGE_ADMINUSER("ADMINUSER", "发送消息用的管理员id"),

    QRTZ_JOB_REMIND_EMAIL("JOBEMAIL", "定时任务异常提醒邮箱"),

    MESSAGE_TIMEINTERVAL("TIMEINTERVAL", "发送消息时间间隔（单位:秒)");

    private String code;
    private String desc;

    SettingConfigEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SettingConfigEnum getEnumByCode(String code) {
        for (SettingConfigEnum item : SettingConfigEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
