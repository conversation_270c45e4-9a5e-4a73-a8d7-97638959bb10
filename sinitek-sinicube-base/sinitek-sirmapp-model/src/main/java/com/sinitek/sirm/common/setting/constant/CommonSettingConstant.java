package com.sinitek.sirm.common.setting.constant;

/**
 * 公共参数配置字段名
 */
public final class CommonSettingConstant {

    public static final String COMMON_MODULE_NAME = "COMMON";
    public static final String ORG_MODULE_NAME = "ORG";

    /**
     * 发送消息用的管理员id
     */
    public static final String SEND_MESSAGE_ADMINUSER = "ADMINUSER";

    /**
     *  是否对附件进行加密
     */
    public static final String ATTACHMENT_ENC_FLAG = "ATTACHMENT_ENC_FLAG";

    /**
     * 附件大小限制
     */
    public static final String ATTACHMENT_MAX_SIZE = "ATTACHMENT_MAXSIZE";

    /**
     * 附件上传数量限制
     */
    public static final String ATTACHMENT_UPLOAD_MAXSIZE = "ATTACHMENT_UPLOAD_MAXSIZE";

    /**
     * 系统可上传文件类型
     */
    public static final String DOCUMENT_MIME_TYPES = "DOCUMENTMIMETYPES";

    /**
     * 主机域名地址
     */
    public static final String HOST_ADDRESS = "HOST_ADDRESS";

    /**
     * 踢人模式
     */
    public static final String KICK_MODE = "KICKMODE";

    /**
     * 默认日志输出格式
     */
    public static final String LOG_PATTERN = "LOG_PATTERN";

    /**
     * 日志输出的字符集
     */
    public static final String LOG_CHARSET = "LOG_CHARSET";


    /**
     * 系统发送邮件地址
     */
    public static final String MAIL_SEND_ADDR= "MAIL_FROMSYS";

    /**
     * smtp服务器发送端口
     */
    public static final String MAIL_SMTP_PORT = "MAIL_SMTPPORT";

    /**
     * smtp服务器密码
     */
    public static final String MAIL_SMTP_PD = "MAIL_SMTPPWD";

    /**
     * smtp服务器地址
     */
    public static final String MAIL_SMTP_SERVER = "MAIL_SMTPSERVER";

    /**
     * smtp服务器登陆账号
     */
    public static final String MAIL_SMTP_USER = "MAIL_SMTPUSER";

    /**
     * 邮箱的加密模式
     */
    public static final String MAIL_ENCRYPTION_MODE = "MAIL_ENCRYPTION_MODE";

    /**
     * smtp服务器是否使用SSL
     */
    public static final String MAIL_SSL_FLAG = "MAIL_SSL";

    /**
     * smtp服务器SSL FACTORY
     */
    public static final String MAIL_SSL_FACTORY = "MAIL_SSLFACTORY";

    /**
     * 定时任务执行异常提醒的邮箱
     */
    public static final String QRTZ_JOB_REMIND_EMAIL = "JOBEMAIL";

    /**
     * 发送消息时间间隔
     */
    public static final String MESSAGE_TIMEINTERVAL = "TIMEINTERVAL";

    /**
     * 菜单模式
     */
    public static final String MENU_MODE = "MENU_MODE";

    /**
     * 菜单限制数
     */
    public static final String MENU_TAB_LIMIT = "MENU_TAB_LIMIT";

    /**
     * 邮件发送类
     */
    public static final String MAIL_SEND_CLASS = "MSG_SENDMODE_1";

    /**
     * 短信发送类
     */
    public static final String MOBILE_MSG_SEND_CLASS = "MSG_SENDMODE_2";

    /**
     * 系统提醒发送类
     */
    public static final String SYS_MSG_SEND_CLASS = "MSG_SENDMODE_4";

    /**
     * 默认授权视图
     */
    public static final String ORG_AUTH_DEFAULT_VIEW = "ORGAUTHDEFAULTVIEW";


    /**
     * 自动登录有效时间(天)
     */
    public static final String AUTO_LOGIN_VALID_DAY = "SIRM0001";

    /**
     * 自动登录有效次数(次)
     */
    public static final String AUTO_LOGIN_VALID_TIMES = "SIRM0002";

    /**
     * 是否支持短信发送
     */
    public static final String SMS_SUPPORT_FLAG = "SMS_SUPPORTFLAG";

    /**
     * 是否显示部门下的员工,当组织结构树状图上面展示人员时，是否展示直属部门的人员
     */
    public static final String UNIT_EMPLOYEE_SHOW = "UNIT_EMPOLYEESHOW";

    /**
     * 水印内容
     */
    public static final String WATERMARK_CONTENT = "WATERMARK_CONTENT";

    /**
     * 水印的倾斜度
     */
    public static final String WATERMARK_DEGREE = "WATERMARK_DEGREE";

    /**
     * 水印字体颜色
     */
    public static final String WATERMARK_FONT_COLOR = "WATERMARK_FONT_COLOR";

    /**
     * 水印文字大小透明度item数量 2
     */
    public static final Integer WATERMARK_FONT_ITEM_COUNT = 2;
    /**
     * 水印宽度高度item数量 2
     */
    public static final Integer WATERMARK_SIZE_ITEM_COUNT = 2;
    /**
     * rgb参数的数量 3
     */
    public static final Integer WATERMARK_FONT_COLOR_ITEM_COUNT = 3;

    /**
     * 水印字体
     */
    public static final String WATERMARK_FONT = "WATERMARK_FONT";

    /**
     * 是否展示水印
     */
    public static final String WATERMARK_SHOW = "WATERMARK_SHOW";

    /**
     * 水印密度
     */
    public static final String WATERMARK_WIDTH_HEIGHT = "WATERMARK_WIDTH_HEIGHT";

    /**
     * 是否远程调用Windows工具
     */
    public static final String RPC_WINDOWS_FLAG = "RPC_WINDOWSFLAG";

    /**
     * 远程Windows服务器地址
     */
    public static final String RPC_WEB_SERVICE = "RPC_WEBSERVICE";

    /**
     * gb的常量
     */
    public static final String GB_NAME = "gb";

    /**
     * g的常量
     */
    public static final String G_NAME = "g";

    /**
     * mb的常量
     */
    public static final String MB_NAME = "mb";

    /**
     * m的常量
     */
    public static final String M_NAME = "m";

    /**
     * kb的常量
     */
    public static final String KB_NAME = "kb";

    /**
     * k的常量
     */
    public static final String K_NAME = "k";

    /**
     * b的常量
     */
    public static final String B_NAME = "b";

    /**
     * 分号的常量
     */
    public static final String SEMICOLON = ";";

    /**
     * 2的常量
     */
    public static final Integer TWO_NAME = 2;

    /**
     * 组织结构管理中离职人员配置
     */
    public static final String ORG_RESIGN_DFAULT_SWITCH = "ORGRESIGNDFAULTSWITCH";

    /**
     * 组织结构管理中离职人员配置
     */
    public static final String ROUTE_PARAM_CHECK = "ROUTE_PARAM_CHECK";

    /**
     * 重置密码所用模式
     */
    public static final String RESET_PWD_MODE = "RESET_PWD_MODE";

    /**
     * 日程提醒方式
     */
    public static final String CALENDAR_EVENT_REMIND_TYPE = "CALENDAR_EVENT_REMIND_TYPE";

    /**
     * 日程提醒方式
     */
    public static final String CALENDAR_EVENT_SYNC_TYPE = "CALENDAR_EVENT_SYNC_TYPE";

    /**
     * 导出模式
     */
    public static final String EXPORT_MODE = "EXPORT_MODE";

    /**
     * 导出数据查询主机地址
     */
    public static final String EXPORT_HOST = "EXPORT_HOST";

    /**
     * 密码复杂度不满足是否强制修改密码
     */
    public static final String PW_COMPLEXITY_CHANGE_PW = "PW_COMPLEXITY_CHANGE_PW";

    /**
     * 检查通过
     */
    public static final String USER_CHECK_OK = "OK";


    /**
     * 用户重置密码或修改密码校验通过
     */
    public static final String USER_PASSWORD_UPDATE_CHECK_OK = "OK";

    /**
     * 权限防扩大
     */
    public static final String PREVENT_PERMISSION_EXPANSION = "PREVENT_PERMISSION_EXPANSION";

    /**
     * 系统可上传文件类型的枚举值type
     */
    public static final String DOCUMENT_MIME_TYPES_ENUM_TYPE = "document-mime-types";

    /**
     * 菜单授权委派开关
     */
    public static final String MENU_AUTH_DELEGATE_FLAG = "MENU_AUTH_DELEGATE_FLAG";
}
