package com.sinitek.sirm.common.attachment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.InputStream;

/**
 * 附件存储类型
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "附件存储类型")
public class AttachmentStoreVO {

    @Schema(description = "附件存储首页")
    private String storeHome;

    @Schema(description = "附件存储类型")
    private String storeType;

    @Schema(description = "文件流")
    private InputStream inputStream;

    @Schema(description = "临时文件的存储路径")
    private String tempFileStorePath;
}
