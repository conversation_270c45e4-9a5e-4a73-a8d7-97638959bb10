<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.common.setting.mapper.SirmSettingMapper">

    <select id="findSettingModule" resultType="java.lang.String">
        select distinct module from sirm_setting order by module
    </select>

    <select id="searchSettings" resultType="com.sinitek.sirm.common.setting.dto.SettingSaveDTO">
        select t.brief, t.createtimestamp, t.encryptionflag, t.entityname, t.module, t.name, t.objid, t.updatetimestamp, t.value, t.version
         from sirm_setting  t
        <where>
            <if test="params.module != null and params.module != ''">
                and t.module = #{params.module}
            </if>

            <if test="params.name != null and params.name != ''">
                <bind name="name_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(params.name)"/>
                and upper(t.name) like upper(#{name_like}) escape '/'
            </if>

            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.searchname)">
                <bind name="searchname_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(params.searchname)"/>
                and upper(t.name) like upper(#{searchname_like}) escape '/'
            </if>

            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.value)">
                <bind name="value_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(params.value)"/>
                and upper(t.value) like upper(#{value_like}) escape '/'
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.description)">
                <bind name="description_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(params.description)"/>
                and upper(t.brief) like upper(#{description_like}) escape '/'
            </if>
        </where>

    </select>

</mapper>
