package com.sinitek.sirm.common.setting.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.MetadbBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;

/**
 * (SirmGroupsetting)表实体类
 *
 * <AUTHOR>
 * @date 2019-12-02 13:38:48
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "参数组配置")
@TableName(value = "SIRM_GROUPSETTING")
public class SirmGroupsetting extends MetadbBaseEntity {

    public static final String ENTITY_NAME = "SirmGroupSetting";


    @TableField(value = "name")
    @Schema(description = "参数名称")
    private String name;

    @TableField(value = "catalogcode")
    @Schema(description = "参数组编码")
    private String catalogCode;

    @TableField(value = "url")
    @Schema(description = "url地址")
    private String url;

    @TableField(value = "sort")
    @Schema(description = "顺序")
    private Integer sort;

    @Schema(description = "绑定的语言文本项编码")
    private String i18n;



    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCatalogCode() {
        return catalogCode;
    }

    public void setCatalogCode(String catalogCode) {
        this.catalogCode = catalogCode;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getI18n() {
        return i18n;
    }

    public void setI18n(String i18n) {
        this.i18n = i18n;
    }

    public static String getEntityNameName() {
        return ENTITY_NAME;
    }
}
