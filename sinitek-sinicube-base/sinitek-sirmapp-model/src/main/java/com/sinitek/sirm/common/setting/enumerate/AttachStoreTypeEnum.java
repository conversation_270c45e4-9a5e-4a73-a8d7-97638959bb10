package com.sinitek.sirm.common.setting.enumerate;

import org.apache.commons.lang3.StringUtils;

/**
 * 附件存放方式枚举
 */
public enum AttachStoreTypeEnum {
    // 文件系统
    FILE("file", 1),
    // 数据库
    DB("db", 0),
    // 自定义
    CUSTOM("custom", -1),
    // FastDfs
    FASTDFS("fastDFS", 4),
    // MINIO
    MINIO("minio",5);

    private String value;

    private int storeTypeInt;

    AttachStoreTypeEnum(String value, int storeTypeInt) {
        this.value = value;
        this.storeTypeInt = storeTypeInt;
    }

    public static AttachStoreTypeEnum getTypeEnumByValue(String value) {
        for (AttachStoreTypeEnum typeEnum : AttachStoreTypeEnum.values()) {
            if (StringUtils.equals(typeEnum.getValue(),value)) {
                return typeEnum;
            }
        }
        //默认DB
        return AttachStoreTypeEnum.DB;
    }

    public String getValue() {
        return value;
    }

    public int getStoreTypeInt() {
        return storeTypeInt;
    }
}
