package com.sinitek.sirm.common.attachment.constant;

/**
 * 附件 常量
 *
 * <AUTHOR>
 * @date 2021-01-18
 */
public class AttachmentConstant {

    /**
     * DB模式的 store_type和 store_code
     */
    public static final int DB_STORE_TYPE = 0;
    public static final String DB_STORE_CODE = "db";

    /**
     * File模式的 store_type和 store_code
     */
    public static final int FILE_STORE_TYPE = 1;
    public static final String FILE_STORE_CODE = "file";

    /**
     * fastDFS模式的 store_type和 store_code
     */
    public static final int FAST_DFS_STORE_TYPE = 4;
    public static final String FAST_DFS_STORE_CODE = "fastDFS";

    /**
     * minio模式的 store_type和 store_code
     */
    public static final int MINIO_STORE_TYPE = 5;
    public static final String MINIO_STORE_CODE = "minio";
}
