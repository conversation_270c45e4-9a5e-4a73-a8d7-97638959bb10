package com.sinitek.sirm.common.setting.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.common.setting.entity.SirmEntitySetting;
import com.sinitek.sirm.common.setting.mapper.SirmEntitySettingMapper;
import com.sinitek.sirm.common.setting.service.IEntitySettingService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: REPORT
 * Author:      金砖
 * History:     11-7-27 created by 金砖
 */
@Service
public class EntitySettingServiceImpl extends ServiceImpl<SirmEntitySettingMapper, SirmEntitySetting> implements
    IEntitySettingService {

    /**
     * 获取实体配置列表
     *
     * @param sourceid
     * @param sourceEntity
     * @return
     */
    @Override
    public List<SirmEntitySetting> findEntitySettingBySourceidSourceEntity(Long sourceid, String sourceEntity) {
        QueryWrapper<SirmEntitySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SirmEntitySetting::getSourceId, sourceid)
                .eq(SirmEntitySetting::getSourceEntity, sourceEntity);
        List<SirmEntitySetting> list = this.baseMapper.selectList(queryWrapper);
        return list;
    }

    /**
     * 添加或更改实体配置
     *
     * @param entitySetting
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEntitySetting(SirmEntitySetting entitySetting) {
        saveOrUpdate(entitySetting);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveEntitySetting(Long sourceId, String sourceEntity, String name, String value) {
        SirmEntitySetting entitySetting = this.getEntitySetting(sourceId, sourceEntity, name);
        if(null == entitySetting){
            entitySetting = new SirmEntitySetting();
        }
        entitySetting.setSourceId(sourceId);
        entitySetting.setSourceEntity(sourceEntity);
        entitySetting.setName(name);
        entitySetting.setValue(value);
        saveOrUpdate(entitySetting);
    }

    /**
     * 删除实体配置
     *
     * @param entitySetting
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteEntitySetting(SirmEntitySetting entitySetting) {
        if (entitySetting != null) {
            this.baseMapper.deleteById(entitySetting.getObjId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIdList(List<Long> idList) {
        super.removeByIds(idList);
    }

    /**
     * 获取实体配置
     *
     * @param sourceid
     * @param sourceEntity
     * @param name
     * @return
     */
    @Override
    public SirmEntitySetting getEntitySetting(Long sourceid, String sourceEntity, String name) {
        List<SirmEntitySetting> entitySettings = this.findEntitySettings(sourceid, sourceEntity, name);
        if (CollectionUtils.isNotEmpty(entitySettings)) {
            return entitySettings.get(0);
        } else {
            return null;
        }
    }

    /**
     * 获取实体配置
     *
     * @param sourceid　实体对应的记录Id
     * @param sourceEntity　实体名称
     * @param name　配置名称
     * @return
     */
    @Override
    public List<SirmEntitySetting> findEntitySettings(Long sourceid, String sourceEntity, String name) {
        QueryWrapper<SirmEntitySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SirmEntitySetting::getSourceId, sourceid)
                .eq(SirmEntitySetting::getSourceEntity, sourceEntity)
                .eq(SirmEntitySetting::getName, name);

        List<SirmEntitySetting> sirmSettings = this.baseMapper.selectList(queryWrapper);
        return sirmSettings;
    }

    /**
     * 获取实体配置
     *
     * @param value
     * @param sourceEntity
     * @param name
     * @return
     */
    @Override
    public SirmEntitySetting getEntitySettingByValue(String value, String name, String sourceEntity) {
        QueryWrapper<SirmEntitySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SirmEntitySetting::getValue, value)
                .eq(SirmEntitySetting::getSourceEntity, sourceEntity)
                .eq(SirmEntitySetting::getName, name);
        List<SirmEntitySetting> list = this.baseMapper.selectList(queryWrapper);
        if (list != null && list.size() > 0) {
            return list.get(0);
        } else {
            return null;
        }
    }


    /**
     * 删除所以此类型的相关配置
     *
     * @param sourceId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteEntitySettingBySourceId(Long sourceId) {
        QueryWrapper<SirmEntitySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SirmEntitySetting::getSourceId, sourceId);
        this.baseMapper.delete(queryWrapper);
    }

    @Override
    public List<SirmEntitySetting> findEntitySettingBySourceEntityAndName(String sourceEntity, String name){
        QueryWrapper<SirmEntitySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SirmEntitySetting::getSourceEntity, sourceEntity)
                .eq(SirmEntitySetting::getName, name);
        List<SirmEntitySetting> list = this.baseMapper.selectList(queryWrapper);
        return list;
    }

}
