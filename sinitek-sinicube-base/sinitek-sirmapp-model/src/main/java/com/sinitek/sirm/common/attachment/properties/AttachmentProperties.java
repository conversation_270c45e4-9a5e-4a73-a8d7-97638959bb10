package com.sinitek.sirm.common.attachment.properties;

import com.sinitek.sirm.common.setting.enumerate.AttachStoreTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/28
 */

@Data
@Component
@Schema(description = "附件相关参数配置")
@ConfigurationProperties(prefix = "sinicube.attachment")
public class AttachmentProperties {

    private String storeHome = "" ;

    private String storeType = AttachStoreTypeEnum.DB.getValue() ;

    @Schema(description = "系统支持上传的文件类型列表")
    private List<String> supportFileTypeList;

    @Schema(description = "系统不支持上传的文件类型列表, 主要用于项目上去掉框架内置的文件类型")
    private List<String> nonsupportFileTypeList;

    /**
     * 获取系统中全部支持上传的文件类型列表
     * @return
     */
    public List<String> findAllSupportFileTypeList() {
        List<String> defaultSupportFileTypeList = findDefaultSupportFileTypeList();
        if (CollectionUtils.isNotEmpty(supportFileTypeList)) {
            defaultSupportFileTypeList.addAll(supportFileTypeList);
        }

        if (CollectionUtils.isNotEmpty(nonsupportFileTypeList)) {
            defaultSupportFileTypeList.removeAll(nonsupportFileTypeList);
        }
        return defaultSupportFileTypeList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 系统默认支持上传的文件类型列表
     * @return
     */
    public List<String> findDefaultSupportFileTypeList () {
        List<String> defaultSupportFileTypeList = new ArrayList<>();
        defaultSupportFileTypeList.add("md");
        defaultSupportFileTypeList.add("rar");
        defaultSupportFileTypeList.add("zip");
        defaultSupportFileTypeList.add("7z");
        defaultSupportFileTypeList.add("7zip");
        defaultSupportFileTypeList.add("doc");
        defaultSupportFileTypeList.add("docx");
        defaultSupportFileTypeList.add("ppt");
        defaultSupportFileTypeList.add("pptx");
        defaultSupportFileTypeList.add("xls");
        defaultSupportFileTypeList.add("xlsx");
        defaultSupportFileTypeList.add("pdf");
        defaultSupportFileTypeList.add("jpg");
        defaultSupportFileTypeList.add("png");
        defaultSupportFileTypeList.add("gif");
        defaultSupportFileTypeList.add("html");
        defaultSupportFileTypeList.add("htm");
        defaultSupportFileTypeList.add("txt");
        defaultSupportFileTypeList.add("xml");
        defaultSupportFileTypeList.add("ipa");
        defaultSupportFileTypeList.add("apk");
        defaultSupportFileTypeList.add("json");
        defaultSupportFileTypeList.add("mp4");
        defaultSupportFileTypeList.add("mov");
        return defaultSupportFileTypeList;
    }
}
