package com.sinitek.sirm.common.setting.service;

import com.sinitek.sirm.common.setting.entity.SirmEntitySetting;

import java.util.List;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: COMMON
 * Author:      金砖
 * History:     11-7-27 created by 金砖
 */
public interface IEntitySettingService {

    /**
     * 获取实体配置列表
     * @param sourceid　实体对应的记录Id
     * @param sourceEntity　实体名称
     * @return
     * 返回相应的实体配置列表
     */
    public List<SirmEntitySetting> findEntitySettingBySourceidSourceEntity(Long sourceid,
        String sourceEntity);

    /**
     * 保存实体配置
     * @param entitySetting　
     */
    public void saveEntitySetting(SirmEntitySetting entitySetting);

    /**
     * 保存实体属性，会对每个属性进行重复判断
     * @param sourceId　实体对应的记录Id
     * @param sourceEntity　实体名称
     * @param name　配置名称
     * @param value　配置值
     */
    public void saveEntitySetting(Long sourceId, String sourceEntity, String name, String value);

    /**
     * 删除实体配置
     * @param entitySetting
     */
    public void deleteEntitySetting(SirmEntitySetting entitySetting);

    /**
     * 根据 idList 删除 entitySettings
     * @param idList
     */
    public void deleteByIdList(List<Long> idList);

    /**
     * 获取实体配置
     * @param sourceid　实体对应的记录Id
     * @param sourceEntity　实体名称
     * @param name　配置名称
     * @return
     * 若存在，返回列表中的第一个值
     * 若不存在，返回null
     */
    public SirmEntitySetting getEntitySetting(Long sourceid, String sourceEntity, String name);

    /**
     * 获取实体配置
     *
     * @param sourceid　实体对应的记录Id
     * @param sourceEntity　实体名称
     * @param name　配置名称
     * @return
     */
    public List<SirmEntitySetting> findEntitySettings(Long sourceid, String sourceEntity,
        String name);

    /**
     * 获取实体配置
     * @param value　配置值
     * @param sourceEntity　实体名称
     * @param name　配置名称
     * @return
     * 若存在，返回列表中的第一个值
     * 若不存在，返回null
     */
    public SirmEntitySetting getEntitySettingByValue(String value, String name, String sourceEntity);

    /**
     * 删除所以此类型的相关配置
     * @param typeId
     */
    public void deleteEntitySettingBySourceId(Long typeId);


    /**
     * 获取实体配置列表
     *
     * @param name
     * @param sourceEntity
     * @return
     */
    public List<SirmEntitySetting> findEntitySettingBySourceEntityAndName(String sourceEntity,
        String name);

}
