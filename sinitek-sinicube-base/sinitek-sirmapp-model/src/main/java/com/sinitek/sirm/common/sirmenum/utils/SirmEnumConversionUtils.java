package com.sinitek.sirm.common.sirmenum.utils;

import com.sinitek.cloud.sirmapp.enumeration.dto.SirmEnumDTO;
import com.sinitek.sirm.common.sirmenum.entity.SirmEnum;
import org.springframework.beans.BeanUtils;

/**
 * 枚举系列JavaBean的转换类
 *
 * <AUTHOR>
 * @date 2020-05-09
 */
public class SirmEnumConversionUtils {

    public static SirmEnum toSirmEnum(SirmEnumDTO sirmEnumDTO) {
        if (sirmEnumDTO == null) {
            return null;
        }
        SirmEnum sirmEnum = new SirmEnum();
        BeanUtils.copyProperties(sirmEnumDTO,sirmEnum);
        return sirmEnum;
    }

    public static SirmEnumDTO toSirmEnumDTO(SirmEnum sirmEnum) {
        if (sirmEnum == null) {
            return null;
        }
        SirmEnumDTO sirmEnumDTO = new SirmEnumDTO();
        BeanUtils.copyProperties(sirmEnum,sirmEnumDTO);
        return sirmEnumDTO;
    }

}
