package com.sinitek.sirm.common.attachment.support;

import com.sinitek.sirm.common.attachment.annotation.AttachmentStore;
import com.sinitek.sirm.common.attachment.constant.AttachmentConstant;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.attachment.service.IAttachmentContentService;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.attachment.vo.AttachmentStoreVO;
import com.sinitek.sirm.common.exception.SirmException;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;

/**
 * DB模式 存储下的 save、remove、getContent
 *
 * <AUTHOR>
 * @date 2019-12-18
 */
@Slf4j
@Service(value = "dbAttachmentCustomStore")
@AttachmentStore(storeName = "数据库(默认)", storeCode = AttachmentConstant.DB_STORE_CODE,
        storeType = AttachmentConstant.DB_STORE_TYPE,sort = 1)
public class DBAttachmentCustomStore extends AbstractAttachmentCustomStore  {

    @Autowired
    private IAttachmentService attachmentService;
    @Autowired
    private IAttachmentContentService attachmentContentService;

    @Override
    public void setStoreContent(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO) {
        InputStream fin = null;
        try {
            fin = attachmentStoreVO.getInputStream();

            // 获取是否附件加密的参数
            boolean encFlag = CommonSettingUtil.isAttachEnc();
            attachmentContent.setStoreType(0);

            if (null == fin) {
                UploadFileDTO uploadFileDTO = new UploadFileDTO();
                uploadFileDTO.setResponseId(attachmentStoreVO.getTempFileStorePath());
                fin = getTempUploadFile(uploadFileDTO);
            }

            if (fin.available() > 0) {
                if (encFlag) {
                    File tempFile = this.setStoreContentEncryption(fin, attachmentContent);
                    attachmentContent.setContent(Files.newInputStream(tempFile.toPath()));
                } else {
                    // 不进行加密
                    attachmentContent.setContent(fin);
                    attachmentContent.setEncKey(null);
                }
            }
        } catch (Exception ex) {
            log.error("run DBAttachmentCustomStore.setStoreContent failed.,name:{}",attachmentContent.getName(), ex);
            throw new SirmException(ex);
        }
    }

    @Override
    public InputStream getStoreContent(Attachment attachment, AttachmentStoreVO attachmentStoreVO) {
        String encKey = attachment.getEncKey();
        InputStream result = null;
        InputStream contentInputStream;
        if (attachment.getContentId() != null) {
            contentInputStream =attachmentContentService.getAttachmentContentAsInputStreamById(attachment.getContentId());
        } else {
            contentInputStream = attachmentService.getAttachmentAsInputStreamByIdInDb(attachment.getObjId());
        }
        if (contentInputStream == null) {
            return null;
        }
        if(StringUtils.isNotBlank(encKey)){
            result = this.getStoreContentDecryption(attachment,encKey, contentInputStream);
        } else {
            File tempfile = IOUtil.createTempFile();
            try (OutputStream fout = Files.newOutputStream(tempfile.toPath());) {
                IOUtil.copy(contentInputStream,fout);
                result = Files.newInputStream(tempfile.toPath());
            } catch (IOException e) {
                log.error("找不到附件,attachment:{}",attachment, e);
            }
        }
        return result;
    }

    @Override
    public void removeStoreContent(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO) {
        log.debug("run db removeStoreContent");
    }

    @Override
    public boolean checkFileExist(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO) {
        boolean result = false;
        Long id = attachmentContent.getId();
        if (id != null) {
            result =attachmentContentService.checkAttachmentExist(id);
        }
        return result;
    }
}

