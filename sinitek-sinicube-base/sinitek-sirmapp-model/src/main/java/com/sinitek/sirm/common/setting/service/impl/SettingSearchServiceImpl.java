package com.sinitek.sirm.common.setting.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.common.setting.dto.SettingSaveDTO;
import com.sinitek.sirm.common.setting.dto.SettingSearchDTO;
import com.sinitek.sirm.common.setting.entity.SirmSetting;
import com.sinitek.sirm.common.setting.mapper.SirmSettingMapper;
import com.sinitek.sirm.common.setting.service.ISettingSearchService;
import com.sinitek.sirm.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: 王志华
 * Date: 12-2-9
 * Time: 上午9:53
 * To change this template use File | Settings | File Templates.
 */
@Service
public class SettingSearchServiceImpl
        extends ServiceImpl<SirmSettingMapper, SirmSetting>
        implements ISettingSearchService {

    @Override
    public List<SettingSaveDTO> findSettings(Map<String, String> params) {
        String module = StringUtil.safeToString(params.get("module"), null);
        String name = StringUtil.safeToString(params.get("name"), null);
        String searchname = StringUtil.safeToString(params.get("searchname"), null);

        SettingSearchDTO settingSearchDTO = new SettingSearchDTO();
        if (!"".equals(module) && null != module) {
            settingSearchDTO.setModule(module);
        }
        if (!"".equals(name) && null != name) {
            settingSearchDTO.setName(name);
        }
        if (StringUtils.isNotBlank(searchname)) {
            settingSearchDTO.setSearchname(searchname);
        }
        List<SettingSaveDTO> mapList = this.getBaseMapper().searchSettings(settingSearchDTO);
        return mapList;
    }

    @Override
    public IPage<SettingSaveDTO> searchSettings(IPage<Void> page, SettingSearchDTO params) {
        String name = params.getName();
        if (!"".equals(name) && null != name) {
            params.setName(name);
        }
        String searchname = params.getSearchname();
        if (StringUtils.isNotBlank(searchname)) {
            params.setSearchname(searchname);
        }
        return this.getBaseMapper().searchSettings(page, params);
    }

}
