package com.sinitek.sirm.common.attachment.support;

import com.sinitek.sirm.common.attachment.dto.UploaderResultDTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.attachment.vo.AttachmentStoreVO;
import com.sinitek.sirm.framework.dto.ChunkUploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 定义附件不同存储下公共的操作
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
public interface IAttachmentCustomStore {

    /**
     * 获取临时上传文件流
     * @param uploadFileDTO
     * @return
     */
    InputStream getTempUploadFile(UploadFileDTO uploadFileDTO);

    /**
     * 上传文件到临时目录下
     * @param file 文件
     * @param fileSize 允许上传附件大小
     * @param suffixes 文件扩展名
     * @return 文件名
     */
    String uploadFile(MultipartFile file, String fileSize, String suffixes);

    /**
     * 上传分片文件检查
     * @param chunk
     * @return 已存在分片文件的编号
     */
    UploaderResultDTO uploadChunkFileCheck(ChunkUploadDTO chunk);

    /**
     * 上传分片文件
     * @param chunk
     * @return
     */
    UploaderResultDTO uploadChunkFile(ChunkUploadDTO chunk);

    /**
     * 定时删除临时文件，默认每天执行一次
     */
    void deleteTempFileTask();

    /**
     * 保存附件
     * @param attachmentContent
     * @param attachmentStoreVO
     */
    void setStoreContent(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO);

    /**
     * 获取附件
     *所有自定义方式里面，只有把内容存到数据库的时候，需要判断从那个表取，其他实现都无所谓
     * @param attachment
     * @param attachmentStoreVO
     * @return
     */
    InputStream getStoreContent(Attachment attachment, AttachmentStoreVO attachmentStoreVO);

    /**
     * 删除附件
     * @param attachmentContent
     * @param attachmentStoreVO
     */
    void removeStoreContent(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO);

    /**
     * 检查文件是否存在，不取附件正文
     * @param attachmentContent            附件属性
     * @param attachmentStoreVO     附件保存信息
     * @return                      是否存在附件
     */
    boolean checkFileExist(AttachmentContent attachmentContent, AttachmentStoreVO attachmentStoreVO);
}
