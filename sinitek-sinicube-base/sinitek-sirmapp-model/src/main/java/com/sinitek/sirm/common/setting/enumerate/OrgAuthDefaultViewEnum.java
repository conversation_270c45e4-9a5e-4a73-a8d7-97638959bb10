package com.sinitek.sirm.common.setting.enumerate;

import org.apache.commons.lang3.StringUtils;

/**
 * 默认授权视图枚举
 */
public enum OrgAuthDefaultViewEnum {

    /**
     * 角色视图
     */
    ROLE("role","角色视图[默认]"),

    /**
     * 组织结构视图
     */
    ORG("org","组织结构视图");

    private String value;
    private String desc;

    OrgAuthDefaultViewEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static OrgAuthDefaultViewEnum getViewEnumByValue(String value) {
        for (OrgAuthDefaultViewEnum viewEnum : OrgAuthDefaultViewEnum.values()) {
            if (StringUtils.equals(viewEnum.getValue(), value)) {
                return viewEnum;
            }
        }
        return OrgAuthDefaultViewEnum.ROLE;
    }

    public String getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }

}
