package com.sinitek.sirm.common.sirmenum.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.sirmenum.dto.EnumListDTO;
import com.sinitek.sirm.common.sirmenum.entity.SirmEnum;
import com.sinitek.sirm.common.sonar.IgnoreMapCheck;
import com.sinitek.sirm.sirmenum.dto.TypeCataLogDTO;

import java.util.List;
import java.util.Map;

/**
 * File Desc:
 * Product Name: SIRM
 * Module Name: commonapi
 * Author:      刘建
 * History:     11-5-9 created by 刘建
 */
public interface ISirmEnumService {
    /**
     * 根据输入的模块和数据类型查询枚举数据
     * @param cataLog 模块
     * @param type    数据类型
     * @return
     * 返回相应的数据
     */
    @IgnoreMapCheck
    public Map<String, String> getSirmEnumMapByCataLogAndType(String cataLog, String type);

    /**
     * 获取枚举名称
     * @param cataLog 模块
     * @param type    数据类型
     * @param value   值
     * @return
     * 若存在，返回列表中的第一个值
     * 若不存在，返回null
     */
    public SirmEnum getSirmEnum(String cataLog, String type, Integer value);

    /**
     * 获取枚举对象
     * @param cataLog　模块
     * @param type　数据类型
     * @param strValue　值
     * @return 枚举对象
     */
    public SirmEnum getSirmEnum(String cataLog, String type, String strValue);

    /**
     * 根据类型获取枚举名称
     * @param typeCataLogDTOList  type、catalog对象
     * @return
     * 返回相应的枚举信息
     */
    public List<SirmEnum> findSirmEnum(List<TypeCataLogDTO> typeCataLogDTOList);

    /**
     * 查询枚举表
     * @return
     * 若存在，返回相应的枚举信息
     * 若不存在，返回null
     */
    public IPage<EnumListDTO> searchSirmEnum(EnumListDTO enumListDTO);

    /**
     * 按条件查询枚举列表
     * @param enumListDTO
     * @return
     */
    List<EnumListDTO> findSirmEnumList(EnumListDTO enumListDTO);

    /**
     * 保存枚举表信息
     * @param sirmEnum
     * @return
     * 返回保存之后的枚举id
     */
    public Long saveSirmEnum(SirmEnum sirmEnum);

    /**
     * 删除枚举表信息
     * @param objid　
     */
    public void delSirmEnumById(Long objid);

    /**
     * 根据catalog和type查询枚举
     * @param catalog　模块
     * @param type　数据类型
     * @return
     * 若存在，返回相应的枚举表
     */
    public List<SirmEnum> findSirmEnumByCatalogAndType(String catalog, String type);

    /**
     * 根据objid得到枚举信息
     * @param objid
     * @return
     * 返回相应的枚举信息
     */
    public SirmEnum getSirmEnumById(Long objid);

    /**
     * 清除enumCache, cataLog、type,value 一组key
     * @param cataLog
     * @param type
     * @param value
     */
    public void evictEnumCacheWithCataLogAndTypeAndValue(String cataLog, String type, Integer value);

    /**
     * 清除enumCache, cataLog、type 一组key
     * @param cataLog
     * @param type
     */
    public void evictEnumCacheWithCataLogAndType(String cataLog, String type);

    /**
     * 清除enumCache
     * @param cataLog
     * @param type
     * @param value
     */
    public void evictEnumCache(String cataLog, String type, Integer value);
}
