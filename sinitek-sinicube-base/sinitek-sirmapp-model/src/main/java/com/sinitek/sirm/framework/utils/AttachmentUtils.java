package com.sinitek.sirm.framework.utils;

import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.listener.AttachmentListener;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.attachment.support.AttachmentSupport;
import com.sinitek.sirm.common.attachment.support.IAttachmentCustomStore;
import com.sinitek.sirm.common.encryption.algorithm.asymmetric.IAsymmetricEncryption;
import com.sinitek.sirm.common.setting.utils.CommonSettingUtil;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.*;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.service.IUploaderService;
import com.sinitek.sirm.framework.support.CommonMessageCode;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 附件工具类
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class AttachmentUtils {

    /**
     * 根据附件的密文id克隆Attachment
     * @param encId
     * @return
     */
    public static Attachment copyAttachmentByEncId(String encId) {
        Long decryptId = NumberTool.safeToLong(IdEncryptUtil.decrypt(encId), 0L);
        return copyAttachment(decryptId);
    }

    /**
     * 获得 IAttachmentService
     * @return
     */
    public static IAttachmentService getAttachmentService () {
        return SpringFactory.getBean(IAttachmentService.class);
    }

    /**
     * 根据附件的明文id克隆Attachment
     * @param id
     * @return
     */
    public static Attachment copyAttachment(Long id) {
        if (IdUtil.isNotDataId(id)) {
            return null;
        }
        Attachment attachment = getAttachmentService().getAttachmentById(id);
        if (attachment == null) {
            return null;
        }
        return attachment.cloneNewAttachment();
    }

    /**
     * 保存文件列表
     * @param uploadDto 文件接收dto
     * @param sourceId  来源id
     * @param sourceEntity 来源实体
     */
    public static List<Attachment> saveAttachmentList(UploadDTO uploadDto, Long sourceId, String sourceEntity) {
        IAsymmetricEncryption asymmetricEncryption = SpringFactory.getBean(IAsymmetricEncryption.class);
        Long tempSourceId = sourceId;
        boolean forcedGenerationMd5 = uploadDto.isForcedGenerationMd5();
        AttachmentListener attachmentListener = SpringFactory.getBean(AttachmentListener.class);

        List<UploadFileDTO> removeFileList = uploadDto.getRemoveFileList();
        // 操作页面删除的附件
        if (CollectionUtils.isNotEmpty(removeFileList)) {
            for (UploadFileDTO removeUploadFile : removeFileList) {
                String id = removeUploadFile.getId();
                if (StringUtils.isBlank(id)) {
                    continue;
                }
                Long decryptId = NumberTool.safeToLong(IdEncryptUtil.decrypt(id), 0L);
                // 删除页面保存过的附件
                if (decryptId != 0) {
                    if (removeUploadFile.getRemoveFlag() != null && removeUploadFile.getRemoveFlag() == 1) {
                        getAttachmentService().removeAttachment(decryptId, sourceEntity, sourceId);
                    }
                }
            }
        }

        List<UploadFileDTO> fileList = uploadDto.getUploadFileList();
        // 保存附件
        if (CollectionUtils.isEmpty(fileList)) {
            return new ArrayList<>();
        }
        int fileSize = fileList.size();
        Integer attachUploadMaxSize = CommonSettingUtil.getAttachUploadMaxSize();

        if (null != attachUploadMaxSize && attachUploadMaxSize > 0) {
            if (fileSize > attachUploadMaxSize) {
                throw new BussinessException(CommonMessageCode.SAVE_FILES_FAIL, attachUploadMaxSize);
            }
        }

        IUploaderService uploaderService = SpringFactory.getBean(IUploaderService.class);

        List<Attachment> resultList = new ArrayList<>();
        for (UploadFileDTO uploadFile : fileList) {
            String id = uploadFile.getId();
            //未保存过的
            if (StringUtils.isBlank(id)) {
                //未保存过的
                String name = StringUtil.safeToString(uploadFile.getName(), "");
                if (StringUtils.isBlank(name)) {
                    throw new BussinessException(CommonMessageCode.FILE_NAME_CAN_NOT_NULL);
                }

                String fileExt = FilenameUtils.getExtension(name);
                uploaderService.checkFileSuffix(name, null);

                Attachment attachment = new Attachment();
                try {
                    attachment.setName(FilenameUtils.getBaseName(name));
                    attachment.setFileType(fileExt);
                } catch (Exception e) {
                    log.error("文件名错误,请输入正确的文件名[如: 文档信息.doc],attachment:{}",attachment,e);
                    throw new BussinessException(CommonMessageCode.FILE_NAME_MISTAKEN);
                }
                attachment.setType(uploadFile.getType());
                // 这里不需要设置storetype，在AttachmentMO中有逻辑判断
                attachment.setConvertStatus(0);
                attachment.setContentSize(NumberTool.safeToLong(uploadFile.getSize(), 0L));
                attachment.setSourceEntity(sourceEntity);
                attachment.setSourceId(tempSourceId);
                attachment.setOwnerId(CurrentUserFactory.getOrgId());

                String md5;
                if (forcedGenerationMd5) {
                    IAttachmentCustomStore currentAttachmentCustomStore = attachmentListener.getCurrentAttachmentCustomStore();
                    InputStream tempUploadFile = currentAttachmentCustomStore.getTempUploadFile(uploadFile);
                    md5 = EncryptUtil.getFileMD5(tempUploadFile);

                } else {
                    md5 = uploadFile.getMd5();
                    if (StringUtils.isNotBlank(md5)) {
                        md5 = asymmetricEncryption.decryptByPrivateKey(md5);
                    }
                }

                String responseId = uploadFile.getResponseId();
                attachment.setTempFileStorePath(responseId);
                attachment.setTempFileMd5(md5);

                getAttachmentService().saveAttachment(attachment);
                resultList.add(attachment);
            } else {
                Long decryptId = NumberTool.safeToLong(IdEncryptUtil.decrypt(id), 0L);
                if (decryptId == 0) {
                    continue;
                }
                Attachment attachment = getAttachmentService().getAttachmentById(decryptId);
                if (attachment == null) {
                    continue;
                }
                attachment.setOwnerId(CurrentUserFactory.getOrgId());
                attachment.setType(uploadFile.getType());
                attachment.setSourceEntity(sourceEntity);
                attachment.setSourceId(tempSourceId);
                getAttachmentService().updateAttachment(attachment);
                resultList.add(attachment);
            }
        }

        return resultList;
    }

    /**
     * 保存Attachment
     * @param origFile 文件
     * @param sourceId 资源id
     * @param sourceEntity 资源实体名称
     */
    public static void saveAttachment(File origFile, Long sourceId, String sourceEntity, int type) {
        saveAttachment("", origFile, sourceId, sourceEntity, type);
    }

    /**
     * 保存Attachment
     * @param origFile 文件
     * @param sourceId 资源id
     * @param sourceEntity 资源实体名称
     */
    public static void saveAttachment(String fileName, File origFile, Long sourceId, String sourceEntity, int type) {
        saveAttachment(fileName, origFile, sourceId, sourceEntity, type, null);
    }

    /**
     * 保存Attachment
     * @param origFile 文件
     * @param sourceId 资源id
     * @param sourceEntity 资源实体名称
     */
    public static void saveAttachment(String fileName, File origFile, Long sourceId, String sourceEntity, int type, String ownerId) {
        Attachment attachment = new Attachment();
        InputStream inputStream = IOUtil.toInputStream(origFile);

        String name = "";
        if (StringUtils.isNotBlank(fileName)) {
            name = fileName;
        } else {
            name = StringUtil.safeToString(origFile.getName(), "");
        }
        attachment.setName(FilenameUtils.getBaseName(name));
        attachment.setFileType(FilenameUtils.getExtension(name));
        attachment.setContent(inputStream);
        attachment.setType(type);
        // 这里不需要设置storetype，在AttachmentMO中有逻辑判断
        attachment.setConvertStatus(0);
        attachment.setContentSize(origFile.length());
        attachment.setSourceEntity(sourceEntity);
        attachment.setSourceId(sourceId);

        if (StringUtils.isNotBlank(ownerId)) {
            attachment.setOwnerId(ownerId);
        } else {
            attachment.setOwnerId(CurrentUserFactory.getOrgId());
        }
        getAttachmentService().saveAttachment(attachment);
    }


    /**
     * 保存头像
     * @param uploadDto 文件接收dto
     * @param sourceId  来源id
     * @param sourceEntity 来源实体
     */
    @SuppressFBWarnings(value = "PATH_TRAVERSAL_IN", justification = "该方法的路径已进行后端加密,所以可信任")
    public static void saveAvatar(UploadDTO uploadDto, Long sourceId, String sourceEntity) {
        IAttachmentService attachmentService = getAttachmentService();
        if (uploadDto != null && uploadDto.getType() != null && sourceId != null && sourceEntity != null) {
            //先删除已保存的头像
            if (CollectionUtils.isNotEmpty(uploadDto.getUploadFileList())) {
                List<Attachment> attachments =  attachmentService.findAttachmentList(sourceEntity, sourceId, uploadDto.getType());
                if (CollectionUtils.isNotEmpty(attachments)) {
                    List<Long> idList = attachments.stream().map(Attachment::getObjId).collect(Collectors.toList());
                    attachmentService.removeAttachmentList(idList);
                }
            }

            List<UploadFileDTO> removeFileList = uploadDto.getRemoveFileList();
            // 操作页面删除的附件
            if (CollectionUtils.isNotEmpty(removeFileList)) {
                for (UploadFileDTO removeUploadFile : removeFileList) {
                    String id = removeUploadFile.getId();
                    if (StringUtils.isBlank(id)) {
                        continue;
                    }
                    Long decryptId = NumberTool.safeToLong(IdEncryptUtil.decrypt(id), 0L);
                    // 删除页面保存过的附件
                    if (IdUtil.isDataId(decryptId)) {
                        getAttachmentService().removeAttachment(decryptId, sourceEntity, sourceId);
                    }
                }
            }

            for (UploadFileDTO uploadFile : uploadDto.getUploadFileList()) {
                if (uploadFile != null && uploadFile.getAvatarUrl() != null) {
                    String avatarUrl = uploadFile.getAvatarUrl();
                    //保存用户头像信息
                    if (StringUtils.isNotBlank(avatarUrl)) {
                        File file = new File(avatarUrl);
                        String name = StringUtil.safeToString(file.getName(), "");
                        if (file.exists()) {
                            //先删除原有头像
                            Attachment attachment = new Attachment();
                            //封装属性
                            attachment.setSourceEntity(sourceEntity);
                            attachment.setSourceId(sourceId);
                            attachment.setName(name);
                            attachment.setType(uploadFile.getType());
                            attachment.setPageCount(1L);
                            attachment.setConvertStatus(0);
                            attachment.setDigest("");
                            attachment.setStorePath("");
                            attachment.setFileType(FilenameUtils.getExtension(name));
                            attachment.setConvertResult(1);
                            attachment.setSendFlag(0);
                            attachment.setConvertFlag(0);
                            attachment.setContentSize(file.length());


                            // 附件内容
                            InputStream inputStream = IOUtil.toInputStream(file);
                            attachment.setContent(inputStream);
                            attachmentService.saveAttachment(attachment);
                        }
                    }
                }
            }
        }
    }

    public static InputStream getAttachStream(Attachment attachment){
        if(attachment==null){ return null;}
        AttachmentSupport attachmentSupport = SpringFactory.getBean(AttachmentSupport.class);
        return attachmentSupport.getContent(attachment);
    }

    /**
     * 根据来源实体id集合和来源实体名称获取附件数量
     * @param sourceIdList 来源实体id集合
     * @param sourceEntity 来源实体名称
     * @return key:sourceId(来源实体id), value: count(附件数量)
     */
    public static Map<Long, Integer> getAttachmentCountBySourceIdListAndSourceEntity(List<Long> sourceIdList, String sourceEntity) {
        return SpringFactory.getBean(IAttachmentService.class).getAttachmentCountBySourceIdListAndSourceEntity(sourceIdList, sourceEntity);
    }
}
