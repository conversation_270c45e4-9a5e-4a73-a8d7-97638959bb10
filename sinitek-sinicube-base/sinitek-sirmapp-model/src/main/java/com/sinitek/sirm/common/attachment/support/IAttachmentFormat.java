package com.sinitek.sirm.common.attachment.support;

import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.support.CommonMessageCode;

public interface IAttachmentFormat {

    /**
     * 提供给项目组，重新设置attchment的值，以引用对象返回
     * @param attachment 需要设值的附件对象
     */
    default Attachment reSetAttachment(Attachment attachment){
        throw new RuntimeException(RequestResult.getMessage(CommonMessageCode.CURRENT_METHOD_NO_IMPLEMENT));
    }
}
