package com.sinitek.sirm.common.setting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.setting.dto.SettingSaveDTO;
import com.sinitek.sirm.common.setting.dto.SettingSearchDTO;
import com.sinitek.sirm.common.setting.entity.SirmSetting;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SirmSetting)表数据库访问层
 *
 * <AUTHOR>
 * @date 2019-11-29 15:43:00
 */
public interface SirmSettingMapper extends BaseMapper<SirmSetting> {

    /**
     * 获得所有模块的分类
     * @return
     */
    List<String> findSettingModule();

    /**
     * 根据参数查询setting信息
     * @param searchDTO
     * @return
     */
    @SuppressWarnings("squid:SearchMethodReturnCheck")
    List<SettingSaveDTO> searchSettings(@Param("params") SettingSearchDTO searchDTO);

    /**
     * 根据参数查询setting信息
     * @param searchDTO
     * @return
     */
    IPage<SettingSaveDTO> searchSettings(IPage<Void> page, @Param("params") SettingSearchDTO searchDTO);
}
