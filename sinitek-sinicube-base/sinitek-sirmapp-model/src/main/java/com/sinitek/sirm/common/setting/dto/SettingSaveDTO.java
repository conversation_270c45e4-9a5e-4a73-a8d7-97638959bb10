package com.sinitek.sirm.common.setting.dto;

import com.sinitek.sirm.common.setting.message.SettingMessageCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * Created by user on 2018/5/3.
 */
@Data
@Schema(description = "参数新增/修改模型")
public class SettingSaveDTO {
    @Schema(description = "主键id")
    private Long id = 0L;

    @Schema(description = "objid")
    private Long objid;

    @NotBlank(message = "{" + SettingMessageCode.MODULE_NAME_CAN_NOT_NULL + "}")
    @Length(max = 30, message = "{"+ SettingMessageCode.MODULE_NAME_LENGTH_TOO_BIG +"}")
    @Schema(description = "模块名称")
    private String module = null;

    @NotBlank(message = "{" + SettingMessageCode.NAME_CAN_NOT_NULL + "}")
    @Length(max = 30, message = "{"+ SettingMessageCode.NAME_LENGTH_TOO_BIG +"}")
    @Schema(description = "参数名称")
    private String name = null;

    @Length(max = 600, message = "{"+ SettingMessageCode.VALUE_LENGTH_TOO_BIG +"}")
    @Schema(description = "参数值")
    private String value = null;

    @Schema(description = "是否加密")
    private int encryptionFlag = 0;

    @Length(max = 200, message = "{"+ SettingMessageCode.DESC_LENGTH_TOO_BIG +"}")
    @Schema(description = "参数说明")
    private String brief = null;

}
