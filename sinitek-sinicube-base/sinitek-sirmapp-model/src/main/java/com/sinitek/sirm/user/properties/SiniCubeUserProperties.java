package com.sinitek.sirm.user.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * date 2022/6/28
 */
@Data
@Component
@Schema(description = "用户业务相关参数配置")
@ConfigurationProperties(prefix = "sinicube.user")
public class SiniCubeUserProperties {

    private String userDefaultDataSrc = "系统" ;

    /**
     * 用户强制修改密码状态时，可以访问的接口
     */
    private List<String> pwdForcedModificationAllowUrlList;

    /**
     * 身份验证限制只使用Cookie
     */
    private boolean onlyCookieCheck = false;

    /**
     * 获取全部用户强制修改密码-系统同意请求的接口
     * @return
     */
    public List<String> getAllPwdAllowUrlList() {
        List<String> allowUrlList = getDefaultPwdAllowUrlList();
        if (CollectionUtils.isNotEmpty(pwdForcedModificationAllowUrlList)) {
            allowUrlList.addAll(pwdForcedModificationAllowUrlList);
        }
        return allowUrlList;
    }

    /**
     * 用户强制修改密码-系统内置同意请求的接口（仅独立修改密码页面用到的接口）
     * @return
     */
    public List<String> getDefaultPwdAllowUrlList() {
        List<String> pwdAllowUrlList = new ArrayList<>();
        pwdAllowUrlList.add("/frontend/api/common/encrypt/encrypt-global-config");
        pwdAllowUrlList.add("/frontend/api/user/check");
        pwdAllowUrlList.add("/frontend/api/user/updatepwd");

        // 微服务-网关的调用
        pwdAllowUrlList.add("/frontend/api/org/currentuser");
        pwdAllowUrlList.add("/frontend/api/remote-with-interceptor/function/check-user-api");
        return pwdAllowUrlList;
    }
}
