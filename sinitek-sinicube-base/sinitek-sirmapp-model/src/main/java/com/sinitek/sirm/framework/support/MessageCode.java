package com.sinitek.sirm.framework.support;

/**
 * Created by IntelliJ IDEA.
 * User: 王志华
 * Date: 2018/2/20
 * Time: 下午10:08
 * To change this template use File | Settings | File Templates.
 */
public class MessageCode {

    private MessageCode() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 用户会话已失效
     */
    public static final String SESSIONINVALID = "010110";

    /**
     * 用户会话验证成功
     */
    public static final String SESSIONSUCCESS = "010111";

    /**
     * 用户名或密码错误
     */
    public static final String INVALIDUSER = "010120";

    /**
     * 用户被锁住
     */
    public static final String LOCKUSER = "010121";

    /**
     * 用户已离职
     */
    public static final String OUTSERVICEUSER = "010122";

    /**
     * LDAP服务器连接出错
     */
    public static final String LDAPURLERROR = "010123";

    /**
     * 用户在系统中不存在
     */
    public static final String NOSUCHUSER = "010124";

    /**
     * 通用登录错误
     */
    public static final String LOGINERROR = "010125";

    /**
     * application.properties中的参数配置错误
     */
    public static final String PARAMERROR = "010126";

    /**
     * 验证码错误Captcha
     */
    public static final String CAPTCHAERROR = "010127";

    /**
     * 密码安全性策略数据异常
     */
    public static final String PASSSECURITYERROR = "010128";

    /**
     * 验证码已过期,请刷新
     */
    public static final String CHECK_CODE_EXPIRE = "010129";

    /**
     * 用户名：{0}，在职状态填写有误，请填写（在职/离职）
     */
    public static final String INSERVICE_INPUT_ERROR = "010130";

    /**
     * 用户已到期
     */
    public static final String USER_EXPIRE = "010135";

    /**
     * 用户没有所属机构
     */
    public static final String USER_NO_BELONG_TO_TENANT = "010136";

    /**
     * 用户所属机构已被删除
     */
    public static final String USER_BELONG_TO_TENANT_DELETED = "010137";

    /**
     * 用户所属机构已被禁用
     */
    public static final String USER_BELONG_TO_TENANT_DISABLED = "010138";

    /**
     * 用户所属机构不存在
     */
    public static final String CHECK_LOGIN_MISSING_TENANT = "010139";

    /**
     * 生成密钥对失败
     */
    public static final String GEN_RSAKEYPAIR_FAILED = "010150";

    /**
     * 未知错误
     */
    public static final String UNKNOW = "010199";


    /**
     * 获取用户失败
     */
    public static final String USER_GETUSERFAIL = "010201";

    /**
     * 用户名已存在
     */
    public static final String USER_USERNAMECONFLICT = "010202";

    /**
     * 权限不足
     */
    public static final String USER_INSUFFICIENTPRIVILEGES = "010203";

    /**
     * 用户无需解锁
     */
    public static final String USER_NONEEDUNLOCK = "010204";

    /**
     * 用户导入失败
     */
    public static final String USER_IMPORTFAIL = "010205";

    /**
     * 行政上级不能设置为本人
     */
    public static final String USER_NOTMYSELF = "010206";

    /**
     * 行政上级不能设置为本人下属员工
     */
    public static final String USER_NOTSUBORDINATE = "010207";

    /**
     * 已有行政上级，不可重复设置
     */
    public static final String USER_NOTREPEAT = "010208";

    /**
     * 已经为其行政上级，不能设置为其为下属员工
     */
    public static final String USER_ALREADYSUPERIOR = "010209";

    /**
     * 行政上级人员错误
     */
    public static final String USER_SUPERIORERROR = "010210";

    /**
     * 部门名已存在
     */
    public static final String ORG_UNITNAMECONFLICT = "010211";

    /**
     * 岗位名已存在
     */
    public static final String ORG_POSITIONNAMECONFLICT = "010212";

    /**
     * 小组名已存在
     */
    public static final String ORG_TEAMNAMECONFLICT = "010213";

    /**
     * 删除组织结构失败
     */
    public static final String ORG_DELETEORGFAIL = "010214";

    /**
     * 小组内只能有一个组长
     */
    public static final String ORG_ONETEAMLEADER = "010215";

    /**
     * 不能移动到根节点下
     */
    public static final String ORG_CANNOTMOVE = "010216";

    /**
     * 移动的目标位置已有同名部门
     */
    public static final String ORG_MOVEUNITCONFLICT = "010217";

    /**
     * 部门只能移动到部门下
     */
    public static final String ORG_MOVEUNITLIMIT = "010218";

    /**
     * 移动的目标位置已有同名岗位
     */
    public static final String ORG_MOVEPOSTCONFLICT = "010219";

    /**
     * 岗位只能移动到部门下
     */
    public static final String ORG_MOVEPOSTLIMIT = "010220";

    /**
     * 员工已经存在于目标位置
     */
    public static final String ORG_MOVEEMPCONFLICT = "010221";

    /**
     * 员工只能移动到岗位、角色、小组下
     */
    public static final String ORG_MOVEEMPLIMIT = "010222";

    /**
     * 方案名称已存在
     */
    public static final String ORG_SCHEMECONFLICT = "010223";

    /**
     * 下级节点不允许选择本节点
     */
    public static final String ORG_CHILDNOTBESELF = "010224";

    /**
     * 不得选择子类节点为父类节点
     */
    public static final String ORG_CHILDNOTBEPARENT = "010225";

    /**
     * 父类节点错误
     */
    public static final String ORG_PARENTERROR = "010226";

    /**
     * 当前用户密码错误
     */
    public static final String USER_PD_ERROR = "010227";

    /**
     * 角色名已存在
     */
    public static final String ORG_ROLENAMECONFLICT = "010228";

    /**
     * =保存{0}失败:{1}
     */
    public static final String ORG_SAVETEAMEMPFAIL = "010229";

    /**
     * {orgNames}已存在上下级关系，不能重复设置
     */
    public static final String ORG_ADDRELATIONCONFLICT = "010230";

    /**
     * 组织结构上下级方案Id[{schemeId}]对应的方案信息不存在
     */
    public static final String ORGSCHEMEINFO_NOTEXIST = "010231";


    /**
     * 组织结构左右值未初始化
     */
    public static final String ORG_TREE_VALUE_NOTINIT = "010232";

    /**
     * 不能移动到根节点前后
     */
    public static final String ORG_CANNOTMOVE_ROOT_PRE_OR_AFT = "010233";


    /**
     * 删除部分枚举项目失败
     */
    public static final String ENUM_DELITEMSFAILED = "010611";
    /**
     * 枚举类型名称重复
     */
    public static final String ENUM_SAVEFAILED1 = "010612";
    /**
     * 枚举类型取值要保持一致，原来取值为整型
     */
    public static final String ENUM_SAVEFAILED2 = "010613";
    /**
     * 整型值取值重复
     */
    public static final String ENUM_SAVEFAILED3 = "010614";
    /**
     * 枚举类型取值要保持一致，原来取值为字符串
     */
    public static final String ENUM_SAVEFAILED4 = "010615";
    /**
     * 字符串值取值重复
     */
    public static final String ENUM_SAVEFAILED5 = "010616";

    /**
     * 菜单id[{objid}]对应的菜单不存在
     */
    public static final String MENU_NOTEXIST = "010618";

    /**
     * 超过附件大小配置
     */
    public static final String ATTACH_OUT_MAXSIZE = "010633";

    /**
     * 不支持的文件类型
     */
    public static final String ATTACH_UNSUPPORT_SUFFIX = "010634";

    /**
     * 请确认是否支持类型为【{0}】的文件
     */
    final public static String ATTACH_FRONTEND_UNSUPPORT_SUFFIX = "010635";

    /**
     * 不支持文件类型【{0}】的使用，请在公共参数中进行配置
     */
    final public static String ATTACH_BACKEND_UNSUPPORT_SUFFIX = "010636";

    /**
     * 系统支持的文件类型未配置，请在公共参数中进行相关配置
     */
    final public static String ATTACH_SYS_UNSUPPORT_EMPTY = "010637";


    public static final String WF_NOSTEPTOJUMP = "010413";

    //消息模板错误
    /**
     * 导入时发生意外错误
     */
    public static final String MESSAGE_IMPORTFAIL1 = "010312";
    /**
     * 没有找到上传的文件
     */
    public static final String MESSAGE_IMPORTFAIL3 = "010314";

    /**
     * 用户导入的文件为空
     */
    public static final String USER_IMPORTEMPTY = "010329";

    /**
     * 行政上级不能设置为本人:{details}
     */
    public static final String USER_NOTMYSELFDETAIL = "010330";

    /**
     * 已有行政上级，不可重复设置:{details}
     */
    public static final String USER_NOTREPEATDETAIL = "010331";

    /**
     * 已经为其行政上级，不能设置为其为下属员工:{details}
     */
    public static final String USER_ALREADYSUPERIORDETAIL = "010332";

    /**
     * 不能将离职人员设置为小组长
     */
    public static final String INSERVICE_USER_CAN_NOT_BE_TEAMER = "010333";

    /**
     * 不能选中离职状态的用户
     */
    public static final String INSERVICE_USER_CAN_NOT_BE_SELECT = "010334";

    /**
     * 方案代码已存在
     */
    public static final String ORG_SCHEME_CODE_ALREADY_EXISTS = "010335";

    /**
     * 参数验证失败
     */
    public static final String PARAM_FIAL = "010200";


    /**
     * LDAP用户账号或者密码错误
     */
    public static final String LDAP_INVALID_CREDENTIALS = "010151";

    /**
     * 获取LdapContext出现异常
     */
    public static final String LDAP_GET_CONTEXT_ERROR = "010152";

    /**
     * 无法获取LDAP目录，请确认查询根目录配置是否正确或者是否拥有LDAP查询权限
     */
    public static final String LDAP_NO_SUCH_OBJECT = "010153";

    /**
     * 查询LDAP用户出现异常
     */
    public static final String LDAP_SEARCH_USER_ERROR = "010154";

    /**
     * 用户过滤表达式【{0}】未查詢到用户
     */
    public static final String LDAP_USER_NOT_FOUND = "010155";

    /**
     * 用户过滤表达式【{0}】查詢到多个用户
     */
    public static final String LDAP_MORE_THEN_ONE_USER_BE_FOUND = "010156";

    /**
     * 用户属性中不存在【{0}】
     */
    public static final String LDAP_DISPLAYNAME_NOT_FOUND = "010157";

    /**
     * 获取LDAP用户姓名出现异常
     */
    public static final String LDAP_GET_DISPLAYNAME_ERROR = "010158";

    /**
     * 错误的用户过滤表达式
     */
    public static final String LDAP_INVALID_FILTER = "010159";


    /**
     * 用户身份验证失败
     */
    public static final String USER_IDENTITY_CHECK_FAILED = "010140";

    /**
     * 用户会话验证失败
     */
    public static final String USER_SESSION_CHECK_FAILED = "010141";

    /**
     * 未找到该身份验证的自定义实现类
     */
    public static final String USER_IDENTITY_CUSTOM_CLASS_NOT_FOUND = "010142";

    /**
     * 验证成功，但未获取到当前登陆人
     */
    public static final String CURRENT_USER_NOT_FOUND = "010143";

    /**
     * 用户密码解密失败
     */
    public static final String USER_PASSWORD_DECRYPT_FAIL = "010144";

    /**
     * 用户所属机构已过期
     */
    public static final String CHECK_LOGIN_BELONG_TO_TENANT_EXPIRED = "010145";

    /**
     * 移动的目标位置已经存在该上下级关系
     */
    public static final String EXISTING_PARENT_CHILD_RELATION = "010146";
}
