package com.sinitek.sirm.common.attachment.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SiniCube 集成Minio的属性配置类
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
@Component
@ConfigurationProperties(prefix = "sinicube.minio")
public class SiniCubeMinioProperties {

    private String endpoint;

    private int port;

    private String accessKey;

    private String secretKey;

    private Boolean secure;

    private String bucketName;

    private String configDir = "/sinicube";

    private String tempDir = "/temp/uploadDir";

    /**
     * 指定存储桶（bucket）的区域设置, 例如：cn-shanghai
     */
    private String region;
}
