package com.sinitek.sirm.common.setting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.setting.dto.SettingSaveDTO;
import com.sinitek.sirm.common.setting.dto.SettingSearchDTO;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: 王志华
 * Date: 12-2-9
 * Time: 上午9:51
 * To change this template use File | Settings | File Templates.
 */
public interface ISettingSearchService {
    /**
     * 查询配置信息
     * @param params
     * module:String,
     * name:String
     * @return
     * 返回相应的配置信息
     */
    public List<SettingSaveDTO> findSettings(Map<String, String> params);

    IPage<SettingSaveDTO> searchSettings(IPage<Void> page, SettingSearchDTO params);

}
