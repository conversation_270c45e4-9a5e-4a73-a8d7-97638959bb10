package com.sinitek.sirm.common.setting.enumerate;

/**
 * 菜单模式枚举
 */
public enum MenuModeEnum {

    /**
     * 顶部横向
     */
    HORIZONTAL("horizontal","顶部横向"),

    /**
     * 左侧垂直
     */
    VERTICAL("vertical","左侧垂直"),

    /**
     * 混合
     */
    MIX("mix","混合");

    private String value;
    private String desc;

    MenuModeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static MenuModeEnum getModeEnumByValue(String value) {
        for (MenuModeEnum modeEnum : MenuModeEnum.values()) {
            if (modeEnum.getValue().equals(value)) {
                return modeEnum;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }
}
