package com.sinitek.sirm.common.attachment.support;

import com.sinitek.sirm.common.attachment.dto.UploaderResultDTO;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.entity.AttachmentContent;
import com.sinitek.sirm.common.encryption.algorithm.symmetry.ISymmetryEncryption;
import com.sinitek.sirm.common.encryption.algorithm.symmetry.enumerate.SymmetryEncryptionAlgorithm;
import com.sinitek.sirm.common.encryption.algorithm.symmetry.impl.AesSymmetryEncryptionImpl;
import com.sinitek.sirm.common.encryption.algorithm.symmetry.impl.SM4SymmetryEncryptionImpl;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.tempdir.support.TempDirCleaner;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.TempDirUtils;
import com.sinitek.sirm.framework.dto.ChunkUploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.service.IUploaderService;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 附件操作抽象类,封装附件加密等公共方法
 *
 * <AUTHOR>
 * @date 2019-12-18
 */
@Slf4j
@SuppressFBWarnings(value = "PATH_TRAVERSAL_IN", justification = "该类中的文件路径是从后端配置文件而非用户输入,无须处理")
public abstract class AbstractAttachmentCustomStore implements IAttachmentCustomStore {

    @Autowired
    public IUploaderService uploaderService;

    public static final String ENCRYPTION_DIR = "encryption";

    public static final String DECRYPTION_DIR = "decryption";

    @SneakyThrows
    @Override
    public InputStream getTempUploadFile(UploadFileDTO uploadFileDTO) {
        String tempSaveDir = uploaderService.getTempSaveDir();
        String filePath = Paths.get(tempSaveDir, uploadFileDTO.getResponseId()).toString();
        File file = new File(filePath);
        if (file.exists()) {
            return Files.newInputStream(file.toPath());
        }
        return null;
    }

    @Override
    public String uploadFile(MultipartFile file, String fileSize, String suffixes) {
        return uploaderService.uploadFile(file, fileSize, suffixes);
    }

    @Override
    public UploaderResultDTO uploadChunkFileCheck(ChunkUploadDTO chunk) {
        return uploaderService.checkChunk(chunk);
    }

    @Override
    public UploaderResultDTO uploadChunkFile(ChunkUploadDTO chunk) {
        return uploaderService.uploadChunk(chunk);
    }

    @Override
    public void deleteTempFileTask() {
        if (log.isDebugEnabled()) {
            log.debug("正在删除临时文件，通过默认的删除临时文件线程实现: {}", TempDirCleaner.class.getName());
        }
    }

    /**
     * 创建临时加密目录
     * @return
     */
    public File createTempEncryptionDir() {
        String tempSaveDir = uploaderService.getTempSaveDir();
        String encryptionDir = Paths.get(tempSaveDir, ENCRYPTION_DIR).toString();
        TempDirUtils.createTempDir(encryptionDir);
        return IOUtil.createTempFile("dat", encryptionDir);
    }

    /**
     * 创建临时解密目录
     * @return
     */
    public File createTempDecryptionDir() {
        String tempSaveDir = uploaderService.getTempSaveDir();
        String decryptionDir = Paths.get(tempSaveDir, DECRYPTION_DIR).toString();
        TempDirUtils.createTempDir(decryptionDir);
        return IOUtil.createTempFile("dat", decryptionDir);
    }

    /**
     * 保存附件时进行加密
     * @param inputStream 输入的流
     * @param attachmentContent 附件实体对象
     * @throws IOException
     */
    public File setStoreContentEncryption(InputStream inputStream, AttachmentContent attachmentContent) throws IOException {
        ISymmetryEncryption symmetryEncryption = SpringFactory.getBean(ISymmetryEncryption.class);
        String currentAlgorithm = symmetryEncryption.getCurrentAlgorithm();
        String encKey = symmetryEncryption.generateKey();

        // 生成临时文件，用于存储密文输出
        File tempFile = createTempEncryptionDir();
        try (OutputStream fout = Files.newOutputStream(tempFile.toPath());) {
            symmetryEncryption.encrypt(inputStream, fout, encKey);
            attachmentContent.setEncKey(encKey);
            attachmentContent.setEncAlgorithm(currentAlgorithm);
        }
        return tempFile;
    }

    /**
     * 获取附件时解密文件流
     * @param encKey
     * @param contentInputStream
     * @return
     */
    public FileInputStream getStoreContentDecryption(Attachment attachment, String encKey,InputStream contentInputStream) {
        ISymmetryEncryption symmetryEncryption = getSymmetryEncryption(attachment.getEncAlgorithm());
        File tempFile = createTempDecryptionDir();
        try (OutputStream fout = Files.newOutputStream(tempFile.toPath());) {
            symmetryEncryption.decrypt(contentInputStream, fout, encKey);
            return new FileInputStream(tempFile);
        } catch (IOException e) {
            log.error("找不到附件，attachment:{}",attachment, e);
        }
        return null;
    }

    /**
     * 解密时,根据解密算法获取对应的解密实现
     * @param encAlgorithm
     * @return
     */
    public ISymmetryEncryption getSymmetryEncryption(String encAlgorithm) {
        if (StringUtils.isBlank(encAlgorithm) || SymmetryEncryptionAlgorithm.AES.name().equalsIgnoreCase(encAlgorithm)) {
            return new AesSymmetryEncryptionImpl();
        } else if (SymmetryEncryptionAlgorithm.SM4.name().equalsIgnoreCase(encAlgorithm)){
            return new SM4SymmetryEncryptionImpl();
        } else {
            return SpringFactory.getBean(ISymmetryEncryption.class);
        }
    }

}
