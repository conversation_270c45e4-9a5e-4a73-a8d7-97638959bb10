<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.common.setting.mapper.SirmGroupsettingMapper">

    <select id="findGroupSettings" resultType="com.sinitek.sirm.common.setting.entity.SirmGroupsetting">
        select * from sirm_groupsetting  order by sort asc
    </select>

    <update id="updateGroupSettings" parameterType="com.sinitek.sirm.common.setting.entity.SirmGroupsetting">
        UPDATE sirm_groupsetting SET
        sort = #{sort}
        WHERE objid = #{id}
    </update>
</mapper>
