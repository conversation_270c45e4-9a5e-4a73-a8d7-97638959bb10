package com.sinitek.sirm.common.setting.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.MetadbBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (SirmSetting)表实体类
 *
 * <AUTHOR>
 * @date 2019-11-29 15:43:00
 */
@Data
@Schema(description = "参数配置实体")
@TableName(value = "SIRM_SETTING")
@EqualsAndHashCode(callSuper = true)
public class SirmSetting extends MetadbBaseEntity {

    public static final String ENTITY_NAME = "SETTING";

    @TableField(value = "module")
    @Schema(description = "模块名称", required = true)
    private String module;

    @TableField(value = "name")
    @Schema(description = "参数名称", required = true)
    private String name;

    @TableField(value = "value")
    @Schema(description = "参数值", required = true)
    private String value;

    @TableField(value = "brief")
    @Schema(description = "参数说明")
    private String brief;

    @TableField(value = "encryptionflag")
    @Schema(description = "是否加密")
    private Integer encryptionFlag;

    public static String getEntityNameName() {
        return ENTITY_NAME;
    }
}
