package com.sinitek.sirm.framework.properties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 子服务相关参数
 * <AUTHOR>
 * @date 2022/12/8
 */


@Data
@Component
@Schema(description = "子系统相关参数配置")
@ConfigurationProperties(prefix = "sinicube.subsystem")
public class SiniCubeSubSysProperties {

    private List<String> subFrontendAddress ;

}
