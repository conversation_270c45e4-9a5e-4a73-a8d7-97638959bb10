package com.sinitek.sirm.common.setting.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "参数查询模型")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SettingSearchDTO {

    @Schema(description = "模块名称")
    private String module;

    /**
     * 模糊查询
     */
    @Schema(description = "参数名称")
    private String name;

    @Schema(description = "参数值")
    private String value;

    @Schema(description = "参数说明")
    private String description;

    /**
     * 模糊查询
     */
    @Schema(description = "条件名称")
    private String searchname;
}
