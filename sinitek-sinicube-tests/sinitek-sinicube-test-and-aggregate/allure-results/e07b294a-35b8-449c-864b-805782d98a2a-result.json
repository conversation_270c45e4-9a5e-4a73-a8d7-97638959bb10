{"uuid": "e07b294a-35b8-449c-864b-805782d98a2a", "historyId": "bab016143d473715e0a964f8528f9621", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest]/[method:testSaveSysMenu()]", "testCaseName": "远调测试-保存菜单", "fullName": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest.testSaveSysMenu", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest]/[method:testSaveSysMenu()]"}, {"name": "feature", "value": "远调-系统菜单相关操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest"}, {"name": "testMethod", "value": "testSaveSysMenu"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest"}], "links": [], "name": "远调测试-保存菜单", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758664458, "stop": 1753758664605}