{"uuid": "ee1502d6-7a75-4deb-bffe-3e65a740ac85", "historyId": "394c590bc753a489b77b883e3ebf2f61", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest]/[method:testFindSirmEnumListBy()]", "testCaseName": "远调测试-根据枚举分类、枚举名称、枚举类型查询枚举列表", "fullName": "com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest.testFindSirmEnumListBy", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest]/[method:testFindSirmEnumListBy()]"}, {"name": "feature", "value": "远调-枚举"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest"}, {"name": "testMethod", "value": "testFindSirmEnumListBy"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest"}], "links": [], "name": "远调测试-根据枚举分类、枚举名称、枚举类型查询枚举列表", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "\r\nExpected size: 1 but was: 0 in:\r\n[]", "trace": "java.lang.AssertionError: \r\nExpected size: 1 but was: 0 in:\r\n[]\r\n\tat com.sinitek.cloud.sirmapp.enumeration.feign.SirmEnumRemoteServiceTest.testFindSirmEnumListBy(SirmEnumRemoteServiceTest.java:67)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:569)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n"}, "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758662434, "stop": 1753758662581}