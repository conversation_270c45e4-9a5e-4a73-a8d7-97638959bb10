{"uuid": "e3f30f24-ae79-4f1b-b8d6-826ffe7c4c9d", "historyId": "5e15f1e5b4be43df49148561e0277e07", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testQuartzStatusChangeStart()]", "testCaseName": "测试改变定时任务计划状态(启动)", "fullName": "com.sinitek.sirm.common.quartz.QuartzApiActionTest.testQuartzStatusChangeStart", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testQuartzStatusChangeStart()]"}, {"name": "feature", "value": "定时任务管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testMethod", "value": "testQuartzStatusChangeStart"}, {"name": "suite", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}], "links": [], "name": "测试改变定时任务计划状态(启动)", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758697830, "stop": 1753758697862}