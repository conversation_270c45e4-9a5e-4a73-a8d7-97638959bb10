{"uuid": "f7ff2576-7547-425d-871a-421d0936aa9c", "historyId": "8a2e914d4476e6c6c5b06ff9f4c8e0c6", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest]/[method:testGetEmployeeById()]", "testCaseName": "testGetEmployeeById()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest.testGetEmployeeById", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest]/[method:testGetEmployeeById()]"}, {"name": "feature", "value": "远调-组织结构-查询"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}, {"name": "testMethod", "value": "testGetEmployeeById"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}], "links": [], "name": "testGetEmployeeById()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758680221, "stop": 1753758680267}