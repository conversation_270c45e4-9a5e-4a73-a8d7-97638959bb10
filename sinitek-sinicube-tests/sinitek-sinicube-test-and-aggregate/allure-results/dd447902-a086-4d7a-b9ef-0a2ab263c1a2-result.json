{"uuid": "dd447902-a086-4d7a-b9ef-0a2ab263c1a2", "historyId": "ef1dd7e8a3e21e75ec0b1e6f70ddd4f3", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testGetRoleIdByRoleNameAndTenantId()]", "testCaseName": "testGetRoleIdByRoleNameAndTenantId()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testGetRoleIdByRoleNameAndTenantId", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testGetRoleIdByRoleNameAndTenantId()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testGetRoleIdByRoleNameAndTenantId"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testGetRoleIdByRoleNameAndTenantId()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758686193, "stop": 1753758686226}