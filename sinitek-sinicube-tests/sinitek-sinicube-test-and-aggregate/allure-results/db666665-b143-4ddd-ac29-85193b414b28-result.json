{"uuid": "db666665-b143-4ddd-ac29-85193b414b28", "historyId": "4469881dd3ed443131c303857a667ecd", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.i18n.frontend.I18nControllerTest]/[method:testDeleteI18n()]", "testCaseName": "删除I18n的语言包信息", "fullName": "com.sinitek.sirm.common.i18n.frontend.I18nControllerTest.testDeleteI18n", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.i18n.frontend.I18nControllerTest]/[method:testDeleteI18n()]"}, {"name": "feature", "value": "语言包配置"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.i18n.frontend.I18nControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.i18n.frontend.I18nControllerTest"}, {"name": "testMethod", "value": "testDeleteI18n"}, {"name": "suite", "value": "com.sinitek.sirm.common.i18n.frontend.I18nControllerTest"}], "links": [], "name": "删除I18n的语言包信息", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758693506, "stop": 1753758693544}