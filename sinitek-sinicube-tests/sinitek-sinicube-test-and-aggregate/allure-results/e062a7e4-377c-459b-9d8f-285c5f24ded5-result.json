{"uuid": "e062a7e4-377c-459b-9d8f-285c5f24ded5", "historyId": "d1ece59151f0a9a83bb7422349615a52", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateImport()]", "testCaseName": "测试导入消息模板", "fullName": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest.testMessageTemplateImport", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateImport()]"}, {"name": "feature", "value": "消息模版管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testMethod", "value": "testMessageTemplateImport"}, {"name": "suite", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}], "links": [], "name": "测试导入消息模板", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758695789, "stop": 1753758695870}