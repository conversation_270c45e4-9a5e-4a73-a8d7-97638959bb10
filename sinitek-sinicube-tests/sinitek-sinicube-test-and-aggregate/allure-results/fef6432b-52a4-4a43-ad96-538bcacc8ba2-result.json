{"uuid": "fef6432b-52a4-4a43-ad96-538bcacc8ba2", "historyId": "213c4545373e6cce10a7dd8122121edc", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.action.ActionControllerTest]/[method:testUnBindAction()]", "testCaseName": "测试解绑动作", "fullName": "com.sinitek.sirm.common.action.ActionControllerTest.testUnBindAction", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.action.ActionControllerTest]/[method:testUnBindAction()]"}, {"name": "feature", "value": "运营管理--动作管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.action.ActionControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.action.ActionControllerTest"}, {"name": "testMethod", "value": "testUnBindAction"}, {"name": "suite", "value": "com.sinitek.sirm.common.action.ActionControllerTest"}], "links": [], "name": "测试解绑动作", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758690348, "stop": 1753758690387}