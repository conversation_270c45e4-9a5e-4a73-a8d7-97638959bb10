{"uuid": "fd67c733-0da7-4c66-9b88-10dd06397992", "historyId": "80319383ce516cd6117fcd1b68d3cfa6", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testTriggerSave()]", "testCaseName": "测试保存调度", "fullName": "com.sinitek.sirm.common.quartz.QuartzApiActionTest.testTriggerSave", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testTriggerSave()]"}, {"name": "feature", "value": "定时任务管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testMethod", "value": "testTriggerSave"}, {"name": "suite", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}], "links": [], "name": "测试保存调度", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758697040, "stop": 1753758697094}