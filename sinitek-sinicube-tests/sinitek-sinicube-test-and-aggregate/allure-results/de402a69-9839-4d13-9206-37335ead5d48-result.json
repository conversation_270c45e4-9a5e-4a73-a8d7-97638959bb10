{"uuid": "de402a69-9839-4d13-9206-37335ead5d48", "historyId": "86b69133d84522d5845c07ff164a67de", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateDelete()]", "testCaseName": "测试根据Id批量删除消息模版", "fullName": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest.testMessageTemplateDelete", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateDelete()]"}, {"name": "feature", "value": "消息模版管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testMethod", "value": "testMessageTemplateDelete"}, {"name": "suite", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}], "links": [], "name": "测试根据Id批量删除消息模版", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758695501, "stop": 1753758695551}