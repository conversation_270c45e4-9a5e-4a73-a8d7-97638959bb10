{"uuid": "d136e12f-0ee7-442d-99e0-30a828b20d1f", "historyId": "9764b880953dbfc4b93e378f103066c6", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindUnitsByFromObjectIdAndInSupervisionAndUnderline()]", "testCaseName": "testFindUnitsByFromObjectIdAndInSupervisionAndUnderline()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testFindUnitsByFromObjectIdAndInSupervisionAndUnderline", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindUnitsByFromObjectIdAndInSupervisionAndUnderline()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testFindUnitsByFromObjectIdAndInSupervisionAndUnderline"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testFindUnitsByFromObjectIdAndInSupervisionAndUnderline()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758683415, "stop": 1753758683448}