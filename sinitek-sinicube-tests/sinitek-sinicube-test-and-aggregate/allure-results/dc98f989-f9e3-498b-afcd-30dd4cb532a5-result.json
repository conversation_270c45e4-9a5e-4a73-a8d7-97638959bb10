{"uuid": "dc98f989-f9e3-498b-afcd-30dd4cb532a5", "historyId": "c07202848683b4605887bab14ad134e1", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.frontend.MessageSettingApiActionTest]/[method:testLoad()]", "testCaseName": "测试加载消息参数配置", "fullName": "com.sinitek.sirm.common.message.frontend.MessageSettingApiActionTest.testLoad", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.frontend.MessageSettingApiActionTest]/[method:testLoad()]"}, {"name": "feature", "value": "消息参数管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.message.frontend.MessageSettingApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.message.frontend.MessageSettingApiActionTest"}, {"name": "testMethod", "value": "testLoad"}, {"name": "suite", "value": "com.sinitek.sirm.common.message.frontend.MessageSettingApiActionTest"}], "links": [], "name": "测试加载消息参数配置", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758695433, "stop": 1753758695477}