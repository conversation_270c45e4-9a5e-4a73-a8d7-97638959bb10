{"uuid": "eaabbe59-473f-4d3c-8c61-4f8d9299cd81", "historyId": "19c37d8235c7ff92f56c00c9813b6ae0", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserApiActionTest]/[test-template:testResetPwd(java.lang.String)]", "testCaseName": "重置密码", "fullName": "com.sinitek.sirm.user.frontend.UserApiActionTest.testResetPwd", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserApiActionTest]/[test-template:testResetPwd(java.lang.String)]/[test-template-invocation:#1]"}, {"name": "feature", "value": "用户管理"}, {"name": "story", "value": "重置密码"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.user.frontend.UserApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.user.frontend.UserApiActionTest"}, {"name": "testMethod", "value": "testResetPwd"}, {"name": "suite", "value": "com.sinitek.sirm.user.frontend.UserApiActionTest"}], "links": [], "name": "重置密码 [1] userName=unit_test", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [{"name": "UniqueId", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserApiActionTest]/[test-template:testResetPwd(java.lang.String)]/[test-template-invocation:#1]", "mode": "hidden"}], "start": 1753758734095, "stop": 1753758734209}