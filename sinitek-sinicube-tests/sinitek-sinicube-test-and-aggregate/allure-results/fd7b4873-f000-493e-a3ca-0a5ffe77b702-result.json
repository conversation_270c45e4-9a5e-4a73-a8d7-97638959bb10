{"uuid": "fd7b4873-f000-493e-a3ca-0a5ffe77b702", "historyId": "af706a734327a48f0c45e431b5bfcd65", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testDeleteAllSendMessage()]", "testCaseName": "测试删除全部消息（发件箱", "fullName": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest.testDeleteAllSendMessage", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testDeleteAllSendMessage()]"}, {"name": "feature", "value": "消息模版管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testMethod", "value": "testDeleteAllSendMessage"}, {"name": "suite", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}], "links": [], "name": "测试删除全部消息（发件箱", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758696053, "stop": 1753758696116}