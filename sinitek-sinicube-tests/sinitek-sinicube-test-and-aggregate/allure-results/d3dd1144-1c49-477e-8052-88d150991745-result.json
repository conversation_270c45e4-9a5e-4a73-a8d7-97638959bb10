{"uuid": "d3dd1144-1c49-477e-8052-88d150991745", "historyId": "f060ed2fa496b3da6b8c9dfd9c0cf3e6", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest]/[method:testHandleSpecialTaskHistoryData()]", "testCaseName": "执行配置历史数据处理", "fullName": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest.testHandleSpecialTaskHistoryData", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest]/[method:testHandleSpecialTaskHistoryData()]"}, {"name": "feature", "value": "流程类型参数管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}, {"name": "testMethod", "value": "testHandleSpecialTaskHistoryData"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}], "links": [], "name": "执行配置历史数据处理", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758738488, "stop": 1753758738536}