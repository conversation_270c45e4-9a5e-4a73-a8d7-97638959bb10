{"uuid": "db4af79b-d3a9-4b6e-876a-8359772e95dd", "historyId": "189c36accc92440c2892e3e20a20b7f6", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserImportApiActionTest]/[method:testImportWithLeaveUserData()]", "testCaseName": "离职用户数据导入-INSERT", "fullName": "com.sinitek.sirm.user.frontend.UserImportApiActionTest.testImportWithLeaveUserData", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserImportApiActionTest]/[method:testImportWithLeaveUserData()]"}, {"name": "feature", "value": "用户管理"}, {"name": "story", "value": "用户导入"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}, {"name": "testMethod", "value": "testImportWithLeaveUserData"}, {"name": "suite", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}], "links": [], "name": "离职用户数据导入-INSERT", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758735121, "stop": 1753758735189}