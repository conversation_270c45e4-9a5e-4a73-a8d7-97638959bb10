{"uuid": "d09c53e0-0293-4efb-aa14-090594624ae0", "historyId": "6d9731054a4517bf7f65b057cb4fc5fb", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest]/[method:testUnFinishTaskList()]", "testCaseName": "测试获取未完成任务列表", "fullName": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest.testUnFinishTaskList", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest]/[method:testUnFinishTaskList()]"}, {"name": "feature", "value": "系统流程管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest"}, {"name": "testMethod", "value": "testUnFinishTaskList"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest"}], "links": [], "name": "测试获取未完成任务列表", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758736167, "stop": 1753758736268}