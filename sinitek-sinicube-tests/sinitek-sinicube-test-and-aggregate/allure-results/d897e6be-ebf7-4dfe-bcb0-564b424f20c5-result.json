{"uuid": "d897e6be-ebf7-4dfe-bcb0-564b424f20c5", "historyId": "e7ff01f40c6fd6605b059fa9924b9598", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserApiActionTest]/[method:testUpdateWithAddRole()]", "testCaseName": "修改用户之给用户增加角色", "fullName": "com.sinitek.sirm.user.frontend.UserApiActionTest.testUpdateWithAddRole", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserApiActionTest]/[method:testUpdateWithAddRole()]"}, {"name": "feature", "value": "用户管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.user.frontend.UserApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.user.frontend.UserApiActionTest"}, {"name": "testMethod", "value": "testUpdateWithAddRole"}, {"name": "suite", "value": "com.sinitek.sirm.user.frontend.UserApiActionTest"}], "links": [], "name": "修改用户之给用户增加角色", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "JSON path \"$.resultcode\"\r\nExpected: is \"0\"\r\n     but: was \"010200\"", "trace": "java.lang.AssertionError: JSON path \"$.resultcode\"\r\nExpected: is \"0\"\r\n     but: was \"010200\"\r\n\tat org.hamcrest.MatcherAssert.assertThat(MatcherAssert.java:20)\r\n\tat org.springframework.test.util.JsonPathExpectationsHelper.assertValue(JsonPathExpectationsHelper.java:74)\r\n\tat org.springframework.test.web.servlet.result.JsonPathResultMatchers.lambda$value$0(JsonPathResultMatchers.java:87)\r\n\tat org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)\r\n\tat com.sinitek.sirm.user.frontend.UserApiActionTest.testUpdateWithAddRole(UserApiActionTest.java:185)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:569)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n"}, "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758734451, "stop": 1753758734563}