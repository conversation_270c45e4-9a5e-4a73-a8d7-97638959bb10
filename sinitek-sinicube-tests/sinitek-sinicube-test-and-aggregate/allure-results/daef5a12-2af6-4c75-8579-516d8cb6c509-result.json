{"uuid": "daef5a12-2af6-4c75-8579-516d8cb6c509", "historyId": "1fcb0abb72714345860881f92357bc2", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ManagerAgentsApiActionTest]/[method:testGetProcessStep()]", "testCaseName": "测试根据流程类型id获得该流程类型下的所有流程步骤", "fullName": "com.sinitek.sirm.workflow.frontend.ManagerAgentsApiActionTest.testGetProcessStep", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ManagerAgentsApiActionTest]/[method:testGetProcessStep()]"}, {"name": "feature", "value": "代理人管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.ManagerAgentsApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.ManagerAgentsApiActionTest"}, {"name": "testMethod", "value": "testGetProcessStep"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.ManagerAgentsApiActionTest"}], "links": [], "name": "测试根据流程类型id获得该流程类型下的所有流程步骤", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758735554, "stop": 1753758735583}