{"uuid": "f7a37bc8-9a55-44a0-bb99-e1ef9473c536", "historyId": "5f1f4a40e1189e4a81041656fcf5496e", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindEmployeesInserviceByOrgId()]", "testCaseName": "testFindEmployeesInserviceByOrgId()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testFindEmployeesInserviceByOrgId", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindEmployeesInserviceByOrgId()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testFindEmployeesInserviceByOrgId"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testFindEmployeesInserviceByOrgId()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758685951, "stop": 1753758685986}