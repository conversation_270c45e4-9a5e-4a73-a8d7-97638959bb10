{"uuid": "efc4eaa3-0ff1-441e-9d7d-912f4fedd97d", "historyId": "9dc98f6dba35b7b3153c18989dd9137f", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest]/[method:testCheckOrgRelationshipExists()]", "testCaseName": "testCheckOrgRelationshipExists()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest.testCheckOrgRelationshipExists", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest]/[method:testCheckOrgRelationshipExists()]"}, {"name": "feature", "value": "远调-组织结构-查询"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}, {"name": "testMethod", "value": "testCheckOrgRelationshipExists"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}], "links": [], "name": "testCheckOrgRelationshipExists()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758679895, "stop": 1753758679940}