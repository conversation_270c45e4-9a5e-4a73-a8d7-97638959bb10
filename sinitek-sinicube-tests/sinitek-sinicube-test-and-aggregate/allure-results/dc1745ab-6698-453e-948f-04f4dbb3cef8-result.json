{"uuid": "dc1745ab-6698-453e-948f-04f4dbb3cef8", "historyId": "d91e0d4aefe4f54e8a167c3f16c6b5f7", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest]/[method:testUnFinishListByStarterIdIsSystem()]", "testCaseName": "测试获取未完成流程列表-发起人为系统", "fullName": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest.testUnFinishListByStarterIdIsSystem", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest]/[method:testUnFinishListByStarterIdIsSystem()]"}, {"name": "feature", "value": "系统流程管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest"}, {"name": "testMethod", "value": "testUnFinishListByStarterIdIsSystem"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.ManagerProcessApiActionTest"}], "links": [], "name": "测试获取未完成流程列表-发起人为系统", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758735988, "stop": 1753758736029}