{"uuid": "d7afe931-2256-4419-bff7-39a25a28e14e", "historyId": "9b5ab07f1b4660ee39de66b4875d9405", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.application.ApplicationControllerTest]/[method:testSave()]", "testCaseName": "新增/修改应用", "fullName": "com.sinitek.sirm.application.ApplicationControllerTest.testSave", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.application.ApplicationControllerTest]/[method:testSave()]"}, {"name": "feature", "value": "应用管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.application.ApplicationControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.application.ApplicationControllerTest"}, {"name": "testMethod", "value": "testSave"}, {"name": "suite", "value": "com.sinitek.sirm.application.ApplicationControllerTest"}], "links": [], "name": "新增/修改应用", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758689235, "stop": 1753758689305}