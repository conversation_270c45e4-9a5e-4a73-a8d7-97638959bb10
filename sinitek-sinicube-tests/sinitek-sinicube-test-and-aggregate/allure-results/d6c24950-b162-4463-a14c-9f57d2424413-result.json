{"uuid": "d6c24950-b162-4463-a14c-9f57d2424413", "historyId": "2332c0d179d27787b2b44ccf8917975d", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testGetOrgNameMapByUserIdList()]", "testCaseName": "testGetOrgNameMapByUserIdList()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testGetOrgNameMapByUserIdList", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testGetOrgNameMapByUserIdList()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testGetOrgNameMapByUserIdList"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testGetOrgNameMapByUserIdList()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758683164, "stop": 1753758683197}