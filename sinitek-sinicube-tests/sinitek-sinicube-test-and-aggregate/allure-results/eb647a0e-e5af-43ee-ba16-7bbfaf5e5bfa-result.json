{"uuid": "eb647a0e-e5af-43ee-ba16-7bbfaf5e5bfa", "historyId": "b6fb52e6a65f14cb8d9700380f5c5f6f", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindAllPosParentByEmpId()]", "testCaseName": "testFindAllPosParentByEmpId()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testFindAllPosParentByEmpId", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindAllPosParentByEmpId()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testFindAllPosParentByEmpId"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testFindAllPosParentByEmpId()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758684002, "stop": 1753758684033}