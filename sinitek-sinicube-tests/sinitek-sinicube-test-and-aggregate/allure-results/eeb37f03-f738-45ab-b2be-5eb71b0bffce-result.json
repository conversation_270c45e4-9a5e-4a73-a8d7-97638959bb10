{"uuid": "eeb37f03-f738-45ab-b2be-5eb71b0bffce", "historyId": "c147d366d146a492c5fd7dd5f6b1948d", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.i18n.frontend.I18nConfigControllerTest]/[method:testGetI18nProperties()]", "testCaseName": "获取国际化设置的默认配置", "fullName": "com.sinitek.sirm.common.i18n.frontend.I18nConfigControllerTest.testGetI18nProperties", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.i18n.frontend.I18nConfigControllerTest]/[method:testGetI18nProperties()]"}, {"name": "story", "value": "国际化设置"}, {"name": "feature", "value": "语言包配置"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.i18n.frontend.I18nConfigControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.i18n.frontend.I18nConfigControllerTest"}, {"name": "testMethod", "value": "testGetI18nProperties"}, {"name": "suite", "value": "com.sinitek.sirm.common.i18n.frontend.I18nConfigControllerTest"}], "links": [], "name": "获取国际化设置的默认配置", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758693168, "stop": 1753758693208}