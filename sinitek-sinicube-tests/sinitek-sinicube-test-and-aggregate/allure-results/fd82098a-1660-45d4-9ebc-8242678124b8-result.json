{"uuid": "fd82098a-1660-45d4-9ebc-8242678124b8", "historyId": "11fabaaa6313cef07194554d717d6a2d", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.tree.demo.DemoTreeControllerTest]/[method:testUpdateDemoTree()]", "testCaseName": "修改树模型demo", "fullName": "com.sinitek.sirm.common.tree.demo.DemoTreeControllerTest.testUpdateDemoTree", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.tree.demo.DemoTreeControllerTest]/[method:testUpdateDemoTree()]"}, {"name": "feature", "value": "树模型demo管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.tree.demo.DemoTreeControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.tree.demo.DemoTreeControllerTest"}, {"name": "testMethod", "value": "testUpdateDemoTree"}, {"name": "suite", "value": "com.sinitek.sirm.common.tree.demo.DemoTreeControllerTest"}], "links": [], "name": "修改树模型demo", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758710241, "stop": 1753758710319}