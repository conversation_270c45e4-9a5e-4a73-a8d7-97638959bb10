{"uuid": "fce7136b-4ba9-4410-99e7-185fc2346523", "historyId": "7073f223101f8419e6c75410cbcd86d1", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.calendar.CalendarEventControllerTest]/[method:testCalendarEventLoad()]", "testCaseName": "测试获取指定日程的具体信息", "fullName": "com.sinitek.sirm.calendar.CalendarEventControllerTest.testCalendarEventLoad", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.calendar.CalendarEventControllerTest]/[method:testCalendarEventLoad()]"}, {"name": "feature", "value": "日历组件"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.calendar.CalendarEventControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.calendar.CalendarEventControllerTest"}, {"name": "testMethod", "value": "testCalendarEventLoad"}, {"name": "suite", "value": "com.sinitek.sirm.calendar.CalendarEventControllerTest"}], "links": [], "name": "测试获取指定日程的具体信息", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "\r\nexpected: \"测试一下\"\r\n but was: \"娴嬭瘯涓�涓�\"", "trace": "org.opentest4j.AssertionFailedError: \r\nexpected: \"测试一下\"\r\n but was: \"娴嬭瘯涓�涓�\"\r\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\r\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\r\n\tat com.sinitek.sirm.calendar.CalendarEventControllerTest.testCalendarEventLoad(CalendarEventControllerTest.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:569)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n"}, "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758689814, "stop": 1753758689868}