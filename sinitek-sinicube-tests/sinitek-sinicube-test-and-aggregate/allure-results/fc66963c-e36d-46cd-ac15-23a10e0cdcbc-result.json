{"uuid": "fc66963c-e36d-46cd-ac15-23a10e0cdcbc", "historyId": "39ec2645a24c15dc00d3066be170461f", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindAllEmployeesInSerivce()]", "testCaseName": "testFindAllEmployeesInSerivce()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testFindAllEmployeesInSerivce", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindAllEmployeesInSerivce()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testFindAllEmployeesInSerivce"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testFindAllEmployeesInSerivce()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758682607, "stop": 1753758682675}