{"uuid": "dbd4d2bc-e918-4be4-b8a0-773ee39a4b2e", "historyId": "d878f4c08d6113327e60f44fe596935b", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateList()]", "testCaseName": "测试根据条件分页查询消息模版列表", "fullName": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest.testMessageTemplateList", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateList()]"}, {"name": "feature", "value": "消息模版管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testMethod", "value": "testMessageTemplateList"}, {"name": "suite", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}], "links": [], "name": "测试根据条件分页查询消息模版列表", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758695937, "stop": 1753758695987}