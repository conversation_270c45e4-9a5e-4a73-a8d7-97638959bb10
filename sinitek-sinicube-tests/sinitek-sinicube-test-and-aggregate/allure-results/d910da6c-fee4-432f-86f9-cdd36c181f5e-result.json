{"uuid": "d910da6c-fee4-432f-86f9-cdd36c181f5e", "historyId": "c158685fd90e373b99867de649588979", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testTriggerStart()]", "testCaseName": "测试启动调度", "fullName": "com.sinitek.sirm.common.quartz.QuartzApiActionTest.testTriggerStart", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testTriggerStart()]"}, {"name": "feature", "value": "定时任务管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testMethod", "value": "testTriggerStart"}, {"name": "suite", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}], "links": [], "name": "测试启动调度", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758697913, "stop": 1753758697955}