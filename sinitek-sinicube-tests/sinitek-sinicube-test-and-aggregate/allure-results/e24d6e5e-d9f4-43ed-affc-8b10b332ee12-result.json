{"uuid": "e24d6e5e-d9f4-43ed-affc-8b10b332ee12", "historyId": "588ad64cadb0cb55809a67dd98480187", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.setting.SettingApiActionTest]/[method:testQuery()]", "testCaseName": "参数查询条件加载", "fullName": "com.sinitek.sirm.common.setting.SettingApiActionTest.testQuery", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.setting.SettingApiActionTest]/[method:testQuery()]"}, {"name": "feature", "value": "参数配置"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.setting.SettingApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.setting.SettingApiActionTest"}, {"name": "testMethod", "value": "testQuery"}, {"name": "suite", "value": "com.sinitek.sirm.common.setting.SettingApiActionTest"}], "links": [], "name": "参数查询条件加载", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758698053, "stop": 1753758698089}