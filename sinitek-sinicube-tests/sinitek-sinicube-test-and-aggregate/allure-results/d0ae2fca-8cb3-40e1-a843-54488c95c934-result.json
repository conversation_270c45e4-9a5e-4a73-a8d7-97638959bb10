{"uuid": "d0ae2fca-8cb3-40e1-a843-54488c95c934", "historyId": "65a249a8aa46058bcc76746ebe15ecfd", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.MyAgentsApiActionTest]/[method:testFindMyAgentsList()]", "testCaseName": "测试获得我的代理人列表数据", "fullName": "com.sinitek.sirm.workflow.frontend.MyAgentsApiActionTest.testFindMyAgentsList", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.MyAgentsApiActionTest]/[method:testFindMyAgentsList()]"}, {"name": "feature", "value": "我的代理人"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.MyAgentsApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.MyAgentsApiActionTest"}, {"name": "testMethod", "value": "testFindMyAgentsList"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.MyAgentsApiActionTest"}], "links": [], "name": "测试获得我的代理人列表数据", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758737312, "stop": 1753758737361}