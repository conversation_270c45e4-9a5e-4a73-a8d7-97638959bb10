{"uuid": "f158b52b-ba63-408a-86b3-1f808883bc84", "historyId": "9aff9d4954749ac0bce8add65b08542b", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.right.feign.RightRemoteServiceTest]/[method:testFindEnableAuthedObjects()]", "testCaseName": "远调测试-查询出有对应权限的AuthOrgIdList", "fullName": "com.sinitek.cloud.sirmapp.right.feign.RightRemoteServiceTest.testFindEnableAuthedObjects", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.right.feign.RightRemoteServiceTest]/[method:testFindEnableAuthedObjects()]"}, {"name": "feature", "value": "远调-通用权限表相关操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.right.feign.RightRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.right.feign.RightRemoteServiceTest"}, {"name": "testMethod", "value": "testFindEnableAuthedObjects"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.right.feign.RightRemoteServiceTest"}], "links": [], "name": "远调测试-查询出有对应权限的AuthOrgIdList", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758688386, "stop": 1753758688422}