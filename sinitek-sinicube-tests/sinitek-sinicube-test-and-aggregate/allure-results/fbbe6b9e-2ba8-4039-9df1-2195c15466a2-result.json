{"uuid": "fbbe6b9e-2ba8-4039-9df1-2195c15466a2", "historyId": "501bb9ed34abcf0a2ec3eb5c1774223f", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserImportApiActionTest]/[method:testImportWithNormalData()]", "testCaseName": "正常数据导入-INSERT", "fullName": "com.sinitek.sirm.user.frontend.UserImportApiActionTest.testImportWithNormalData", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserImportApiActionTest]/[method:testImportWithNormalData()]"}, {"name": "feature", "value": "用户管理"}, {"name": "story", "value": "用户导入"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}, {"name": "testMethod", "value": "testImportWithNormalData"}, {"name": "suite", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}], "links": [], "name": "正常数据导入-INSERT", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758735195, "stop": 1753758735325}