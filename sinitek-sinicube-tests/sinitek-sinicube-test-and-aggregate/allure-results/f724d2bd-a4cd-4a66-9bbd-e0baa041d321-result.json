{"uuid": "f724d2bd-a4cd-4a66-9bbd-e0baa041d321", "historyId": "a6da8cb5f9be2cc52a44d27bceefe2d", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.MyTaskApiActionTest]/[method:testRecoverTask()]", "testCaseName": "测试撤回任务", "fullName": "com.sinitek.sirm.workflow.frontend.MyTaskApiActionTest.testRecoverTask", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.MyTaskApiActionTest]/[method:testRecoverTask()]"}, {"name": "feature", "value": "我的事宜"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.MyTaskApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.MyTaskApiActionTest"}, {"name": "testMethod", "value": "testRecoverTask"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.MyTaskApiActionTest"}], "links": [], "name": "测试撤回任务", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758737852, "stop": 1753758738035}