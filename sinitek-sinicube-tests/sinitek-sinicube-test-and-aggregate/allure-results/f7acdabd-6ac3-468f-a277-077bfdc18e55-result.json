{"uuid": "f7acdabd-6ac3-468f-a277-077bfdc18e55", "historyId": "c979e51f0373426fb9c5370381e96da9", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest]/[method:testDeleteParameterIfType()]", "testCaseName": "测试删除条件配置", "fullName": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest.testDeleteParameterIfType", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest]/[method:testDeleteParameterIfType()]"}, {"name": "feature", "value": "流程类型参数管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}, {"name": "testMethod", "value": "testDeleteParameterIfType"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}], "links": [], "name": "测试删除条件配置", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758739051, "stop": 1753758739074}