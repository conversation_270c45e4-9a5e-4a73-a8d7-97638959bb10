{"uuid": "fb730b2b-9ec8-4059-bbc4-f34103bd1e7a", "historyId": "57935638710841bf8e68b1d504c69033", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testGetPositionByPath()]", "testCaseName": "testGetPositionByPath()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testGetPositionByPath", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testGetPositionByPath()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testGetPositionByPath"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "testGetPositionByPath()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758683249, "stop": 1753758683278}