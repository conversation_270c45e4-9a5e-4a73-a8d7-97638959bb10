{"uuid": "eb1cfd3b-b65c-46a3-9df9-b3e5951efb0c", "historyId": "e744a8522cb60e1eab0d783bdc719109", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.framework.frontend.HomePageCfgApiActionTest]/[method:testSave()]", "testCaseName": "配置保存", "fullName": "com.sinitek.sirm.framework.frontend.HomePageCfgApiActionTest.testSave", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.framework.frontend.HomePageCfgApiActionTest]/[method:testSave()]"}, {"name": "feature", "value": "首页模板配置"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.framework.frontend.HomePageCfgApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.framework.frontend.HomePageCfgApiActionTest"}, {"name": "testMethod", "value": "testSave"}, {"name": "suite", "value": "com.sinitek.sirm.framework.frontend.HomePageCfgApiActionTest"}], "links": [], "name": "配置保存", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758711044, "stop": 1753758711136}