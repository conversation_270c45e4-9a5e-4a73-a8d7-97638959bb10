{"uuid": "d770d39e-7c4a-4161-b503-d9a874ae13ca", "historyId": "478502670671f4930b0e1ac3e5fb01d9", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testQuartzStatusChangeEnd()]", "testCaseName": "测试改变定时任务计划状态(停止)", "fullName": "com.sinitek.sirm.common.quartz.QuartzApiActionTest.testQuartzStatusChangeEnd", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.quartz.QuartzApiActionTest]/[method:testQuartzStatusChangeEnd()]"}, {"name": "feature", "value": "定时任务管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}, {"name": "testMethod", "value": "testQuartzStatusChangeEnd"}, {"name": "suite", "value": "com.sinitek.sirm.common.quartz.QuartzApiActionTest"}], "links": [], "name": "测试改变定时任务计划状态(停止)", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758697258, "stop": 1753758697299}