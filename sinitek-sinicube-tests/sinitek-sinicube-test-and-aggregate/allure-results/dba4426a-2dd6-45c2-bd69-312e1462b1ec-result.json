{"uuid": "dba4426a-2dd6-45c2-bd69-312e1462b1ec", "historyId": "4030c378411533391f1ddf1c1b4cfc78", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserImportApiActionTest]/[method:testImportWithErrorUserSource()]", "testCaseName": "错误用户来源-INSERT", "fullName": "com.sinitek.sirm.user.frontend.UserImportApiActionTest.testImportWithErrorUserSource", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.user.frontend.UserImportApiActionTest]/[method:testImportWithErrorUserSource()]"}, {"name": "feature", "value": "用户管理"}, {"name": "story", "value": "用户导入"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}, {"name": "testMethod", "value": "testImportWithErrorUserSource"}, {"name": "suite", "value": "com.sinitek.sirm.user.frontend.UserImportApiActionTest"}], "links": [], "name": "错误用户来源-INSERT", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758735040, "stop": 1753758735115}