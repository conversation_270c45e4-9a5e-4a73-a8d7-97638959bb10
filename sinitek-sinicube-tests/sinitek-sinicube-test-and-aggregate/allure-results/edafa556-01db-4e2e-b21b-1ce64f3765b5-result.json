{"uuid": "edafa556-01db-4e2e-b21b-1ce64f3765b5", "historyId": "7b466234b88c55317f55ad9e72ad3c30", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest]/[method:testFindParameterUrl()]", "testCaseName": "测试获取处理页面配置", "fullName": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest.testFindParameterUrl", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest]/[method:testFindParameterUrl()]"}, {"name": "feature", "value": "流程类型参数管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}, {"name": "testMethod", "value": "testFindParameterUrl"}, {"name": "suite", "value": "com.sinitek.sirm.workflow.frontend.ProcessTypeParamManagerActionTest"}], "links": [], "name": "测试获取处理页面配置", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758739420, "stop": 1753758739458}