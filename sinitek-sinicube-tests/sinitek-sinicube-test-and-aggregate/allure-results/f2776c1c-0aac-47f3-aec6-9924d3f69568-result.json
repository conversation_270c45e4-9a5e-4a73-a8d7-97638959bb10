{"uuid": "f2776c1c-0aac-47f3-aec6-9924d3f69568", "historyId": "caa12a79d77ca6be528959822aa043a9", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateUpdate()]", "testCaseName": "测试更新消息模版信息", "fullName": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest.testMessageTemplateUpdate", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest]/[method:testMessageTemplateUpdate()]"}, {"name": "feature", "value": "消息模版管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}, {"name": "testMethod", "value": "testMessageTemplateUpdate"}, {"name": "suite", "value": "com.sinitek.sirm.common.message.template.frontend.MessageTemplateApiActionTest"}], "links": [], "name": "测试更新消息模版信息", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758695878, "stop": 1753758695930}