{"uuid": "d7ddf7a3-2eb9-42a4-8efc-7503e752a1a1", "historyId": "1cd83abe60ca891fa004d5727bec16db", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.action.ActionControllerTest]/[method:testActionList()]", "testCaseName": "测试获取动作列表", "fullName": "com.sinitek.sirm.common.action.ActionControllerTest.testActionList", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.action.ActionControllerTest]/[method:testActionList()]"}, {"name": "feature", "value": "运营管理--动作管理接口"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.action.ActionControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.action.ActionControllerTest"}, {"name": "testMethod", "value": "testActionList"}, {"name": "suite", "value": "com.sinitek.sirm.common.action.ActionControllerTest"}], "links": [], "name": "测试获取动作列表", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758690243, "stop": 1753758690272}