{"uuid": "ff79e157-5cec-4810-b6e2-08c079d3d12d", "historyId": "fccecc281d2841eb34f267b3ead8530", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.calendar.CalendarEventControllerTest]/[method:testCalendarEventSave()]", "testCaseName": "测试保存日程", "fullName": "com.sinitek.sirm.calendar.CalendarEventControllerTest.testCalendarEventSave", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.calendar.CalendarEventControllerTest]/[method:testCalendarEventSave()]"}, {"name": "feature", "value": "日历组件"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.calendar.CalendarEventControllerTest"}, {"name": "testClass", "value": "com.sinitek.sirm.calendar.CalendarEventControllerTest"}, {"name": "testMethod", "value": "testCalendarEventSave"}, {"name": "suite", "value": "com.sinitek.sirm.calendar.CalendarEventControllerTest"}], "links": [], "name": "测试保存日程", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758689875, "stop": 1753758689920}