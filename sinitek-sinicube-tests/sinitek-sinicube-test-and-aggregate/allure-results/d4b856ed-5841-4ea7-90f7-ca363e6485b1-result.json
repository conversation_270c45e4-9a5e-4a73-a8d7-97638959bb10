{"uuid": "d4b856ed-5841-4ea7-90f7-ca363e6485b1", "historyId": "64cc9489318f44dff3a6f49ab4e2643", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindBatchEmployeesByOrgId()]", "testCaseName": "远调测试-根据orgid数组批量获取组织结构下面的所有人员", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest.testFindBatchEmployeesByOrgId", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest]/[method:testFindBatchEmployeesByOrgId()]"}, {"name": "feature", "value": "远调-用户操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}, {"name": "testMethod", "value": "testFindBatchEmployeesByOrgId"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteServiceTest"}], "links": [], "name": "远调测试-根据orgid数组批量获取组织结构下面的所有人员", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758686106, "stop": 1753758686148}