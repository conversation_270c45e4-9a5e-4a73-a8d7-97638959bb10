{"uuid": "ef27858b-3982-4fad-88aa-d30988df11ee", "historyId": "4ff2504905c2e2d8e67e7e0e775b6778", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.framework.frontend.WebModuleApiActionTest]/[method:testDetail()]", "testCaseName": "获取模块详情", "fullName": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest.testDetail", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.framework.frontend.WebModuleApiActionTest]/[method:testDetail()]"}, {"name": "feature", "value": "首页模块管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest"}, {"name": "testMethod", "value": "testDetail"}, {"name": "suite", "value": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest"}], "links": [], "name": "获取模块详情", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "JSON path \"$.data.name\"\r\nExpected: is \"未读消息\"\r\n     but: was \"鏈娑堟伅\"", "trace": "java.lang.AssertionError: JSON path \"$.data.name\"\r\nExpected: is \"未读消息\"\r\n     but: was \"鏈娑堟伅\"\r\n\tat org.hamcrest.MatcherAssert.assertThat(MatcherAssert.java:20)\r\n\tat org.springframework.test.util.JsonPathExpectationsHelper.assertValue(JsonPathExpectationsHelper.java:74)\r\n\tat org.springframework.test.web.servlet.result.JsonPathResultMatchers.lambda$value$0(JsonPathResultMatchers.java:87)\r\n\tat org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)\r\n\tat com.sinitek.sirm.framework.frontend.WebModuleApiActionTest.testDetail(WebModuleApiActionTest.java:114)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:569)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n"}, "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758723283, "stop": 1753758723324}