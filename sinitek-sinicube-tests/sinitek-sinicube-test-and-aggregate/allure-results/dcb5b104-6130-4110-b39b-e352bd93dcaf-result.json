{"uuid": "dcb5b104-6130-4110-b39b-e352bd93dcaf", "historyId": "a992ac78e7b662f4e1c1925ade5df233", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest]/[method:testGetSysMenuById()]", "testCaseName": "远调测试-根据menuId查询sysMenu", "fullName": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest.testGetSysMenuById", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest]/[method:testGetSysMenuById()]"}, {"name": "feature", "value": "远调-系统菜单相关操作"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest"}, {"name": "testMethod", "value": "testGetSysMenuById"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.menu.feign.SirmSysMenuRemoteServiceTest"}], "links": [], "name": "远调测试-根据menuId查询sysMenu", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758664882, "stop": 1753758664948}