{"uuid": "fcd7414f-766c-4ad3-99bb-5998c5a82e28", "historyId": "4569d1f15224bac148b773e6ac588aac", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.framework.frontend.WebModuleApiActionTest]/[method:testDisable()]", "testCaseName": "模块禁用", "fullName": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest.testDisable", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.framework.frontend.WebModuleApiActionTest]/[method:testDisable()]"}, {"name": "feature", "value": "首页模块管理"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest"}, {"name": "testMethod", "value": "testDisable"}, {"name": "suite", "value": "com.sinitek.sirm.framework.frontend.WebModuleApiActionTest"}], "links": [], "name": "模块禁用", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758723242, "stop": 1753758723275}