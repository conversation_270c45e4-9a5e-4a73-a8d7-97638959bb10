{"uuid": "e05b9d8d-a4d2-4c73-b792-41cfcab7a36f", "historyId": "5a2a6be53fb0130899164cb2bcae9d10", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.function.frontend.FunctionApiActionTest]/[method:testMenuFcuntionList()]", "testCaseName": "测试获取菜单对应的功能配置列表", "fullName": "com.sinitek.sirm.common.function.frontend.FunctionApiActionTest.testMenuFcuntionList", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.sirm.common.function.frontend.FunctionApiActionTest]/[method:testMenuFcuntionList()]"}, {"name": "feature", "value": "功能配置"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.sirm.common.function.frontend.FunctionApiActionTest"}, {"name": "testClass", "value": "com.sinitek.sirm.common.function.frontend.FunctionApiActionTest"}, {"name": "testMethod", "value": "testMenuFcuntionList"}, {"name": "suite", "value": "com.sinitek.sirm.common.function.frontend.FunctionApiActionTest"}], "links": [], "name": "测试获取菜单对应的功能配置列表", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758691428, "stop": 1753758691475}