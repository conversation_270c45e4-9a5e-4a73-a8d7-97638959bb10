{"uuid": "f5330fec-2daf-4108-b356-e0aea7073ddb", "historyId": "9de0681ef7edc21f12002c47c997ba25", "testCaseId": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest]/[method:testGetRoot()]", "testCaseName": "testGetRoot()", "fullName": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest.testGetRoot", "labels": [{"name": "junit.platform.uniqueid", "value": "[engine:junit-jupiter]/[class:com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest]/[method:testGetRoot()]"}, {"name": "feature", "value": "远调-组织结构-查询"}, {"name": "host", "value": "B-XN-2950"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "junit-platform"}, {"name": "language", "value": "java"}, {"name": "package", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}, {"name": "testClass", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}, {"name": "testMethod", "value": "testGetRoot"}, {"name": "suite", "value": "com.sinitek.cloud.sirmapp.org.feign.OrgRemoteFinderTest"}], "links": [], "name": "testGetRoot()", "status": "passed", "stage": "finished", "description": "", "steps": [], "attachments": [], "parameters": [], "start": 1753758680089, "stop": 1753758680145}