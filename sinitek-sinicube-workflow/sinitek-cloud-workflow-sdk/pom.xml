<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sinitek-sinicube-workflow</artifactId>
        <groupId>com.sinitek.sinicube</groupId>
        <version>8.0.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sinitek-cloud-workflow-sdk</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- sinitek -->
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-workflow-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek_commoncore</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

</project>
