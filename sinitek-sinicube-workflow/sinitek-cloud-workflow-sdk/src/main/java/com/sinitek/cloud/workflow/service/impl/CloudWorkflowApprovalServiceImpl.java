package com.sinitek.cloud.workflow.service.impl;

import com.sinitek.cloud.workflow.service.IWorkflowService;
import com.sinitek.cloud.workflow.support.dto.WorkflowExampleOwnerDTO;
import com.sinitek.sirm.workflow.dto.WorkflowStartParamDTO;
import com.sinitek.sirm.workflow.dto.WorkflowSubParamDTO;
import com.sinitek.sirm.workflow.service.IWorkflowApprovalService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * date 2023/8/3
 */
public class CloudWorkflowApprovalServiceImpl implements IWorkflowApprovalService {

    @Autowired
    private IWorkflowService workflowService;

    @Override
    public void saveLaunchComponentData(WorkflowStartParamDTO workflowStartParam) {
        workflowService.saveLaunchComponentData(workflowStartParam);
    }

    @Override
    public List<WorkflowExampleOwnerDTO> startProcess(WorkflowStartParamDTO workflowStartParam) {
        return workflowService.startProcess(workflowStartParam).getData();
    }

    @Override
    public List<WorkflowExampleOwnerDTO> submitApproval(WorkflowSubParamDTO workflowSubDtoParam) {
        return workflowService.submitApproval(workflowSubDtoParam).getData();
    }
}
