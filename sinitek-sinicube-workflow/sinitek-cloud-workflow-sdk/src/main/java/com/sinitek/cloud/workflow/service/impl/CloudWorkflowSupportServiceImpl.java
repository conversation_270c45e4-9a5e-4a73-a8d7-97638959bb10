package com.sinitek.cloud.workflow.service.impl;

import com.sinitek.cloud.workflow.service.IWorkflowService;
import com.sinitek.cloud.workflow.support.dto.WorkflowExampleOwnerDTO;
import com.sinitek.cloud.workflow.support.dto.WorkflowStartDTO;
import com.sinitek.cloud.workflow.support.dto.WorkflowSubDTO;
import com.sinitek.sirm.workflow.dto.*;
import com.sinitek.sirm.workflow.service.IWorkflowSupportService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/24
 * @Time 10:04
 */
public class CloudWorkflowSupportServiceImpl implements IWorkflowSupportService {

    @Autowired
    private IWorkflowService workflowService;

    @Override
    public List<WorkflowExampleOwnerDTO> startProcess(WorkflowStartDTO start) {
        return workflowService.startProcess(start).getData();
    }

    @Override
    public List<WorkflowExampleOwnerDTO> subProcessStep(WorkflowSubDTO subDTO) {
        return workflowService.subProcessStep(subDTO).getData();
    }

    @Override
    public List<NowWorkflowDTO> findNowWorkflow() {
        return workflowService.getNowWorkflow().getData();
    }

    @Override
    public List<MainWorkFlowExampleDTO> findMainWorkflow(Long exampleid) {
        return workflowService.getMainWorkflow(exampleid).getData();
    }

    @Override
    public List<ExampleTaskDTO> findUnfinishTasks(ExampleSearchDTO exampleSearch) {
        return workflowService.getUnfinishTasks(exampleSearch).getData();
    }

    @Override
    public List<ExampleTaskDTO> findFinishedTasks(ExampleSearchDTO exampleSearc) {
        return workflowService.getFinishedTasks(exampleSearc).getData();
    }

    @Override
    public List<ReleaseProcesssDTO> findReleaseProcessList() {
        return workflowService.getReleaseProcessList().getData();
    }

    @Override
    public List<ReleaseProcesssDTO> findReleaseProcessAndCodeList() {
        return workflowService.getReleaseProcessAndCodeList().getData();
    }

    @Override
    public List<ReleaseProcesssDTO> findReleaseProcessAndNullList() {
        return workflowService.getReleaseProcessAndNullList().getData();
    }

    @Override
    public List<DetailCadidateDTO> findDetailCadidateListByExampleStepid(Long examplestepid) {
        return workflowService.getDetailCadidateListByExampleStepid(examplestepid).getData();
    }

    @Override
    public List<DetailCadidateDTO> findDetailCadidateListByProcessStepid(Long processstepid) {
        return workflowService.getDetailCadidateListByProcessStepid(processstepid).getData();
    }

    @Override
    public List<WorkflowExampleOwnerDTO> gotoProcessStart(RecoverProcessDTO recoverProcess) {
        return workflowService.gotoProcessStart(recoverProcess).getData();
    }

    @Override
    public List<WorkflowExampleOwnerDTO> recoverProcessToPreStep(RecoverProcessDTO recoverProcess) {
        return workflowService.recoverProcessToPreStep(recoverProcess).getData();
    }

    @Override
    public List<WorkflowExampleOwnerDTO> recoverProcessToSpecialStep(
            RecoverProcessDTO recoverProcess, Long targetStepId) {
        return workflowService.recoverProcessToSpecialStep(recoverProcess, targetStepId).getData();
    }

    @Override
    public void recoverExampleTask(Long exampleId, Long exampleStepId, Long exampleOwnerId) {
        workflowService.recoverExampleTask(exampleId, exampleStepId, exampleOwnerId);
    }

    @Override
    public void recoverExampleAssign(Long assignId) {
        workflowService.recoverExampleAssign(assignId);
    }

    @Override
    public List<ExampleTaskDTO> findTasksTakepart(ExampleSearchDTO exampleSearch) {
        return workflowService.getTasksTakepart(exampleSearch).getData();
    }

    @Override
    public List<ExampleTaskDTO> findMyTasks(ExampleSearchDTO exampleSearch) {
        return workflowService.getMyTasks(exampleSearch).getData();
    }

    @Override
    public Long addExampleStepOwner(Long exampleStepId, String orgId) {
        return workflowService.addExampleStepOwner(exampleStepId, orgId).getData();
    }

    @Override
    public List<ExampleEntryDTO> findExampleEntryList(Long exampleid) {
        return workflowService.findExampleEntryList(exampleid).getData();
    }
}
