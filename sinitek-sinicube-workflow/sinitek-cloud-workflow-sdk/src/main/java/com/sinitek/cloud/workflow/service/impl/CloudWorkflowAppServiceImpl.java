package com.sinitek.cloud.workflow.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.cloud.workflow.service.IWorkflowService;
import com.sinitek.cloud.workflow.support.dto.*;
import com.sinitek.sirm.common.sonar.IgnoreMapCheck;
import com.sinitek.sirm.common.utils.ObjectUtils;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.workflow.dto.*;
import com.sinitek.sirm.workflow.service.IWorkflowAppService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/24
 * @Time 10:03
 */
public class CloudWorkflowAppServiceImpl implements IWorkflowAppService {

    @Autowired
    private IWorkflowService workflowService;

    @Override
    public Long saveExampleStepOwner(WfExamplestepownerDTO owner) {
        return workflowService.saveExampleStepOwner(owner).getData();
    }

    @Override
    public Long cancelExampleAssignTask(WfExamplestepownerDTO owner) {
        return workflowService.cancelExampleAssignTask(owner).getData();
    }

    @Override
    public Long saveExample(WfExampleDTO example) {
        return workflowService.saveExample(example).getData();
    }

    @Override
    public Long saveExampleStep(WfExamplestepDTO step) {
        return workflowService.saveExampleStep(step).getData();
    }

    @Override
    public Long saveExampleEntry(WfExampleentryDTO entry) {
        return workflowService.saveExampleEntry(entry).getData();
    }

    @Override
    public Long saveExampleStepLink(WfExamplesteplinkDTO link) {
        return workflowService.saveExampleStepLink(link).getData();
    }

    @Override
    public Long saveExamplePara(WfExampleparaDTO para) {
        return workflowService.saveExamplePara(para).getData();
    }

    @Override
    public WfExampleDTO loadExample(Long exampleid) {
        return workflowService.loadExample(exampleid).getData();
    }

    @Override
    public WfExamplestepDTO loadExampleStep(Long stepid) {
        return workflowService.loadExampleStep(stepid).getData();
    }

    @Override
    public WfExampleentryDTO loadExampleEntry(Long entryid) {
        return workflowService.loadExampleEntry(entryid).getData();
    }

    @Override
    public WfExamplestepownerDTO loadExampleStepOwner(Long ownerid) {
        return workflowService.loadExampleStepOwner(ownerid).getData();
    }

    @Override
    public WfExampleparaDTO loadExamplePara(Long paraid) {
        return workflowService.loadExamplePara(paraid).getData();
    }

    @Override
    public List<NowExampleDTO> findNowExampleList() {
        return workflowService.getNowExampleList().getData();
    }

    @Override
    public List<NowExampleDTO> findNowExampleList(ExampleSearchDTO exampleSearch) {
        return workflowService.getNowExampleList(exampleSearch).getData();
    }

    @Override
    public List<ExampleOwnerDTO> findExampleOwnerInfo(Long examplestepid) {
        return workflowService.findExampleOwnerInfo(examplestepid).getData();
    }

    @Override
    public List<ExampleStepSimpleDTO> findExampleStepList(Long exampleid) {
        return workflowService.getExampleStepList(exampleid).getData();
    }

    @Override
    public List<ExampleTaskDTO> findExampleTask(String orgid, Integer type) {
        return workflowService.getExampleTask(orgid, type).getData();
    }

    @Override
    public List<ExampleTaskDTO> findExampleTask(ExampleSearchDTO exampleSearch) {
        return workflowService.getExampleTask(exampleSearch).getData();
    }

    @Override
    public List<ExampleOwnerBaseDTO> findExampleOwnerList(Long examplestepid) {
        return workflowService.getExampleOwnerList(examplestepid).getData();
    }

    @Override
    public Long changeExampleS(Long exampleid, Integer status) {
        return workflowService.changeExampleS(exampleid, status).getData();
    }

    @Override
    public List<IssueProcessStepDTO> findIssueProcessStep(String syscode) {
        return workflowService.getIssueProcessStep(syscode).getData();
    }

    @Override
    public List<ExampleEntryDTO> findExampleEntrys(Long exampleid) {
        return workflowService.getExampleEntrys(exampleid).getData();
    }

    @Override
    public List<AgentDTO> findAgentsList(String orgid) {
        return workflowService.getAgentsList(orgid).getData();
    }

    @Override
    public List<AgentDTO> findLiveAgentsList(String orgid, Date starttime, Date endtime) {
        return workflowService.getLiveAgentsList(orgid, starttime, endtime).getData();
    }

    @Override
    public List<ExampleEntryDTO> findProcessListByTypeAndId(String entrytype, Long entryid) {
        return workflowService.getProcessListByTypeAndId(entrytype, entryid).getData();
    }


    @Override
    public Integer saveParaMap(Long exampleid, Long examplestepid, Long exampleownerid, Map map) {
        RequestResult<Integer> requestResult = workflowService
                .resultMapStorage(exampleid, examplestepid, exampleownerid, map);
        return requestResult.getData();
    }

    @Override
    @IgnoreMapCheck
    public Map<String, Object> loadParaMap(Long exampleid, Long examplestepid, Long exampleownerid) {
        return workflowService.resultMapLoad(exampleid, examplestepid, exampleownerid).getData();
    }

    @Override
    public List<ProcessParamListDTO> findParaListByName(String name) {
        return workflowService.findParaListByName(name).getData();
    }

    @Override
    @IgnoreMapCheck
    public Map<String, String> getAllParaMap() {
        return workflowService.findAllParaMap().getData();
    }

    @Override
    public List<ExampleOwnerDTO> findExampleOwnerListByExampleid(Long exampleid, Integer type) {
        return workflowService.getExampleOwnerListByExampleId(exampleid, type).getData();
    }

    @Override
    public List<ExampleOwnerDTO> findExampleOwnerListByStepid(Long stepid, Integer type,
            Long exampleid) {
        return workflowService.getExampleOwnerListByStepid(stepid, type, exampleid).getData();
    }

    @Override
    public List<CurrentExampleStepDTO> findCurrentExampleStepList(Long exampleid) {
        return workflowService.getCurrentExampleStepList(exampleid).getData();
    }

    @Override
    public List<FrontExampleStepDTO> findFrontExampleStepList(Long examplestepid) {
        return workflowService.getFrontExampleStepList(examplestepid).getData();
    }

    @Override
    public List<ExampleDTO> findMyProcessList(String orgid, Long processid, Long processtype,
            String syscode) {
        return workflowService.getMyProcessList(orgid, processid, processtype, syscode).getData();
    }

    @Override
    public List<ExampleOwnerOpinionDTO> findExampleOwnerOpinion(Long exampleid, Integer sort, Integer type) {
        return workflowService.getExampleOwnerOpinion(exampleid, sort, type).getData();
    }

    @Override
    public List<ExampleOwnerDTO> findOwnerListByExampleid(Long exampleid) {
        return workflowService.getOwnerListByExampleid(exampleid).getData();
    }

    @Override
    public List<HistorySourceDTO> findHistorySourceList(HistorySourceSearchDTO historySourceSearch) {
        return workflowService.getHistorySourceList(historySourceSearch).getData();
    }

    @Override
    public List<DetailTaskDTO> findDetailTaskList(Long exampleStepId) {
        return workflowService.getDetailTaskList(exampleStepId).getData();
    }

    @Override
    public List<DealerTaskCountDTO> findDealerTaskCountList() {
        return workflowService.getDealerTaskCountList().getData();
    }

    @Override
    public List<Integer> findStepNextList(Long stepId) {
        return workflowService.findStepNextList(stepId).getData();
    }

    @Override
    public List<NextStepOwnerDTO> findNextStepOwnersInfo(Long stepId) {
        return workflowService.findNextStepOwnersInfo(stepId).getData();
    }

    @Override
    public List<AskForInfoDTO> findAskForInfo(Long exampleid) {
        return workflowService.findAskForInfo(exampleid).getData();
    }

    @Override
    public List<ExampleOwnerBaseDTO> findExampleOwnerInfoList(Long examplestepid) {
        return workflowService.getExampleOwnerInfoList(examplestepid).getData();
    }

    @Override
    public WorkflowExampleStepDetailDTO getExampleStepDetail(Long exampleStepId) {
        return workflowService.findExampleStepDetail(exampleStepId).getData();
    }

    @Override
    public void changeStarter(ChangeStarterDTO changeStarterDTO) {
       workflowService.changeStarter(changeStarterDTO);
    }

    @Override
    public void cancelAssignTask(Long assignId) {
        workflowService.cancelAssignTask(assignId);
    }

    @Override
    public List<Long> findProcessListByEntryList(List<EntryInfoDTO> entrylist) {
        return workflowService.findProcessListByEntryList(entrylist).getData();
    }

    @Override
    public List<WfExampletaskDTO> findExampleTaskByIds(List<Long> ids) {
        return workflowService.findExampleTaskByIds(ids).getData();
    }

    @Override
    public IPage<ExampleDetailDTO> searchExample(IPage page, ExampleQueryDTO query){
        ExampleRemoteQueryDTO param = new ExampleRemoteQueryDTO();
        ObjectUtils.copyObject(query, param);
        Page<ExampleDetailDTO> myPage = new Page<>();
        ObjectUtils.copyObject(page, myPage);
        param.setPage(myPage);
        return workflowService.searchExample(param).getData();
    }

}
