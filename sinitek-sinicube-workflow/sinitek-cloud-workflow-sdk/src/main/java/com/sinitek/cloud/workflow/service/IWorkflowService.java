package com.sinitek.cloud.workflow.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.cloud.workflow.support.dto.*;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.workflow.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@FeignClient(
        name = "${sinicube.workflow.remote.service-name:CLOUD-WORKFLOW}",
        contextId = "workflowService",
        url = "${sinicube.workflow.remote.url:}"
)
@Tag(name = "工作流远程调用暴露出的api")
public interface IWorkflowService {

    @PostMapping(value = "/frontend/api/remote/workflow/start-process")
    @Operation(summary = "发起流程")
    RequestResult<List<WorkflowExampleOwnerDTO>> startProcess(@RequestBody WorkflowStartDTO dto);


    @PostMapping(value = "/frontend/api/remote/workflow/sub-process-step")
    @Operation(summary = "提交流程")
    RequestResult<List<WorkflowExampleOwnerDTO>> subProcessStep(@RequestBody WorkflowSubDTO dto);


    @PostMapping(value = "/frontend/api/remote/workflow/get-entry-list")
    @Operation(summary = "通过流程实例id查询关联实体")
    RequestResult<List<WorkflowEntryDTO>> getEntryListByExampleId(@RequestBody Long exampleId);


    @PostMapping(value = "/frontend/api/remote/workflow/get-example-detail-by-entry")
    @Operation(summary = "通过实体查询相关的流程实例id")
    RequestResult<List<Long>> getExampleDetailByEntry(@RequestBody List<WorkflowEntryDTO> entries);


    @GetMapping(value = "/frontend/api/remote/workflow/process/get-issue-process-by-process-type")
    @Operation(summary = "通过流程类型查询已发布的流程模板信息")
    RequestResult<List<IssueProcessDTO>> getIssueProcess(@RequestParam("processType") Integer processtype);

    @GetMapping(value = "/frontend/api/remote/workflow/get-example-step-detail")
    @Operation(summary = "查询步骤实例详情")
    RequestResult<WorkflowExampleStepDetailDTO> getExampleStepDetail(@RequestParam("exampleStepId") Long exampleStepId);

    @GetMapping(value = "/frontend/api/remote/workflow/get-example-detail-by-id")
    @Operation(summary = "通过流程实例id查询流程实例详情")
    RequestResult<WorkflowExampleDetailDTO> getExampleDetailById(@RequestParam("exampleId") Long exampleId);

    /**
     * 根据步骤处理人实例id查询处理人实例信息
     * @param exampleStepOwnerId
     * @return
     */
    @GetMapping("/frontend/api/remote/workflow/get-example-step-owner-detail")
    @ResponseBody
    @Operation(summary = "根据步骤处理人实例id查询处理人实例信息")
    RequestResult<WfExamplestepownerDTO> getExampleStepOwnerDetailById(@RequestParam("exampleStepOwnerId") Long exampleStepOwnerId);

    @PostMapping(value = "/frontend/api/remote/workflow/save-example-params")
    @Operation(summary = "保存流程运行时参数")
    RequestResult<Integer> resultMapStorage(@RequestParam(value = "exampleId", required = true) Long exampleid,
                                            @RequestParam(value = "exampleStepId", required = true) Long examplestepid,
                                            @RequestParam(value = "exampleOwnerId", required = true) Long exampleownerid,
                                            Map<String,Object> resultMap);

    @Operation(summary = "加载流程运行时参数")
    @PostMapping({"/frontend/api/remote/workflow/load-example-params"})
    RequestResult<Map<String, Object>> resultMapLoad(@RequestParam(value = "exampleId",required = true) Long exampleId,
                                                     @RequestParam(value = "exampleStepId",required = true) Long exampleStepId,
                                                     @RequestParam(value = "exampleOwnerId",required = true) Long exampleOwnerId);

    @Operation(summary = "终止流程")
    @PostMapping(value = "/frontend/api/remote/workflow/example/remote-terminate-process")
    RequestResult<Void> terminateProcess(@RequestParam("exampleId") Long exampleId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-now-example-owner-list-by-exampleid")
    @Operation(summary = "根据流程实例ID，获取流程中所有未被处理的任务")
    RequestResult<List<WorkflowExampleOwnerDTO>> getNowExampleOwnerListByExampleid(@RequestParam("exampleId") Long exampleId);

    /**
     * 获取某个步骤的所有处理人
     * @param exampleStepId 步骤id
     * @return 所有处理人信息
     */
    @PostMapping(value = "/frontend/api/remote/workflow/get-example-owner-list")
    @Operation(summary = "根据流程实例ID，获取流程中所有未被处理的任务")
    RequestResult<List<ExampleOwnerBaseDTO>> getExampleOwnerList(@RequestParam("exampleStepId") Long exampleStepId);

    /**
     * 保存实例信息
     * @param exampleInfo 要保存的实例信息
     * @return 主键
     */
    @PostMapping(value = "/frontend/api/remote/workflow/save-example")
    @Operation(summary = "保存实例信息")
    RequestResult<Long> saveExample(@RequestBody WfExampleDTO exampleInfo);

    /**
     * 查询实例信息
     * @param exampleId 主键
     * @return 主键
     */
    @PostMapping(value = "/frontend/api/remote/workflow/load-example")
    @Operation(summary = "加载流程实例信息")
    RequestResult<WfExampleDTO> loadExample(@RequestParam("exampleId") Long exampleId);

    /**
     * 保存步骤实例信息
     * @param exampleStepInfo 要保存的步骤实例信息
     * @return 主键
     */
    @PostMapping(value = "/frontend/api/remote/workflow/save-example-step")
    @Operation(summary = "保存流程实例信息")
    RequestResult<Long> saveExampleStep(@RequestBody WfExamplestepDTO exampleStepInfo);

    /**
     * 查询步骤实例信息
     * @param exampleStepId 主键
     * @return 主键
     */
    @PostMapping(value = "/frontend/api/remote/workflow/load-example-step")
    @Operation(summary = "加载步骤实例信息")
    RequestResult<WfExamplestepDTO> loadExampleStep(@RequestParam("exampleStepId") Long exampleStepId);

    /**
     * 保存流程任务
     * @param exampletaskDTO
     * @return
     */
    @PostMapping(value = "/frontend/api/remote/workflow/save-example-task")
    @Operation(summary = "查询流程任务")
    RequestResult<Long> saveExampleTask(@RequestBody WfExampletaskDTO exampletaskDTO);

    /**
     * 查询流程任务
     * @param sourceName
     * @param sourceId
     * @return
     */
    @PostMapping(value = "/frontend/api/remote/workflow/get-example-test-by-sourcename-and-sourceid")
    @Operation(summary = "查询流程任务")
    RequestResult<WfExampletaskDTO> getExampleTaskBySourceNameAndSourceId (@RequestParam("sourceName") String sourceName, @RequestParam("sourceId") Long sourceId);

    /**
     * 查询处理人的征求任务信息
     * @param ownerId 处理人实例id，exampleStepOwnerId
     * @return
     */
    @PostMapping(value = "/frontend/api/remote/workflow/get-example-askfor-list-by-ownerid")
    @Operation(summary = "查询处理人的征求任务信息")
    RequestResult<List<WfExampleaskforInfoDTO>> findExampleAskforListByOwnerId(@RequestParam("ownerId") Long ownerId);

    /**
     * 通过主键查询流程
     * @param processId
     * @return
     */
    @PostMapping(value = "/frontend/api/remote/workflow/get-process-by-id")
    @Operation(summary = "通过主键查询流程")
    RequestResult<WfProcessDTO> getProcessInfoById(@RequestParam("processId") Long processId);

    /**
     * 根据步骤实例ID，获取流程中所有该步骤的任务列表
     * @param exampleStepId 步骤实例id
     * @return
     */
    @PostMapping(value = "/frontend/api/remote/workflow/get-example-owner-list-by-example-stepid")
    @Operation(summary = "根据步骤实例ID，获取流程中所有该步骤的任务列表")
    RequestResult<List<ExampleOwnerDTO>> getAllExampleOwnerListByExampleStepid(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/save-example-step-owner")
    @Operation(summary = "保存处理人实例")
    RequestResult<Long> saveExampleStepOwner(@RequestBody WfExamplestepownerDTO owner);

    @PostMapping(value = "/frontend/api/remote/workflow/cancel-assign-task")
    @Operation(summary = "撤销交办任务")
    RequestResult<Long> cancelExampleAssignTask(@RequestBody WfExamplestepownerDTO owner);

    @PostMapping(value = "/frontend/api/remote/workflow/save-example-entry")
    @Operation(summary = "保存流程实例信息")
    RequestResult<Long> saveExampleEntry(@RequestBody WfExampleentryDTO entry);

    @PostMapping(value = "/frontend/api/remote/workflow/save-example-step-link")
    @Operation(summary = "保存流程实例步骤连接信息")
    RequestResult<Long> saveExampleStepLink(@RequestBody WfExamplesteplinkDTO examplesteplinkDTO);

    @PostMapping(value = "/frontend/api/remote/workflow/save-example-para")
    @Operation(summary = "保存流程实例信息参数")
    RequestResult<Long> saveExamplePara(@RequestBody WfExampleparaDTO examplepara);

    @PostMapping(value = "/frontend/api/remote/workflow/load-example-entry")
    @Operation(summary = "加载流程关联实例信息")
    RequestResult<WfExampleentryDTO> loadExampleEntry(@RequestParam("entryId") Long entryId);

    @PostMapping(value = "/frontend/api/remote/workflow/load-example-step-owner")
    @Operation(summary = "加载流程步骤处理人实例信息")
    RequestResult<WfExamplestepownerDTO> loadExampleStepOwner(@RequestParam("exampleStepOwnerId") Long exampleStepOwnerId);

    @PostMapping(value = "/frontend/api/remote/workflow/load-example-para")
    @Operation(summary = "加载流程步骤处理人实例信息")
    RequestResult<WfExampleparaDTO> loadExamplePara(@RequestParam("paraId") Long paraId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-all-now-example-list")
    @Operation(summary = "查找目前没有结束的流程")
    RequestResult<List<NowExampleDTO>> getNowExampleList();

    @PostMapping(value = "/frontend/api/remote/workflow/get-now-example-list-by-param")
    @Operation(summary = "查找目前没有结束的流程通过参数过滤")
    RequestResult<List<NowExampleDTO>> getNowExampleList(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/find-example-owner-info")
    @Operation(summary = "查询当前未完成流程步骤的相关处理人信息")
    RequestResult<List<ExampleOwnerDTO>> findExampleOwnerInfo(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-all-past-example-list")
    @Operation(summary = "查找目前已经结束的流程")
    RequestResult<List<PastExampleDTO>> getPastExampleList();

    @PostMapping(value = "/frontend/api/remote/workflow/get-past-example-list-by-param")
    @Operation(summary = "查找目前已经结束的流程")
    RequestResult<List<PastExampleDTO>> getPastExampleList(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-step-list")
    @Operation(summary = "获取某个实例的所有步骤")
    RequestResult<List<ExampleStepSimpleDTO>> getExampleStepList(@RequestParam("exampleId") Long exampleId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-task")
    @Operation(summary = "获取某个人的所有待处理任务，已处理任务")
    RequestResult<List<ExampleTaskDTO>> getExampleTask(@RequestParam("orgId") String orgid, @RequestParam("type") Integer type);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-task-by-param")
    @Operation(summary = "获取某个人的所有待处理任务，已处理任务")
    RequestResult<List<ExampleTaskDTO>> getExampleTask(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/change-example-s")
    @Operation(summary = "更改流程实例状态，及更改时间（包含修改每一步）")
    RequestResult<Long> changeExampleS(@RequestParam("exampleId") Long exampleId, @RequestParam("status") Integer status);

    @PostMapping(value = "/frontend/api/remote/workflow/get-release-process")
    @Operation(summary = "更改流程实例状态，及更改时间（包含修改每一步）")
    RequestResult<List<ReleaseProcesssDTO>> getReleaseProcesss(@RequestParam("type") Integer type);

    @PostMapping(value = "/frontend/api/remote/workflow/get-process-by-type")
    @Operation(summary = "获取所有已发布的流程,包含发布了但是已经删除了的")
    RequestResult<List<SelectOptionDTO>> getProcessByType(@RequestParam("type") Integer type);

    @PostMapping(value = "/frontend/api/remote/workflow/get-process-step-by-type")
    @Operation(summary = "通过syscode获取这个流程的步骤， （去除开始和结束）")
    RequestResult<List<IssueProcessStepDTO>> getIssueProcessStep(@RequestParam("sysCode") String sysCode);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-entrys")
    @Operation(summary = "获取实例关联的所有实体")
    RequestResult<List<ExampleEntryDTO>> getExampleEntrys(@RequestParam("exampleId") Long exampleId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-agents-list")
    @Operation(summary = "获取某个人的所有相关代理人")
    RequestResult<List<AgentDTO>> getAgentsList(@RequestParam("orgId") String orgid);

    @PostMapping(value = "/frontend/api/remote/workflow/get-live-agents-list")
    @Operation(summary = "获取某个人的所有活性代理人..(可能是有有效的)")
    RequestResult<List<AgentDTO>> getLiveAgentsList(@RequestParam("orgId") String orgid, @RequestParam("startTime") Date starttime, @RequestParam("endTime") Date endtime);

    @PostMapping(value = "/frontend/api/remote/workflow/get-process-list-by-type-and-id")
    @Operation(summary = "根据实体类型，实体ID获取实例ID")
    RequestResult<List<ExampleEntryDTO>> getProcessListByTypeAndId(@RequestParam("entryType") String entryType, @RequestParam("entryId") Long entryId);

    @PostMapping(value = "/frontend/api/remote/workflow/find-para-list-by-name")
    @Operation(summary = "获取参数列表-processlist")
    RequestResult<List<ProcessParamListDTO>> findParaListByName(@RequestParam("name") String name);

    @PostMapping(value = "/frontend/api/remote/workflow/find-all-para-map")
    @Operation(summary = "获取所有参数列表")
    RequestResult<Map<String, String>> findAllParaMap();

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-owner-list-by-example-id")
    @Operation(summary = "根据流程实例ID，获取所有未处理任务列表")
    RequestResult<List<ExampleOwnerDTO>> getExampleOwnerListByExampleId(@RequestParam("exampleId") Long exampleid, @RequestParam("type") Integer type);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-owner-list-by-step-id")
    @Operation(summary = "根据流程实例ID和stepid，获取所有未处理任务列表，（type=1 or 0查询所有未处理的，type=-1查询所有， stepid标识步骤实例id,exampleStepId），（type=2查询所有未处理的，type=-2查询所有，stepid表示processStepId）")
    RequestResult<List<ExampleOwnerDTO>> getExampleOwnerListByStepid(@RequestParam("stepId") Long stepid, @RequestParam("type") Integer type,
                                                                     @RequestParam("exampleId") Long exampleid);

    @PostMapping(value = "/frontend/api/remote/workflow/get-current-example-step-list")
    @Operation(summary = "获取当前流程步骤")
    RequestResult<List<CurrentExampleStepDTO>> getCurrentExampleStepList(@RequestParam("exampleId") Long exampleid);

    @PostMapping(value = "/frontend/api/remote/workflow/get-front-example-step-list")
    @Operation(summary = "获取流程的上一步")
    RequestResult<List<FrontExampleStepDTO>> getFrontExampleStepList(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-my-process-list")
    @Operation(summary = "获取该用户的历史流程")
    RequestResult<List<ExampleDTO>> getMyProcessList(@RequestParam("orgId") String orgId, @RequestParam("processId") Long processid, @RequestParam("processType") Long processtype,
                                                     @RequestParam("sysCode") String syscode);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-owner-opinion")
    @Operation(summary = "获取本流程所有人的处理情况")
    RequestResult<List<ExampleOwnerOpinionDTO>> getExampleOwnerOpinion(@RequestParam("exampleId") Long exampleid, @RequestParam("sort") Integer sort, @RequestParam("type") Integer type);

    @PostMapping(value = "/frontend/api/remote/workflow/get-owner-list-by-example-id")
    @Operation(summary = "查询当前未完成流程步骤的相关处理人信息")
    RequestResult<List<ExampleOwnerDTO>> getOwnerListByExampleid(@RequestParam("exampleId") Long exampleid);

    @PostMapping(value = "/frontend/api/remote/workflow/get-history-source-list")
    @Operation(summary = "获取该人历史处理该类型对象列表")
    RequestResult<List<HistorySourceDTO>> getHistorySourceList(@RequestBody HistorySourceSearchDTO historySourceSearch);

//    @PostMapping(value = "/frontend/api/remote/workflow/get-history-step-map")
//    @Operation(summary = "获取该人历史处理该类型对象列表")
//    public RequestResult<Map<String, Long>> getHistoryStepMap(@RequestParam("type") Integer type);

    @PostMapping(value = "/frontend/api/remote/workflow/get-detail-task-list")
    @Operation(summary = "获取某步骤的详细内容")
    RequestResult<List<DetailTaskDTO>> getDetailTaskList(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-dealer-task-count-list")
    @Operation(summary = "获取处理人及其任务数")
    RequestResult<List<DealerTaskCountDTO>> getDealerTaskCountList();

    @PostMapping(value = "/frontend/api/remote/workflow/find-step-next-list")
    @Operation(summary = "获得接下来的步骤处理人的状态")
    RequestResult<List<Integer>> findStepNextList(@RequestParam("stepId") Long stepId);

    @PostMapping(value = "/frontend/api/remote/workflow/find-next-step-owners-info")
    @Operation(summary = "获得接下来的步骤处理人信息")
    RequestResult<List<NextStepOwnerDTO>> findNextStepOwnersInfo(@RequestParam("stepId") Long stepId);

    @PostMapping(value = "/frontend/api/remote/workflow/find-askfor-info")
    @Operation(summary = "获取流程所有回复转发信息")
    RequestResult<List<AskForInfoDTO>> findAskForInfo(@RequestParam("exampleId") Long exampleId);

    @PostMapping(value = "/frontend/api/remote/workflow/get-example-owner-info-list")
    @Operation(summary = "根据流程步骤id获取流程b步骤处理人信息")
    RequestResult<List<ExampleOwnerBaseDTO>> getExampleOwnerInfoList(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/find-example-step-detail")
    @Operation(summary = "根据流程步骤实例id获取步骤实例详情")
    RequestResult<WorkflowExampleStepDetailDTO> findExampleStepDetail(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-now-step-list")
    @Operation(summary = "获取当前所有待处理的步骤")
    RequestResult<List<StepListShowDTO>> getNowStepList(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-my-initiate-process-list")
    @Operation(summary = "获取可撤回流程")
    RequestResult<List<StepListShowDTO>> getMyInitiateProcessList(
            @RequestBody RecoverProcessSearchDTO recoverProcessSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-my-recover-process-list")
    @Operation(summary = "获取已撤回流程")
    RequestResult<List<StepListShowDTO>> getMyRecoverProcessList(
            @RequestBody RecoverProcessSearchDTO recoverProcessSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/support/add-example-step-owner")
    @Operation(summary = "添加处理人")
    RequestResult<Long> addExampleStepOwner(@RequestParam("exampleStepId") Long exampleStepId, @RequestParam("orgId") String orgId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-my-tasks")
    @Operation(summary = "获取某人参与的未结束任务")
    RequestResult<List<ExampleTaskDTO>> getMyTasks(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-tasks-takepart")
    @Operation(summary = "获取某人的参与的任务")
    RequestResult<List<ExampleTaskDTO>> getTasksTakepart(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/support/recover-process-to-special-step")
    @Operation(summary = "撤回到指定步骤")
    RequestResult<List<WorkflowExampleOwnerDTO>> recoverProcessToSpecialStep(@RequestBody RecoverProcessDTO recoverProcess, @RequestParam("targetStepId") Long targetStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/recover-example-task")
    @Operation(summary = "撤回已处理的任务-反悔操作")
    RequestResult<Void> recoverExampleTask(@RequestParam("exampleId") Long assignId, @RequestParam("exampleStepId") Long exampleStepId, @RequestParam("exampleOwnerId") Long exampleOwnerId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/recover-example-assign")
    @Operation(summary = "撤回已交办的任务")
    RequestResult<Void> recoverExampleAssign(@RequestParam("assignId") Long assignId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/recover-process-to-pre-step")
    @Operation(summary = "撤回到上一步")
    RequestResult<List<WorkflowExampleOwnerDTO>> recoverProcessToPreStep(@RequestBody RecoverProcessDTO recoverProcess);

    @PostMapping(value = "/frontend/api/remote/workflow/support/goto-process-start")
    @Operation(summary = "撤回到开始步骤")
    RequestResult<List<WorkflowExampleOwnerDTO>> gotoProcessStart(@RequestBody RecoverProcessDTO recoverProcess);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-detail-cadidate-list-by-process-step-id")
    @Operation(summary = "获取详细的候选人列表")
    RequestResult<List<DetailCadidateDTO>> getDetailCadidateListByProcessStepid(@RequestParam("processStepId") Long processStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-detail-cadidate-list-by-example-step-id")
    @Operation(summary = "获取详细的候选人列表")
    RequestResult<List<DetailCadidateDTO>> getDetailCadidateListByExampleStepid(@RequestParam("exampleStepId") Long exampleStepId);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-release-process-and-null-list")
    @Operation(summary = "获取所有已发布并且无CODE的流程")
    RequestResult<List<ReleaseProcesssDTO>> getReleaseProcessAndNullList();

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-release-process-and-code-list")
    @Operation(summary = "获取所有已发布并且有CODE的流程")
    RequestResult<List<ReleaseProcesssDTO>> getReleaseProcessAndCodeList();

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-release-process-list")
    @Operation(summary = "获取所有已发布的流程")
    RequestResult<List<ReleaseProcesssDTO>> getReleaseProcessList();

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-finished-tasks")
    @Operation(summary = "获取某人的所有已处理任务")
    RequestResult<List<ExampleTaskDTO>> getFinishedTasks(@RequestBody ExampleSearchDTO exampleSearc);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-unfinish-tasks")
    @Operation(summary = "获取某人的所有待处理任务")
    RequestResult<List<ExampleTaskDTO>> getUnfinishTasks(@RequestBody ExampleSearchDTO exampleSearch);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-main-workflow")
    @Operation(summary = "获取整个流程实例详细")
    RequestResult<List<MainWorkFlowExampleDTO>> getMainWorkflow(@RequestParam("exampleId") Long exampleid);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-now-workflow")
    @Operation(summary = "获取当前所有未完成的流程")
    RequestResult<List<NowWorkflowDTO>> getNowWorkflow();

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-process-status")
    @Operation(summary = "根据exampleid判断流程的状态")
    RequestResult<Integer> getProcessStatus(@RequestParam("exampleid") Long exampleid);

    @PostMapping(value = "/frontend/api/remote/workflow/support/get-processs-by-syscode")
    @Operation(summary = "根据SYSCODE，获取流程")
    RequestResult<ProcessDTO> getProcessBySyscode(@RequestParam("syscode") String syscode);

    @PostMapping(value = "/frontend/api/remote/workflow/support/find-example-entrys")
    @Operation(summary = "根据exampleid，获取实例实体列表")
    RequestResult<List<ExampleEntryDTO>> findExampleEntryList(@RequestParam("exampleid") Long exampleid);

    @PostMapping(value = "/frontend/api/remote/workflow/support/find-process-list-by-entrys")
    @Operation(summary = "根据实例列表获得流程列表")
    RequestResult<List<Long>> findProcessListByEntryList(@RequestParam("entrylist") List<EntryInfoDTO> entrylist);

    @PostMapping(value = "/frontend/api/remote/workflow/support/find-example-task-by-ids")
    @Operation(summary = "根据taskid集合，获取任务列表")
    public RequestResult<List<WfExampletaskDTO>> findExampleTaskByIds(@RequestBody List<Long> ids);

    @PostMapping(value = "/frontend/api/remote/workflow/launch/save-launch-component-data")
    @Operation(summary = "保存发起组件数据")
    public RequestResult<Void> saveLaunchComponentData(@RequestBody WorkflowStartParamDTO workflowStartParam);

    @PostMapping(value = "/frontend/api/remote/workflow/launch/start-process")
    @Operation(summary = "使用发起组件发起流程")
    public RequestResult<List<WorkflowExampleOwnerDTO>> startProcess(@RequestBody WorkflowStartParamDTO workflowStartParam);

    @PostMapping(value = "/frontend/api/remote/workflow/launch/submit-approval")
    @Operation(summary = "通过、提交、驳回公用方法")
    public RequestResult<List<WorkflowExampleOwnerDTO>> submitApproval(@RequestBody WorkflowSubParamDTO workflowSubDtoParam);


    @PostMapping("/frontend/api/remote/workflow/example/change-starter")
    @Operation(summary = "变更发起人")
   RequestResult<Void> changeStarter(ChangeStarterDTO changeStarterDTO);


    @PostMapping("/frontend/api/remote/workflow/example/cancel-assign-task")
    @Operation(summary = "撤销交办给某人的任务")
   RequestResult<Void> cancelAssignTask(@RequestParam("assignId") Long assignId);

    @PostMapping(value = "/frontend/api/remote/workflow/example/search-example")
    @Operation(summary = "分页查询流程实例数据")
    RequestResult<Page<ExampleDetailDTO>> searchExample(@RequestBody ExampleRemoteQueryDTO query);
}

