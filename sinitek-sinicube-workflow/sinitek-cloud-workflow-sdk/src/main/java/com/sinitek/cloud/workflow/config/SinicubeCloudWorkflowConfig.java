package com.sinitek.cloud.workflow.config;

import com.sinitek.cloud.workflow.service.impl.CloudWorkflowAppServiceImpl;
import com.sinitek.cloud.workflow.service.impl.CloudWorkflowApprovalServiceImpl;
import com.sinitek.cloud.workflow.service.impl.CloudWorkflowSupportServiceImpl;
import com.sinitek.sirm.workflow.service.IWorkflowAppService;
import com.sinitek.sirm.workflow.service.IWorkflowApprovalService;
import com.sinitek.sirm.workflow.service.IWorkflowSupportService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@ConditionalOnProperty(prefix = "sinicube.cloud", name = "type", havingValue = "MICROSERVICE", matchIfMissing = true)
@Configuration
public class SinicubeCloudWorkflowConfig {

    @Bean
    @ConditionalOnMissingBean(IWorkflowAppService.class)
    public IWorkflowAppService workflowAppService() {
        return new CloudWorkflowAppServiceImpl();
    }


    @Bean
    @ConditionalOnMissingBean(IWorkflowSupportService.class)
    public IWorkflowSupportService workflowSupportService() {
        return new CloudWorkflowSupportServiceImpl();
    }

    @Bean
    @ConditionalOnMissingBean(IWorkflowApprovalService.class)
    public IWorkflowApprovalService workflowApprovalService() {
        return new CloudWorkflowApprovalServiceImpl();
    }

}
