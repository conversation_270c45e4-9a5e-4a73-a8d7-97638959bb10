package com.sinitek.sirm.common.message.dto;

import com.sinitek.sirm.common.message.enumerate.MessageContentTypeEnum;
import com.sinitek.sirm.common.message.support.BaseMessageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 消息发送类型数据对象
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Data
@Schema(description = "消息引擎-消息发送类型数据对象")
public class MessageTypeDTO {

    @Schema(description = "消息对象类型")
    private int type;


    @Schema(description = "类型名称的国际化编码")
    private String i18nCode;


    @Schema(description = "排序")
    private int sort;

    /**
     * 内容文本格式
     * @see MessageContentTypeEnum
     *
     */
    @Schema(description = "内容文本类型")
    private int contentType;

    @Schema(description = "消息数据对象class")
    private Class<BaseMessageDTO> entity;


}
