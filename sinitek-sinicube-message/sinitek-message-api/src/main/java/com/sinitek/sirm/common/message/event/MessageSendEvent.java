package com.sinitek.sirm.common.message.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.sinitek.sirm.common.event.annotation.EventDefinition;
import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.common.message.dto.MessageEventSourceDTO;

/**
 * 消息发送事件
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@EventDefinition(type = "SiniCube", module = "消息引擎", name = "消息发送事件", brief = "发送消息时触发该事件，事件源数据为发送的消息（邮件、短信、站内信等）")
public class MessageSendEvent extends SiniCubeEvent<MessageEventSourceDTO> {

    @JsonCreator
    public MessageSendEvent(MessageEventSourceDTO source) {
        super(source);
    }

}
