package com.sinitek.sirm.common.message.annotation;

import com.sinitek.sirm.common.message.enumerate.MessageContentTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 消息对象注解
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MessageEntity {

    /**
     * 消息对象类型，1：邮件、2：短信、4：站内信、8：企业微信。 以此类推，规则为2的n次方
     */
    int type();

    /**
     * 排序，默认int的最大值
     */
    int sort() default Integer.MAX_VALUE;

    /**
     * i18n编码，用于国际化显示 消息对象类型的名称
     */
    String i18nCode();

    /**
     * 模版内容类型
     */
    MessageContentTypeEnum contentType() default MessageContentTypeEnum.PLAIN_TEXT;



}
