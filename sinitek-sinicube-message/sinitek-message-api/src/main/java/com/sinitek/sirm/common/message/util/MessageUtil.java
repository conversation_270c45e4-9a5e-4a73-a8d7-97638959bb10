package com.sinitek.sirm.common.message.util;

import com.sinitek.sirm.common.message.constant.MessageConstant;
import lombok.extern.slf4j.Slf4j;


/**
 * 消息发送工具类
 *
 * <AUTHOR>
 * @date 2021/6/10
 */
@Slf4j
public class MessageUtil {

    /**
     * 是否发送邮件
     * @param sendMode
     * @return
     */
    public static boolean hasMailRemind(long sendMode){
        return (MessageConstant.SENDMODE_EMAIL & sendMode) != 0;
    }

    /**
     * 是否发送站内信
     * @param sendMode
     * @return
     */
    public static boolean hasSysRemind(long sendMode){
        return (MessageConstant.SENDMODE_SYSREMINDER & sendMode) != 0;
    }


    /**
     * 添加邮件发送方式 （计算发送方式的值）
     * @param states    原发送方式值
     * @return  新发送方式值
     */
    public static int addMailRemind(int states){
        return states | MessageConstant.SENDMODE_EMAIL;
    }



    /**
     * 添加短信发送方式 （计算发送方式的值）
     * @param states    原发送方式值
     * @return  新发送方式值
     */
    public static int addSmsRemind(int states){
        return states | MessageConstant.SENDMODE_SMS;
    }



    /**
     * 添加站内信发送方式 （计算发送方式的值）
     * @param states     原发送方式值
     * @return  新发送方式值
     */
    public static int addSysRemind(int states){
        return states | MessageConstant.SENDMODE_SYSREMINDER;
    }



    /**
     * 添加企业微信发送方式 （计算发送方式的值）
     * @param states     原发送方式值
     * @return  新发送方式值
     */
    public static int addWxWorkRemind(int states){
        return states | MessageConstant.SENDMODE_WXWORK;
    }





}
