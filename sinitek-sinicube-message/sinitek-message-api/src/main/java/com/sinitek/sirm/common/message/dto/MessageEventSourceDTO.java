package com.sinitek.sirm.common.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
@Schema(description = "消息发送事件的源数据对象")
public class MessageEventSourceDTO {


    @Schema(description = "消息类型")
    private String type;

    @Schema(description = "发送消息的应用")
    private String appName;

    @Schema(description = "消息的class类")
    private String entity;

    @Schema(description = "消息对象的json")
    private String msgStr;

}
