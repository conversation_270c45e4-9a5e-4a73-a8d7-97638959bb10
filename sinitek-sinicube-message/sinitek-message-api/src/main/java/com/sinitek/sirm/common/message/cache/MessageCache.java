package com.sinitek.sirm.common.message.cache;

import com.sinitek.sirm.common.message.dto.MessageTypeDTO;
import com.sinitek.sirm.common.message.support.BaseMessageDTO;
import com.sinitek.sirm.common.message.support.IMessageHandler;
import com.sinitek.sirm.common.message.support.IMessageReceiverFilter;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息实体缓存
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Slf4j
public class MessageCache {

    private static MessageCache instance = new MessageCache();

    private MessageCache(){

    }


    private List<MessageTypeDTO> msgTypeList = new ArrayList<>();

    @SuppressWarnings({ "rawtypes" })
    private Map<Class<? extends BaseMessageDTO>, IMessageHandler> messageHandlerMap = new HashMap<>();

    private Map<Class<? extends BaseMessageDTO>, IMessageReceiverFilter<? extends BaseMessageDTO>> messageReceiverFilterMap = new HashMap<>();


    public static void initHandler(Map<Class<? extends BaseMessageDTO>, IMessageHandler<? extends BaseMessageDTO>> map){
        instance.messageHandlerMap.putAll(map);
    }

    public static void initType(List<MessageTypeDTO> list){
        instance.msgTypeList.addAll(list);
    }


    public static Map<Integer, String> getSendModeMap(){
        Map<Integer, String> sendModeMap = new HashMap<>();

        instance.msgTypeList.stream().forEach(type -> sendModeMap.put(type.getType(), RequestResult.getMessage(type.getI18nCode())));

        return sendModeMap;
    }

    public static List<MessageTypeDTO> findSendModes(){
        ArrayList<MessageTypeDTO> messageTypeDTOS = new ArrayList<>();
        instance.msgTypeList.forEach(item-> {
            MessageTypeDTO messageTypeDTO = new MessageTypeDTO();
            BeanUtils.copyProperties(item,messageTypeDTO);
            messageTypeDTO.setI18nCode(RequestResult.getMessage(item.getI18nCode()));
            messageTypeDTOS.add(messageTypeDTO);
        });
        return messageTypeDTOS;
    }


    public static Map<Class<BaseMessageDTO>, Integer> getSendModeEntityAndKeyMap(){
        Map<Class<BaseMessageDTO>, Integer> sendModeMap = new HashMap<>();

        instance.msgTypeList.stream().forEach(type -> sendModeMap.put(type.getEntity(), type.getType()));

        return sendModeMap;
    }

    public static Map<Integer, Class<BaseMessageDTO>> getSendModeKeyAndEntityMap(){
        Map<Integer, Class<BaseMessageDTO>> sendModeMap = new HashMap<>();

        instance.msgTypeList.stream().forEach(type -> sendModeMap.put(type.getType(), type.getEntity()));

        return sendModeMap;
    }

    @SuppressWarnings({ "rawtypes" })
    public static Map<Class<? extends BaseMessageDTO>, IMessageHandler> getMessageHandlerMap() {
        return instance.messageHandlerMap;
    }

    public static void initMessageReceiverFilter(Map<Class<? extends BaseMessageDTO>, IMessageReceiverFilter<? extends BaseMessageDTO>> map) {
        instance.messageReceiverFilterMap.putAll(map);
    }

    @SuppressWarnings({ "squid:S1452" })
    public static Map<Class<? extends BaseMessageDTO>, IMessageReceiverFilter<? extends BaseMessageDTO>> getMessageReceiverFilterMap() {
        return instance.messageReceiverFilterMap;
    }

}
