package com.sinitek.sirm.common.message.constant;

/**
 * 消息系列公共常量
 *
 * <AUTHOR>
 * @date 2020/04/27
 */
public class MessageConstant {




    /**
     * 系统内消息
     */
    final public static String SYSTEM_INNER_MESSAGE = "0402353";

    /**
     * 邮件
     */
    final public static String EMAIL = "0402354";

    /**
     * 短信
     */
    final public static String SMS = "0402355";

    /**
     * 企业微信
     */
    final public static String WX_WORK = "0402365";



    /**
     * 主键key
     */
    public static final String OBJID_KEY = "objid";

    /**
     * 发送方式邮件
     */
    public static final int SENDMODE_EMAIL = 1;
    /**
     * 短消息
     */
    public static final int SENDMODE_SMS = 2;
    /**
     * 系统提醒
     */
    public static final int SENDMODE_SYSREMINDER = 4;

    /**
     * 手机推送
     */
    public static final int SENDMODE_MOBILE = 16;

    /**
     * 企业微信
     */
    public static final int SENDMODE_WXWORK = 8;

    /**
     * 系统消息
     */
    public static final  String  SENDMODE_SYSREMINDER_STR = "1";

    /**
     * 邮件
     */
    public static final  String  SENDMODE_EMAIL_STR = "2";

    /**
     * 短信
     */
    public static final String  SENDMODE_SMS_STR = "3";

    /**
     * 支持短信发送
     */
    public static final String  SUPPORT_FLAG = "1";

    /**
     * 消息重要度为3
     */
    public static final int IMPORTANT_LEVEL = 3;

    public static final Integer TO_RECEIVER = 1;

    public static final Integer CC_RECEIVER = 0;

    /**
     * URL以http://开头
     */
    public static final String URL_START_WITH = "http://";

    /**
     * URL以https://开头
     */
    public static final String URL_START_WITHS = "https://";



    /**
     * 发送消息的通道名
     */
    public static final String MESSAGE_API_SEND_MESSAGE_TOPIC = "message-api-send-message";


    /**
     * 监听消息发送结果的通道名
     */
    public static final String MESSAGE_API_HANDLE_MESSAGE_TOPIC = "message-api-handle-message";

    /**
     *  消息数量缓存
     */
    public static final String MESSAGE_COUNT_CACHE = "com.sinitek.sirm.message.cache.messageCountCache";

    /**
     * 消息队列sourceEntity
     */
    public static final String SIRM_SENDMESSAGE_ENTITYNAME = "SIRM_SEND_MESSAGE";

}
