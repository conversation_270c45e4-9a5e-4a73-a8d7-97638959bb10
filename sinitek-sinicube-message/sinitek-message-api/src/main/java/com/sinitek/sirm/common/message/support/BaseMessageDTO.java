package com.sinitek.sirm.common.message.support;


import com.sinitek.sirm.common.message.dto.MessageReceiverDTO;
import com.sinitek.sirm.common.message.dto.MsgAttachmentDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 基础消息数据对象，定义消息对象的顶级接口类, 需要实现具体的消息类型
 *
 * <AUTHOR>
 * @date 2021/5/19
 */
@Schema(description = "消息引擎-基础消息数据接口类")
@Data
public class BaseMessageDTO implements java.io.Serializable {

    @Schema(description = "发件人id")
    private String senderId;

    @Schema(description = "标题")
    public String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "收件人")
    private List<MessageReceiverDTO> receivers;

    @Schema(description = "消息重要程度")
    private Integer importantLevel;

    @Schema(description = "附件")
    private List<MsgAttachmentDTO> attachments = new ArrayList<>();


    @Schema(description = "来源记录id")
    private Long sourceId = 0L;

    @Schema(description = "来源记录名称")
    private String sourceEntity;

    @Schema(description = "发送时间")
    private Date sendTime;

    @Schema(description ="发送配置")
    private transient Map<String,Object> sendConfigMap;

}
