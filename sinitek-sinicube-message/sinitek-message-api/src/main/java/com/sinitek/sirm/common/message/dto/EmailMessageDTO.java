package com.sinitek.sirm.common.message.dto;

import com.sinitek.sirm.common.message.annotation.MessageEntity;
import com.sinitek.sirm.common.message.constant.MessageConstant;
import com.sinitek.sirm.common.message.enumerate.MessageContentTypeEnum;
import com.sinitek.sirm.common.message.support.BaseMessageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 邮件消息的数据对象
 *
 * <AUTHOR>
 * @date 2021/5/19
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@Schema(description = "消息引擎-邮件消息数据对象")
@MessageEntity(type = 1, i18nCode = MessageConstant.EMAIL, sort = 1, contentType = MessageContentTypeEnum.RICH_TEXT)
public class EmailMessageDTO extends BaseMessageDTO {

    @Schema(description = "发件邮箱")
    private String mailFrom;
}
