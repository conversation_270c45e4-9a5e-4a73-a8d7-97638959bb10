package com.sinitek.sirm.common.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 消息数据的附件对象
 *
 * <AUTHOR>
 * @date 2021/5/25
 */
@Data
@Schema(description = "消息引擎-消息数据的附件对象")
public class MsgAttachmentDTO implements java.io.Serializable{

    @Schema(description = "文件名")
    private String name;


    @Schema(description = "文件对象")
    private String filePath;

    @Schema(description = "持久化附件id")
    private Long attachmentId;

}
