package com.sinitek.sirm.common.message.support;

import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
public interface IMessageReceiverFilter<T extends BaseMessageDTO> {

    /**
     * 消息收件人过滤器
     * @param list 所有收件人信息集合
     * @return 自定义过滤之后的收件人信息集合
     */
    List<MessageReceiverTemplateDTO> filter(List<MessageReceiverTemplateDTO> list);
}
