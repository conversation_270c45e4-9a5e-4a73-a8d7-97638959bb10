package com.sinitek.sirm.common.message.support;

import com.sinitek.sirm.common.message.dto.MessageReceiverDTO;

import java.util.List;

/**
 * 消息处理接口类
 *
 * <AUTHOR>
 * @date 2021/5/24
 */
public interface IMessageHandler<T extends BaseMessageDTO> {

    /**
     * 处理消息
     *
     * @param msg   消息内容
     * @return 发送失败的收件人集合，全部发送成功可以返回null或者空集合
     */
    List<MessageReceiverDTO> send(T msg);

}
