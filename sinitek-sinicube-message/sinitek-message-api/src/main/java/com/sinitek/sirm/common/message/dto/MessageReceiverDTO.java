package com.sinitek.sirm.common.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 邮件接收人
 *
 * <AUTHOR>
 * @date 2021/5/19
 */
@Data
@Schema(description = "消息引擎-消息接收人数据对象")
public class MessageReceiverDTO implements java.io.Serializable{

    @Schema(description = "接收对象")
    private String receiver;

    @Schema(description = "接收对象名称")
    private String receiverName;

    @Schema(description = "收件人类型, 邮件消息时有效,'To':邮件收件人，'Cc':邮件抄送人， 'Bcc':邮件密送人")
    private String type;

    @Schema(description = "错误原因")
    private String reason;

    @Schema(description = "员工id")
    private String empId;

}
