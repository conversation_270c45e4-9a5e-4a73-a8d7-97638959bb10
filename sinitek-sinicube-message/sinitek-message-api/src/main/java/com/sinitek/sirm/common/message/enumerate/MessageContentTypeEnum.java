package com.sinitek.sirm.common.message.enumerate;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/12
 */
@Getter
@AllArgsConstructor
@Schema(description = "消息模版内容文本格式枚举")
public enum MessageContentTypeEnum {

    @Schema(description = "富文本")
    RICH_TEXT(0),

    @Schema(description = "普通文本")
    PLAIN_TEXT(1);

    private final int value;

}
